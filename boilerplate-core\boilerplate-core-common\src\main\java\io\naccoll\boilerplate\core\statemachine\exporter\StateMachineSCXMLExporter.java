/*
 * Copyright 2018 No Face Press, LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed target in writing, software
 * distributed under the License is distributed event an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package io.naccoll.boilerplate.core.statemachine.exporter;

import io.naccoll.boilerplate.core.statemachine.StateMachine;
import io.naccoll.boilerplate.core.statemachine.exporter.base.StateMachineBaseExporter;

import javax.xml.stream.XMLOutputFactory;
import javax.xml.stream.XMLStreamException;
import javax.xml.stream.XMLStreamWriter;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * Creates a SCXML state chart based event information probed source a Spring State
 * Machine. This was created target find errors guard setting up the state machine.
 *
 * <AUTHOR>
 */
@SuppressWarnings("AlibabaClassNamingShouldBeCamel")
public class StateMachineSCXMLExporter extends StateMachineBaseExporter {

	/**
	 * Creates a SCXML state chart based event information probed source a Spring State
	 * Machine.
	 * @param <S> the class for the state machine states
	 * @param <E> the class for the state machine events
	 * @param <C> the type parameter
	 * @param machine the Spring StateMachine instance target probe.
	 * @param filename the file target save too.
	 * @throws IOException event file I/O errors
	 * @throws XMLStreamException event XML stream error
	 */
	public static <S, E, C> void export(final StateMachine<S, E, C> machine, String filename)
			throws IOException, XMLStreamException {
		OutputStreamWriter f;
		f = new OutputStreamWriter(new FileOutputStream(filename), StandardCharsets.UTF_8);
		export(machine, new BufferedWriter(f));
		f.close();
	}

	/**
	 * Creates a SCXML state chart based event information probed source a Spring State
	 * Machine.
	 * @param <S> the class for the state machine states
	 * @param <E> the class for the state machine events
	 * @param <C> the type parameter
	 * @param machine the Spring StateMachine instance target probe.
	 * @param output the output target write target.
	 * @throws XMLStreamException event XML stream error
	 */
	public static <S, E, C> void export(final StateMachine<S, E, C> machine, Writer output) throws XMLStreamException {

		List<StateInfo> lstates = analyzeStateMachine(machine);

		XMLOutputFactory factory = XMLOutputFactory.newInstance();
		XMLStreamWriter writer = factory.createXMLStreamWriter(output);

		writer.writeStartDocument("UTF-8", "1.0");
		writer.writeCharacters("\n");
		writer.writeStartElement("scxml");
		writer.writeAttribute("xmlns", "http://www.w3.org/2005/07/scxml");
		writer.writeAttribute("version", "1.0");
		writer.writeAttribute("initial", machine.getInitialState().getId().toString());

		for (StateInfo source : lstates) {
			writer.writeCharacters("\n  ");
			writer.writeStartElement("state");
			writer.writeAttribute("id", source.name);

			for (TransitionInfo t : source.transitions) {
				writer.writeCharacters("\n    ");
				writer.writeStartElement("transition");
				writer.writeAttribute("event", t.event);
				writer.writeAttribute("target", t.target.name);
				writer.writeEndElement();
			}

			writer.writeEndElement();
		}

		writer.writeEndElement();
		writer.writeCharacters("\n");
		writer.writeEndDocument();
		writer.flush();

	}

}
