package io.naccoll.boilerplate.messenger.mail;

import io.naccoll.boilerplate.messenger.mail.exception.BuildMessageException;
import io.naccoll.boilerplate.messenger.mail.exception.SendMessageException;
import jakarta.annotation.Resource;
import jakarta.mail.internet.InternetAddress;
import jakarta.mail.internet.MimeMessage;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.mail.MailProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.core.io.FileSystemResource;
import org.springframework.mail.MailException;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.io.File;

/**
 * 邮件服务类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@Validated
@EnableConfigurationProperties(MailProperties.class)
public class MailService {

	/**
	 * 邮件配置属性
	 */
	@Resource
	MailProperties mailProperties;

	@Resource
	private JavaMailSender javaMailSender;

	/**
	 * 发送邮件
	 * @param sendMailCommand 发送邮件命令对象
	 * @throws SendMessageException 发送邮件异常
	 */
	public void sendMail(@Valid SendMailCommand sendMailCommand) throws SendMessageException {
		MimeMessage message = javaMailSender.createMimeMessage();
		try {
			boolean emptyInlines = CollectionUtils.isEmpty(sendMailCommand.getInlines());
			boolean emptyAttachs = CollectionUtils.isEmpty(sendMailCommand.getAttachments());
			boolean multipar = !(emptyAttachs && emptyInlines);
			MimeMessageHelper helper = new MimeMessageHelper(message, multipar);
			helper.setSubject(sendMailCommand.getSubject());
			helper.setFrom(mailProperties.getUsername());
			for (String recipient : sendMailCommand.getRecipients()) {
				helper.addTo(new InternetAddress(recipient));
			}
			helper.setText(sendMailCommand.getText(), sendMailCommand.getHtmlText());
			if (!emptyInlines) {
				for (File file : sendMailCommand.getInlines()) {
					helper.addInline(file.getName(), new FileSystemResource(file));
				}
			}
			if (!emptyAttachs) {
				for (File file : sendMailCommand.getAttachments()) {
					helper.addAttachment(file.getName(), new FileSystemResource(file));
				}
			}
			javaMailSender.send(message);
		}
		catch (MailException e) {
			log.info("Sending Mail Failed: {}", e);
			throw new SendMessageException(e.getMessage());
		}
		catch (Exception e) {
			log.info("Error when build MimeMessage: {}", e);
			throw new BuildMessageException(e.getMessage());
		}

	}

}
