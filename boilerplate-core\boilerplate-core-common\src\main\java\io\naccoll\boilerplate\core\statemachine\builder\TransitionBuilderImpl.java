package io.naccoll.boilerplate.core.statemachine.builder;

import io.naccoll.boilerplate.core.statemachine.Action;
import io.naccoll.boilerplate.core.statemachine.Guard;
import io.naccoll.boilerplate.core.statemachine.State;
import io.naccoll.boilerplate.core.statemachine.Transition;
import io.naccoll.boilerplate.core.statemachine.impl.StateHelper;
import io.naccoll.boilerplate.core.statemachine.impl.TransitionType;

import java.util.Map;

/**
 * TransitionBuilderImpl
 *
 * @param <S> the type parameter
 * @param <E> the type parameter
 * @param <C> the type parameter
 * <AUTHOR>
 * @date 2020 -02-07 10:20 PM
 */
class TransitionBuilderImpl<S, E, C> implements ExternalTransitionBuilder<S, E, C>, InternalTransitionBuilder<S, E, C>,
		Source<S, E, C>, On<S, E, C>, Target<S, E, C> {

	/**
	 * The State map.
	 */
	final Map<S, State<S, E, C>> stateMap;

	/**
	 * The Transition type.
	 */
	final TransitionType transitionType;

	/**
	 * The Target.
	 */
	protected State<S, E, C> target;

	private State<S, E, C> source;

	private Transition<S, E, C> transition;

	/**
	 * Instantiates a new Transition builder.
	 * @param stateMap the state map
	 * @param transitionType the transition type
	 */
	public TransitionBuilderImpl(Map<S, State<S, E, C>> stateMap, TransitionType transitionType) {
		this.stateMap = stateMap;
		this.transitionType = transitionType;
	}

	@Override
	public Source<S, E, C> source(S stateId) {
		source = StateHelper.getState(stateMap, stateId);
		return this;
	}

	@Override
	public Target<S, E, C> target(S stateId) {
		target = StateHelper.getState(stateMap, stateId);
		return this;
	}

	@Override
	public Target<S, E, C> within(S stateId) {
		source = target = StateHelper.getState(stateMap, stateId);
		return this;
	}

	@Override
	public When<S, E, C> guard(Guard<C> guard) {
		transition.setCondition(guard);
		return this;
	}

	@Override
	public On<S, E, C> event(E event) {
		transition = source.addTransition(event, target, transitionType);
		return this;
	}

	@Override
	public void action(Action<S, E, C>... action) {
		transition.setActions(action);
	}

}
