package io.naccoll.boilerplate.cms.interfaces.api.platform;

import io.naccoll.boilerplate.cms.constant.CmsApiConstant;
import io.naccoll.boilerplate.cms.convert.CmsConvert;
import io.naccoll.boilerplate.cms.dto.CmsArticleCreateCommand;
import io.naccoll.boilerplate.cms.dto.CmsArticleDto;
import io.naccoll.boilerplate.cms.dto.CmsArticleQueryCondition;
import io.naccoll.boilerplate.cms.dto.CmsArticleUpdateCommand;
import io.naccoll.boilerplate.cms.service.CmsArticleQueryService;
import io.naccoll.boilerplate.cms.service.CmsArticleService;
import io.naccoll.boilerplate.core.audit.operate.OperateLog;
import io.naccoll.boilerplate.core.constant.MediaTypeConstant;
import io.naccoll.boilerplate.core.exception.ClientException;
import io.naccoll.boilerplate.oss.OssServiceHelper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文章管理平台API接口
 *
 * <AUTHOR>
 */
@Tag(name = "文章管理")
@RestController
@RequestMapping(CmsApiConstant.PlatformApiV1.ARTICLE)
public class CmsPlatformApiV1ArticleApi {

	@Resource
	private CmsArticleService cmsArticleService;

	@Resource
	private CmsArticleQueryService cmsArticleQueryService;

	@Resource
	private CmsConvert cmsConvert;

	@Resource
	private OssServiceHelper ossServiceHelper;

	/**
	 * 根据文章ID获取单篇文章信息
	 * @param articleId 文章ID
	 * @return 文章DTO
	 */
	@PreAuthorize("hasPermission(0L,'GLOBAL','cms/article:read')")
	@Operation(summary = "查询单篇文章")
	@GetMapping("/{articleId}")
	public CmsArticleDto getOneArticle(@PathVariable Long articleId) {
		return cmsConvert.convertCmsArticleDto(cmsArticleQueryService.findByIdNotNull(articleId));
	}

	/**
	 * 分页查询文章列表
	 * @param pageable 分页参数
	 * @param condition 查询条件
	 * @return 文章DTO分页结果
	 */
	@PreAuthorize("hasPermission(0L,'GLOBAL','cms/article:read')")
	@Operation(summary = "分页查询文章")
	@GetMapping("/page")
	public Page<CmsArticleDto> queryArticle(Pageable pageable, CmsArticleQueryCondition condition) {
		return cmsConvert.convertCmsArticleDtoPage(cmsArticleQueryService.page(pageable, condition));
	}

	/**
	 * 创建新的文章
	 * @param command 文章创建命令
	 * @param file 文章头图文件
	 * @return 新创建的文章DTO
	 */
	@PreAuthorize("hasPermission(0L,'GLOBAL','cms/article:add')")
	@Operation(summary = "创建文章")
	@PostMapping(value = "", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
	public CmsArticleDto createArticle(CmsArticleCreateCommand command,
			@RequestPart(value = "headImgFile", required = false) MultipartFile file) {
		if (file != null && file.getContentType() != null) {
			if (!MediaType.parseMediaType(file.getContentType()).isCompatibleWith(MediaTypeConstant.IMAGE)) {
				throw new ClientException("请上传图片的文件");
			}
			command.setHeadImage(ossServiceHelper.uploadPublicMultipartFile("cms/head-img", file));
		}
		return cmsConvert.convertCmsArticleDto(cmsArticleService.create(command));
	}

	/**
	 * 更新现有文章信息
	 * @param command 文章更新命令
	 * @param file 文章头图文件
	 * @return 更新后的文章DTO
	 */
	@PreAuthorize("hasPermission(0L,'GLOBAL','cms/article:edit')")
	@Operation(summary = "修改文章")
	@PutMapping(value = "", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
	public CmsArticleDto updateArticle(CmsArticleUpdateCommand command,
			@RequestPart(value = "headImgFile", required = false) MultipartFile file) {
		if (file != null && file.getContentType() != null) {
			if (!MediaType.parseMediaType(file.getContentType()).isCompatibleWith(MediaTypeConstant.IMAGE)) {
				throw new ClientException("请上传图片的文件");
			}
			command.setHeadImage(ossServiceHelper.uploadPublicMultipartFile("cms/head-img", file));
		}
		return cmsConvert.convertCmsArticleDto(cmsArticleService.update(command));
	}

	/**
	 * 上传文章内容资源文件
	 * @param file 资源文件
	 * @return 资源文件URL
	 */
	@PreAuthorize("hasPermission(0L,'GLOBAL','cms/article:add') OR hasPermission(0L,'GLOBAL','cms/article:edit')")
	@Operation(summary = "上传文章内容资源")
	@PostMapping(value = "/content-resource", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
	@OperateLog(value = "上传文章内容资源")
	public ResponseEntity<String> uploadArticleResource(@RequestPart MultipartFile file) {
		if (file == null || file.getContentType() == null) {
			throw new ClientException("请上传文件");
		}
		MediaType fileMediaType = MediaType.parseMediaType(file.getContentType());
		if (!fileMediaType.isCompatibleWith(MediaTypeConstant.IMAGE)
				&& !fileMediaType.isCompatibleWith(MediaTypeConstant.VIDEO)) {
			throw new ClientException("请上传图片或者视频类型的文件");
		}
		String originUrl = ossServiceHelper.uploadPublicMultipartFile("cms/content-resource", file);
		return new ResponseEntity<>(ossServiceHelper.parseNoProtocolUrl(originUrl), HttpStatus.CREATED);
	}

	/**
	 * 删除指定的文章
	 * @param articleId 文章ID
	 * @return 响应状态
	 */
	@PreAuthorize("hasPermission(0L,'GLOBAL','cms/article:del')")
	@Operation(summary = "删除文章")
	@DeleteMapping("/{articleId}")
	public ResponseEntity<Void> deleteArticle(@PathVariable Long articleId) {
		cmsArticleService.delete(articleId);
		return new ResponseEntity<>(HttpStatus.RESET_CONTENT);
	}

}
