package io.naccoll.boilerplate.core.persistence.dao;

import cn.hutool.extra.spring.SpringUtil;
import io.naccoll.boilerplate.core.audit.AuditContextManager;
import io.naccoll.boilerplate.core.audit.AuditUser;
import io.naccoll.boilerplate.core.persistence.model.AbstractEntity;
import jakarta.persistence.EntityManager;
import org.springframework.data.jpa.repository.support.JpaEntityInformation;
import org.springframework.data.jpa.repository.support.JpaEntityInformationSupport;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * 基础数据访问实现类，支持软删除功能
 *
 * @param <T> 实体类型
 * @param <ID> 主键类型
 * <AUTHOR>
 */
public class BaseDaoImpl<T, ID> extends SimpleJpaRepository<T, ID> implements BaseDao<T, ID> {

	private AuditContextManager auditContextManager;

	private final EntityManager entityManager;

	private final JpaEntityInformation entityInformation;

	public BaseDaoImpl(Class<T> domainClass, EntityManager entityManager) {
		this(JpaEntityInformationSupport.getEntityInformation(domainClass, entityManager), entityManager);
	}

	public BaseDaoImpl(JpaEntityInformation<T, ?> entityInformation, EntityManager entityManager) {
		super(entityInformation, entityManager);
		this.entityInformation = entityInformation;
		this.entityManager = entityManager;
	}

	/**
	 * 重写删除方法，对AbstractEntity实现软删除
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delete(T entity) {
		performSoftDelete(entity);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void deleteAllInBatch() {
		if (entityInformation.getJavaType().isAssignableFrom(AbstractEntity.class)) {
			super.deleteAll();
		}
		else {
			super.deleteAllInBatch();
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void deleteAllInBatch(Iterable<T> entities) {
		if (entityInformation.getJavaType().isAssignableFrom(AbstractEntity.class)) {
			super.deleteAll(entities);
		}
		else {
			super.deleteAllInBatch(entities);
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void deleteAllByIdInBatch(Iterable<ID> ids) {
		if (entityInformation.getJavaType().isAssignableFrom(AbstractEntity.class)) {
			super.deleteAllById(ids);
		}
		else {
			super.deleteAllByIdInBatch(ids);
		}
	}

	/**
	 * 执行软删除操作
	 */
	public void performSoftDelete(T entity) {
		if (entity instanceof AbstractEntity<?>abstractEntity) {
			if (auditContextManager == null) {
				auditContextManager = SpringUtil.getBean(AuditContextManager.class);
			}
			// 更新审计信息
			AuditUser current = auditContextManager.getAuditUser();
			abstractEntity.setLastModifiedUserId(current.getId());
			abstractEntity.setLastModifiedUserName(current.getName());
			abstractEntity.setLastModifiedDate(new Date());
			abstractEntity.setIsDeleted(true);
			save(entity);
		}
		else {
			super.delete(entity);
		}

	}

}
