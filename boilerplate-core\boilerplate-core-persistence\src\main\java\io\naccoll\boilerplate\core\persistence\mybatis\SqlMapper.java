package io.naccoll.boilerplate.core.persistence.mybatis;

import org.apache.ibatis.builder.StaticSqlSource;
import org.apache.ibatis.exceptions.TooManyResultsException;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.ResultMap;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.mapping.SqlSource;
import org.apache.ibatis.scripting.LanguageDriver;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.session.SqlSession;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * MyBatis执行sql工具，在写SQL的时候建议使用参数形式的可以是${}或#{}
 *
 * 不建议将参数直接拼到字符串中，当大量这么使用的时候由于缓存MappedStatement而占用更多的内存
 *
 * <AUTHOR>
 */
public class SqlMapper {

	private final MSUtils msUtils;

	private final SqlSession sqlSession;

	/**
	 * 构造方法，默认缓存MappedStatement
	 * @param sqlSession SqlSession实例
	 */
	public SqlMapper(SqlSession sqlSession) {
		this.sqlSession = sqlSession;
		this.msUtils = new MSUtils(sqlSession.getConfiguration());
	}

	/**
	 * 获取List中最多只有一个的数据
	 * @param list List结果
	 * @param <T> 泛型类型
	 * @return 泛型类型T的实例
	 */
	private <T> T getOne(List<T> list) {
		if (list.size() == 1) {
			return list.getFirst();
		}
		else if (list.size() > 1) {
			throw new TooManyResultsException(
					"Expected one result (or null) to be returned by selectOne(), but found: " + list.size());
		}
		else {
			return null;
		}
	}

	/**
	 * 查询返回一个结果，多个结果时抛出异常
	 * @param sql 执行的sql
	 * @return Map<String, Object>类型的结果
	 */
	public Map<String, Object> selectOne(String sql) {
		List<Map<String, Object>> list = selectList(sql);
		return getOne(list);
	}

	/**
	 * 查询返回一个结果，多个结果时抛出异常
	 * @param sql 执行的sql
	 * @param value 参数
	 * @return Map<String, Object>类型的结果
	 */
	public Map<String, Object> selectOne(String sql, Object value) {
		List<Map<String, Object>> list = selectList(sql, value);
		return getOne(list);
	}

	/**
	 * 查询返回一个结果，多个结果时抛出异常
	 * @param sql 执行的sql
	 * @param resultType 返回的结果类型
	 * @param <T> 泛型类型
	 * @return 泛型类型T的实例
	 */
	public <T> T selectOne(String sql, Class<T> resultType) {
		List<T> list = selectList(sql, resultType);
		return getOne(list);
	}

	/**
	 * 查询返回一个结果，多个结果时抛出异常
	 * @param sql 执行的sql
	 * @param value 参数
	 * @param resultType 返回的结果类型
	 * @param <T> 泛型类型
	 * @return 泛型类型T的实例
	 */
	public <T> T selectOne(String sql, Object value, Class<T> resultType) {
		List<T> list = selectList(sql, value, resultType);
		return getOne(list);
	}

	/**
	 * 查询返回List<Map<String, Object>>
	 * @param sql 执行的sql
	 * @return List<Map<String, Object>>类型的结果
	 */
	public List<Map<String, Object>> selectList(String sql) {
		String msId = msUtils.select(sql);
		return sqlSession.selectList(msId);
	}

	/**
	 * 查询返回List<Map<String, Object>>
	 * @param sql 执行的sql
	 * @param value 参数
	 * @return List<Map<String, Object>>类型的结果
	 */
	public List<Map<String, Object>> selectList(String sql, Object value) {
		Class<?> parameterType = value != null ? value.getClass() : null;
		String msId = msUtils.selectDynamic(sql, parameterType);
		return sqlSession.selectList(msId, value);
	}

	/**
	 * 查询返回List<Map<String, Object>>
	 * @param sql 执行的sql
	 * @param value 参数
	 * @return List<List<Map<String, Object>>>类型的结果
	 */
	public List<List<Map<String, Object>>> selectListMultipleResult(String sql, Object value) {
		Class<?> parameterType = value != null ? value.getClass() : null;
		String msId = msUtils.selectDynamicMultipleResult(sql, parameterType);
		List<List<Map<String, Object>>> tmpResult = sqlSession.selectList(msId, value);
		if (tmpResult.isEmpty()) {
			List<List<Map<String, Object>>> result = new ArrayList<>();
			result.add(new ArrayList<>());
			return result;
		}
		else {
			if (tmpResult.getFirst() instanceof Map) {
				List<Map<String, Object>> tmp = new ArrayList<>();
				for (Object i : tmpResult) {
					if (i instanceof Map<?, ?>map) {
						tmp.add((Map<String, Object>) map);
					}
				}
				List<List<Map<String, Object>>> result = new ArrayList<>();
				result.add(tmp);
				return result;
			}
			else {
				return tmpResult;
			}
		}
	}

	/**
	 * 查询返回指定的结果类型
	 * @param sql 执行的sql
	 * @param resultType 返回的结果类型
	 * @param <T> 泛型类型
	 * @return List<T>类型的结果
	 */
	public <T> List<T> selectList(String sql, Class<T> resultType) {
		String msId;
		if (resultType == null) {
			msId = msUtils.select(sql);
		}
		else {
			msId = msUtils.select(sql, resultType);
		}
		return sqlSession.selectList(msId);
	}

	/**
	 * 查询返回指定的结果类型
	 * @param sql 执行的sql
	 * @param value 参数
	 * @param resultType 返回的结果类型
	 * @param <T> 泛型类型
	 * @return List<T>类型的结果
	 */
	public <T> List<T> selectList(String sql, Object value, Class<T> resultType) {
		String msId;
		Class<?> parameterType = value != null ? value.getClass() : null;
		if (resultType == null) {
			msId = msUtils.selectDynamic(sql, parameterType);
		}
		else {
			msId = msUtils.selectDynamic(sql, parameterType, resultType);
		}
		return sqlSession.selectList(msId, value);
	}

	/**
	 * 插入数据
	 * @param sql 执行的sql
	 * @return 影响的行数
	 */
	public int insert(String sql) {
		String msId = msUtils.insert(sql);
		return sqlSession.insert(msId);
	}

	/**
	 * 插入数据
	 * @param sql 执行的sql
	 * @param value 参数
	 * @return 影响的行数
	 */
	public int insert(String sql, Object value) {
		Class<?> parameterType = value != null ? value.getClass() : null;
		String msId = msUtils.insertDynamic(sql, parameterType);
		return sqlSession.insert(msId, value);
	}

	/**
	 * 更新数据
	 * @param sql 执行的sql
	 * @return 影响的行数
	 */
	public int update(String sql) {
		String msId = msUtils.update(sql);
		return sqlSession.update(msId);
	}

	/**
	 * 更新数据
	 * @param sql 执行的sql
	 * @param value 参数
	 * @return 影响的行数
	 */
	public int update(String sql, Object value) {
		Class<?> parameterType = value != null ? value.getClass() : null;
		String msId = msUtils.updateDynamic(sql, parameterType);
		return sqlSession.update(msId, value);
	}

	/**
	 * 删除数据
	 * @param sql 执行的sql
	 * @return 影响的行数
	 */
	public int delete(String sql) {
		String msId = msUtils.delete(sql);
		return sqlSession.delete(msId);
	}

	/**
	 * 删除数据
	 * @param sql 执行的sql
	 * @param value 参数
	 * @return 影响的行数
	 */
	public int delete(String sql, Object value) {
		Class<?> parameterType = value != null ? value.getClass() : null;
		String msId = msUtils.deleteDynamic(sql, parameterType);
		return sqlSession.delete(msId, value);
	}

	@SuppressWarnings("AlibabaClassNamingShouldBeCamel")
	private static class MSUtils {

		private final Configuration configuration;

		private final LanguageDriver languageDriver;

		/**
		 * MSUtils构造方法
		 * @param configuration MyBatis配置实例
		 */
		private MSUtils(Configuration configuration) {
			this.configuration = configuration;
			languageDriver = configuration.getDefaultScriptingLanguageInstance();
		}

		/**
		 * 创建MSID
		 * @param sql 执行的sql
		 * @param sqlCommandType 执行的sqlCommandType
		 * @return 生成的MSID
		 */
		private String newMsId(String sql, SqlCommandType sqlCommandType) {
			return sqlCommandType.toString() + "." + sql.hashCode();
		}

		/**
		 * 是否已经存在该ID
		 * @param msId MappedStatement的ID
		 * @return 是否存在该ID
		 */
		private boolean hasMappedStatement(String msId) {
			return configuration.hasStatement(msId, false);
		}

		/**
		 * 创建一个查询的MS
		 * @param msId MappedStatement的ID
		 * @param sqlSource 执行的sqlSource
		 * @param resultType 返回的结果类型
		 */
		private void newSelectMappedStatement(String msId, SqlSource sqlSource, final Class<?> resultType) {
			ArrayList<ResultMap> list = new ArrayList<>();
			list.add(new ResultMap.Builder(configuration, "defaultResultMap", resultType, new ArrayList<>(0)).build());
			MappedStatement ms = new MappedStatement.Builder(configuration, msId, sqlSource, SqlCommandType.SELECT)
				.resultMaps(list)
				.build();
			// 缓存
			configuration.addMappedStatement(ms);
		}

		/**
		 * 创建一个查询的MS
		 * @param msId MappedStatement的ID
		 * @param sqlSource 执行的sqlSource
		 * @param resultType 返回的结果类型
		 * @param resetSetNum 结果集数量
		 */
		private void newSelectMappedStatementMultipleResult(String msId, SqlSource sqlSource, final Class<?> resultType,
				int resetSetNum) {
			ArrayList<ResultMap> list = new ArrayList<>();
			List<String> resultSetNames = new LinkedList<>();
			for (int i = 0; i < resetSetNum; i++) {
				String resultSetName = "a" + i;
				resultSetNames.add(resultSetName);
				list.add(new ResultMap.Builder(configuration, resultSetName, resultType, new ArrayList<>(0)).build());
			}
			MappedStatement ms = new MappedStatement.Builder(configuration, msId, sqlSource, SqlCommandType.SELECT)
				.resultMaps(list)
				.resultSets(String.join(",", resultSetNames))
				.build();
			// 缓存
			configuration.addMappedStatement(ms);
		}

		/**
		 * 创建一个简单的MS
		 * @param msId MappedStatement的ID
		 * @param sqlSource 执行的sqlSource
		 * @param sqlCommandType 执行的sqlCommandType
		 */
		private void newUpdateMappedStatement(String msId, SqlSource sqlSource, SqlCommandType sqlCommandType) {
			ArrayList<ResultMap> list = new ArrayList<>();
			list.add(new ResultMap.Builder(configuration, "defaultResultMap", int.class, new ArrayList<>(0)).build());
			MappedStatement ms = new MappedStatement.Builder(configuration, msId, sqlSource, sqlCommandType)
				.resultMaps(list)
				.build();
			// 缓存
			configuration.addMappedStatement(ms);
		}

		/**
		 * 创建一个简单的查询MS
		 * @param sql 执行的sql
		 * @return 生成的MSID
		 */
		private String select(String sql) {
			String msId = newMsId(sql, SqlCommandType.SELECT);
			if (hasMappedStatement(msId)) {
				return msId;
			}
			StaticSqlSource sqlSource = new StaticSqlSource(configuration, sql);
			newSelectMappedStatement(msId, sqlSource, LinkedHashMap.class);
			return msId;
		}

		/**
		 * 创建一个动态查询MS
		 * @param sql 执行的sql
		 * @param parameterType 参数类型
		 * @return 生成的MSID
		 */
		private String selectDynamic(String sql, Class<?> parameterType) {
			String msId = newMsId(sql + parameterType, SqlCommandType.SELECT);
			if (!hasMappedStatement(msId)) {
				SqlSource sqlSource = languageDriver.createSqlSource(configuration, sql, parameterType);
				newSelectMappedStatement(msId, sqlSource, LinkedHashMap.class);
			}
			return msId;
		}

		/**
		 * 创建一个动态查询MS（支持多个结果集）
		 * @param sql 执行的sql
		 * @param parameterType 参数类型
		 * @return 生成的MSID
		 */
		private String selectDynamicMultipleResult(String sql, Class<?> parameterType) {
			String msId = newMsId(sql + parameterType, SqlCommandType.SELECT);
			if (!hasMappedStatement(msId)) {
				SqlSource sqlSource = languageDriver.createSqlSource(configuration, sql, parameterType);
				int resultSetNum = (int) Arrays.stream(sql.split(";")).filter(StringUtils::hasText).count();
				resultSetNum = resultSetNum == 0 ? 1 : resultSetNum;
				newSelectMappedStatementMultipleResult(msId, sqlSource, LinkedHashMap.class, resultSetNum);
			}
			return msId;
		}

		/**
		 * 创建一个指定结果类型的查询MS
		 * @param sql 执行的sql
		 * @param resultType 返回的结果类型
		 * @return 生成的MSID
		 */
		private String select(String sql, Class<?> resultType) {
			String msId = newMsId(resultType + sql, SqlCommandType.SELECT);
			if (!hasMappedStatement(msId)) {
				StaticSqlSource sqlSource = new StaticSqlSource(configuration, sql);
				newSelectMappedStatement(msId, sqlSource, resultType);
			}
			return msId;
		}

		/**
		 * 创建一个动态查询MS（指定结果类型）
		 * @param sql 执行的sql
		 * @param parameterType 参数类型
		 * @param resultType 返回的结果类型
		 * @return 生成的MSID
		 */
		private String selectDynamic(String sql, Class<?> parameterType, Class<?> resultType) {
			String msId = newMsId(resultType + sql + parameterType, SqlCommandType.SELECT);
			if (!hasMappedStatement(msId)) {
				SqlSource sqlSource = languageDriver.createSqlSource(configuration, sql, parameterType);
				newSelectMappedStatement(msId, sqlSource, resultType);
			}
			return msId;
		}

		/**
		 * 创建一个插入MS
		 * @param sql 执行的sql
		 * @return 生成的MSID
		 */
		private String insert(String sql) {
			String msId = newMsId(sql, SqlCommandType.INSERT);
			if (!hasMappedStatement(msId)) {
				StaticSqlSource sqlSource = new StaticSqlSource(configuration, sql);
				newUpdateMappedStatement(msId, sqlSource, SqlCommandType.INSERT);
			}
			return msId;
		}

		/**
		 * 创建一个动态插入MS
		 * @param sql 执行的sql
		 * @param parameterType 参数类型
		 * @return 生成的MSID
		 */
		private String insertDynamic(String sql, Class<?> parameterType) {
			String msId = newMsId(sql + parameterType, SqlCommandType.INSERT);
			if (!hasMappedStatement(msId)) {
				SqlSource sqlSource = languageDriver.createSqlSource(configuration, sql, parameterType);
				newUpdateMappedStatement(msId, sqlSource, SqlCommandType.INSERT);
			}
			return msId;
		}

		/**
		 * 创建一个更新MS
		 * @param sql 执行的sql
		 * @return 生成的MSID
		 */
		private String update(String sql) {
			String msId = newMsId(sql, SqlCommandType.UPDATE);
			if (!hasMappedStatement(msId)) {
				StaticSqlSource sqlSource = new StaticSqlSource(configuration, sql);
				newUpdateMappedStatement(msId, sqlSource, SqlCommandType.UPDATE);
			}
			return msId;
		}

		/**
		 * 创建一个动态更新MS
		 * @param sql 执行的sql
		 * @param parameterType 参数类型
		 * @return 生成的MSID
		 */
		private String updateDynamic(String sql, Class<?> parameterType) {
			String msId = newMsId(sql + parameterType, SqlCommandType.UPDATE);
			if (!hasMappedStatement(msId)) {
				SqlSource sqlSource = languageDriver.createSqlSource(configuration, sql, parameterType);
				newUpdateMappedStatement(msId, sqlSource, SqlCommandType.UPDATE);
			}
			return msId;
		}

		/**
		 * 创建一个删除MS
		 * @param sql 执行的sql
		 * @return 生成的MSID
		 */
		private String delete(String sql) {
			String msId = newMsId(sql, SqlCommandType.DELETE);
			if (!hasMappedStatement(msId)) {
				StaticSqlSource sqlSource = new StaticSqlSource(configuration, sql);
				newUpdateMappedStatement(msId, sqlSource, SqlCommandType.DELETE);
			}
			return msId;
		}

		/**
		 * 创建一个动态删除MS
		 * @param sql 执行的sql
		 * @param parameterType 参数类型
		 * @return 生成的MSID
		 */
		private String deleteDynamic(String sql, Class<?> parameterType) {
			String msId = newMsId(sql + parameterType, SqlCommandType.DELETE);
			if (!hasMappedStatement(msId)) {
				SqlSource sqlSource = languageDriver.createSqlSource(configuration, sql, parameterType);
				newUpdateMappedStatement(msId, sqlSource, SqlCommandType.DELETE);
			}
			return msId;
		}

	}

}
