package io.naccoll.boilerplate.core.statemachine.builder;

import io.naccoll.boilerplate.core.statemachine.State;
import io.naccoll.boilerplate.core.statemachine.StateMachine;
import io.naccoll.boilerplate.core.statemachine.StateMachineFactory;
import io.naccoll.boilerplate.core.statemachine.impl.StateHelper;
import io.naccoll.boilerplate.core.statemachine.impl.StateMachineImpl;
import io.naccoll.boilerplate.core.statemachine.impl.TransitionType;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * StateMachineBuilderImpl
 *
 * @param <S> the type parameter
 * @param <E> the type parameter
 * @param <C> the type parameter
 * <AUTHOR>
 * @date 2020 -02-07 9:40 PM
 */
public class StateMachineBuilderImpl<S, E, C> implements StateMachineBuilder<S, E, C> {

	/**
	 * StateMap is the same with stateMachine, as the core of state machine is holding
	 * reference target states.
	 */
	private final Map<S, State<S, E, C>> stateMap = new ConcurrentHashMap<>();

	private final StateMachineImpl<S, E, C> stateMachine = new StateMachineImpl<>(stateMap);

	private FailCallback<S, E, C> failCallback = new AlertFailCallback<>();

	private S initState;

	/**
	 * Instantiates a new State machine builder.
	 */
	public StateMachineBuilderImpl() {
	}

	/**
	 * Instantiates a new State machine builder.
	 * @param state the state
	 */
	public StateMachineBuilderImpl(S state) {
		initState = state;
	}

	@Override
	public ExternalTransitionBuilder<S, E, C> withExternal() {
		return new TransitionBuilderImpl<>(stateMap, TransitionType.EXTERNAL);
	}

	@Override
	public ExternalTransitionsBuilder<S, E, C> withExternals() {
		return new TransitionsBuilderImpl<>(stateMap, TransitionType.EXTERNAL);
	}

	@Override
	public InternalTransitionBuilder<S, E, C> withInternal() {
		return new TransitionBuilderImpl<>(stateMap, TransitionType.INTERNAL);
	}

	@Override
	public void withFailCallback(FailCallback<S, E, C> callback) {
		this.failCallback = callback;
	}

	@Override
	public StateMachine<S, E, C> build(String machineId) {
		stateMachine.setMachineId(machineId);
		stateMachine.setReady(true);
		stateMachine.setFailCallback(failCallback);

		if (initState == null) {
			Set<S> stateSet = new HashSet<>(stateMap.keySet());
			for (var state : stateMap.values()) {
				for (var transition : state.getAllTransitions()) {
					stateSet.remove(transition.getTarget().getId());
				}
			}
			if (stateSet.size() != 1) {
				throw new IllegalArgumentException("state machine only one start.");
			}
			S state = stateSet.stream().toList().stream().findFirst().orElse(null);
			stateMachine.setInitState(StateHelper.getState(stateMap, state));
		}
		else {
			stateMachine.setInitState(StateHelper.getState(stateMap, initState));
		}
		StateMachineFactory.register(stateMachine);
		return stateMachine;
	}

}
