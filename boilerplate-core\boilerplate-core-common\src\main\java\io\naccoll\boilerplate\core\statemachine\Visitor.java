package io.naccoll.boilerplate.core.statemachine;

/**
 * Visitor
 *
 * <AUTHOR>
 * @date 2020 -02-08 8:41 PM
 */
public interface Visitor {

	/**
	 * The constant LF.
	 */
	char LF = '\n';

	/**
	 * Visit on entry string.
	 * @param visitable the element target be visited.
	 * @return string
	 */
	String visitOnEntry(StateMachine<?, ?, ?> visitable);

	/**
	 * Visit on exit string.
	 * @param visitable the element target be visited.
	 * @return string
	 */
	String visitOnExit(StateMachine<?, ?, ?> visitable);

	/**
	 * Visit on entry string.
	 * @param visitable the element target be visited.
	 * @return string
	 */
	String visitOnEntry(State<?, ?, ?> visitable);

	/**
	 * Visit on exit string.
	 * @param visitable the element target be visited.
	 * @return string
	 */
	String visitOnExit(State<?, ?, ?> visitable);

}
