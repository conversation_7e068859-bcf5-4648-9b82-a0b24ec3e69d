package io.naccoll.boilerplate.sys.dao;

import io.naccoll.boilerplate.core.persistence.dao.BaseDao;
import io.naccoll.boilerplate.core.persistence.jpa.specification.Specifications;
import io.naccoll.boilerplate.sys.constant.SysCacheName;
import io.naccoll.boilerplate.sys.dto.SysSqlQueryCondition;
import io.naccoll.boilerplate.sys.model.SysSqlPo;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * 动态SQL集数据库访问层
 *
 * <AUTHOR>
 */
@SuppressWarnings("AlibabaAbstractMethodOrInterfaceMethodMustUseJavadoc")
public interface SysSqlDao extends BaseDao<SysSqlPo, Long> {

	@Override
	@Caching(put = { @CachePut(value = SysCacheName.SQL_ID, key = "#result.id"),
			@CachePut(value = SysCacheName.SQL_CODE, key = "#result.code") })
	<S extends SysSqlPo> S save(S entity);

	@Override
	@Caching(put = { @CachePut(value = SysCacheName.SQL_ID, key = "#result.id"),
			@CachePut(value = SysCacheName.SQL_CODE, key = "#result.code") })
	<S extends SysSqlPo> S saveAndFlush(S s);

	@Override
	@Caching(evict = { @CacheEvict(value = SysCacheName.SQL_ID, allEntries = true),
			@CacheEvict(value = SysCacheName.SQL_CODE, allEntries = true) })
	<S extends SysSqlPo> List<S> saveAll(Iterable<S> iterable);

	@Override
	@Caching(evict = { @CacheEvict(value = SysCacheName.SQL_ID, allEntries = true),
			@CacheEvict(value = SysCacheName.SQL_CODE, allEntries = true) })
	<S extends SysSqlPo> List<S> saveAllAndFlush(Iterable<S> entities);

	@Override
	@Caching(evict = { @CacheEvict(value = SysCacheName.SQL_CODE, allEntries = true),
			@CacheEvict(value = SysCacheName.SQL_ID, key = "#p0.id", condition = "#p0.id != null"), })
	void delete(SysSqlPo entity);

	@Override
	@Caching(evict = { @CacheEvict(value = SysCacheName.SQL_CODE, allEntries = true),
			@CacheEvict(value = SysCacheName.SQL_ID, allEntries = true) })
	void deleteAll();

	@Override
	@Caching(evict = { @CacheEvict(value = SysCacheName.SQL_CODE, allEntries = true),
			@CacheEvict(value = SysCacheName.SQL_ID, allEntries = true) })
	void deleteAllById(Iterable<? extends Long> longs);

	@Override
	@Caching(evict = { @CacheEvict(value = SysCacheName.SQL_CODE, allEntries = true),
			@CacheEvict(value = SysCacheName.SQL_ID, allEntries = true) })
	void deleteAllByIdInBatch(Iterable<Long> longs);

	@Override
	@Caching(evict = { @CacheEvict(value = SysCacheName.SQL_CODE, allEntries = true),
			@CacheEvict(value = SysCacheName.SQL_ID, allEntries = true) })
	void deleteAll(Iterable<? extends SysSqlPo> entities);

	@Override
	@Caching(evict = { @CacheEvict(value = SysCacheName.SQL_CODE, allEntries = true),
			@CacheEvict(value = SysCacheName.SQL_ID, allEntries = true) })
	void deleteAllInBatch(Iterable<SysSqlPo> entities);

	@Override
	@Caching(evict = { @CacheEvict(value = SysCacheName.SQL_CODE, allEntries = true),
			@CacheEvict(value = SysCacheName.SQL_ID, allEntries = true) })
	void deleteAllInBatch();

	@Override
	@Caching(evict = { @CacheEvict(value = SysCacheName.SQL_CODE, allEntries = true),
			@CacheEvict(value = SysCacheName.SQL_ID, key = "#p0"), })
	void deleteById(Long aLong);

	@Override
	List<SysSqlPo> findAll();

	@Override
	@Cacheable(value = SysCacheName.SQL_ID, key = "#p0")
	Optional<SysSqlPo> findById(Long aLong);

	/**
	 * 使用code查询动态SQL集
	 * @param code 动态SQL标识
	 * @return 动态SQL集
	 */
	@Cacheable(value = SysCacheName.SQL_CODE, key = "#p0")
	SysSqlPo findFirstByCode(String code);

	/**
	 * 查询SysSql列表
	 * @param condition SysSql查询条件
	 * @return SysSql列表
	 */
	default List<SysSqlPo> findAll(SysSqlQueryCondition condition) {
		Specification<SysSqlPo> spec = buildSpecification(condition);
		return findAll(spec);
	}

	/**
	 * 查询SysSql分页
	 * @param condition SysSql查询条件
	 * @param pageable 分页条件
	 * @return SysSql分页
	 */
	default Page<SysSqlPo> page(SysSqlQueryCondition condition, Pageable pageable) {
		Specification<SysSqlPo> spec = buildSpecification(condition);
		return findAll(spec, pageable);
	}

	default Specification<SysSqlPo> buildSpecification(SysSqlQueryCondition condition) {
		return Specifications.builder(SysSqlPo.class)
			.contain(condition.getCode() != null, SysSqlPo::getCode, condition.getCode());
	}

	List<SysSqlPo> findByCodeIn(Collection<String> codes);

}
