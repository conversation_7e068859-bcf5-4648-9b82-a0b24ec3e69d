redis.replicate_commands()

local water_key = KEYS[1]
local last_leak_time_key = KEYS[2]

local rate = tonumber(ARGV[1])
local capacity = tonumber(ARGV[2])
local now = tonumber(ARGV[3])
if now == nil then
  now = redis.call('TIME')[1]
end
local requested = tonumber(ARGV[4])

local fill_time = capacity / rate
local ttl = math.floor(fill_time * 2)

local last_water = tonumber(redis.call("get", water_key))
if last_water == nil then
  last_water = 0
end

local last_leak_time = tonumber(redis.call("get", last_leak_time_key))
if last_leak_time == nil then
  last_leak_time = now
end

local delta = math.max(0, now - last_leak_time)
local leaked_water = delta * rate
local current_water = math.max(0, last_water - leaked_water)

local allowed = (current_water + requested) <= capacity
local new_water = current_water
local allowed_num = 0
if allowed then
  new_water = current_water + requested
  allowed_num = 1
end

if ttl > 0 then
  redis.call("setex", water_key, ttl, new_water)
  redis.call("setex", last_leak_time_key, ttl, now)
end

return { allowed_num, new_water }