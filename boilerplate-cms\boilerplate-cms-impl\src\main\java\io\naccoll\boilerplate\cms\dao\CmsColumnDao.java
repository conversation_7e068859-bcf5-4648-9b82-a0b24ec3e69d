package io.naccoll.boilerplate.cms.dao;

import io.naccoll.boilerplate.cms.constant.CmsCacheName;
import io.naccoll.boilerplate.cms.dto.CmsColumnQueryCondition;
import io.naccoll.boilerplate.cms.model.CmsColumnPo;
import io.naccoll.boilerplate.core.persistence.dao.BaseDao;
import io.naccoll.boilerplate.core.persistence.jpa.specification.Specifications;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Optional;

/**
 * The interface Cms column dao.
 *
 * <AUTHOR>
 */
@SuppressWarnings("AlibabaAbstractMethodOrInterfaceMethodMustUseJavadoc")
public interface CmsColumnDao extends BaseDao<CmsColumnPo, Long> {

	@Override
	@Caching(evict = { @CacheEvict(value = CmsCacheName.COLUMN_ALL, allEntries = true) },
			put = { @CachePut(value = CmsCacheName.COLUMN_ID, key = "#result.id"), })
	<S extends CmsColumnPo> S save(S entity);

	@Override
	@Caching(evict = { @CacheEvict(value = CmsCacheName.COLUMN_ALL, allEntries = true) },
			put = { @CachePut(value = CmsCacheName.COLUMN_ID, key = "#result.id"), })
	<S extends CmsColumnPo> S saveAndFlush(S s);

	@Override
	@Caching(evict = { @CacheEvict(value = CmsCacheName.COLUMN_ID, allEntries = true),
			@CacheEvict(value = CmsCacheName.COLUMN_ALL, allEntries = true) })
	<S extends CmsColumnPo> List<S> saveAll(Iterable<S> iterable);

	@Override
	@Caching(evict = { @CacheEvict(value = CmsCacheName.COLUMN_ID, allEntries = true),
			@CacheEvict(value = CmsCacheName.COLUMN_ALL, allEntries = true) })
	<S extends CmsColumnPo> List<S> saveAllAndFlush(Iterable<S> entities);

	@Override
	@Caching(evict = { @CacheEvict(value = CmsCacheName.COLUMN_ID, key = "#p0.id", condition = "#p0.id != null"),
			@CacheEvict(value = CmsCacheName.COLUMN_ALL, allEntries = true) })
	void delete(CmsColumnPo entity);

	@Override
	@Caching(evict = { @CacheEvict(value = CmsCacheName.COLUMN_ID, allEntries = true),
			@CacheEvict(value = CmsCacheName.COLUMN_ALL, allEntries = true) })
	void deleteAll();

	@Override
	@Caching(evict = { @CacheEvict(value = CmsCacheName.COLUMN_ID, allEntries = true),
			@CacheEvict(value = CmsCacheName.COLUMN_ALL, allEntries = true) })
	void deleteAll(Iterable<? extends CmsColumnPo> entities);

	@Override
	@Caching(evict = { @CacheEvict(value = CmsCacheName.COLUMN_ID, allEntries = true),
			@CacheEvict(value = CmsCacheName.COLUMN_ALL, allEntries = true) })
	void deleteAllInBatch(Iterable<CmsColumnPo> entities);

	@Override
	@Caching(evict = { @CacheEvict(value = CmsCacheName.COLUMN_ID, allEntries = true),
			@CacheEvict(value = CmsCacheName.COLUMN_ALL, allEntries = true) })
	void deleteAllInBatch();

	@Override
	@Caching(evict = { @CacheEvict(value = CmsCacheName.COLUMN_ID, allEntries = true),
			@CacheEvict(value = CmsCacheName.COLUMN_ALL, allEntries = true) })
	void deleteAllById(Iterable<? extends Long> longs);

	@Override
	@Caching(evict = { @CacheEvict(value = CmsCacheName.COLUMN_ID, allEntries = true),
			@CacheEvict(value = CmsCacheName.COLUMN_ALL, allEntries = true) })
	void deleteAllByIdInBatch(Iterable<Long> longs);

	@Override
	@Caching(evict = { @CacheEvict(value = CmsCacheName.COLUMN_ID, key = "#p0"),
			@CacheEvict(value = CmsCacheName.COLUMN_ALL, allEntries = true) })
	void deleteById(Long aLong);

	@Override
	@Cacheable(value = CmsCacheName.COLUMN_ID, key = "#p0")
	Optional<CmsColumnPo> findById(Long aLong);

	@Override
	@Cacheable(value = CmsCacheName.COLUMN_ALL)
	List<CmsColumnPo> findAll();

	/**
	 * 根据条件查询栏目列表
	 * @param condition 查询条件
	 * @return 栏目列表
	 */
	default List<CmsColumnPo> findAll(CmsColumnQueryCondition condition) {
		Specifications<CmsColumnPo> spec = Specifications.builder(CmsColumnPo.class)
			.eq(null != condition.getType(), CmsColumnPo::getType, condition.getType())
			.contain(StringUtils.hasText(condition.getName()), CmsColumnPo::getName, condition.getName());
		return findAll(spec);
	}

}
