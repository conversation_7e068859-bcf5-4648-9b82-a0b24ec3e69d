package io.naccoll.boilerplate.annex.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.crypto.SecureUtil;
import io.naccoll.boilerplate.annex.dao.OssAnnexDao;
import io.naccoll.boilerplate.annex.dto.OssAnnexCreateCommand;
import io.naccoll.boilerplate.annex.dto.OssAnnexRefCreateCommand;
import io.naccoll.boilerplate.annex.dto.OssAnnexRefQueryCondition;
import io.naccoll.boilerplate.annex.dto.OssAnnexUpdateCommand;
import io.naccoll.boilerplate.annex.model.OssAnnexPo;
import io.naccoll.boilerplate.annex.model.OssAnnexRefPo;
import io.naccoll.boilerplate.annex.service.*;
import io.naccoll.boilerplate.core.audit.operate.OperateLog;
import io.naccoll.boilerplate.core.exception.ClientException;
import io.naccoll.boilerplate.core.exception.DataRelatedException;
import io.naccoll.boilerplate.core.id.IdService;
import io.naccoll.boilerplate.core.ratelimit.RateLimit;
import io.naccoll.boilerplate.core.ratelimit.RateLimiterMode;
import io.naccoll.boilerplate.core.utils.BeanUtils;
import io.naccoll.boilerplate.oss.DelegatingOssServiceImpl;
import io.naccoll.boilerplate.oss.OssServiceHelperImpl;
import io.naccoll.boilerplate.oss.config.OssProperties;
import io.naccoll.boilerplate.oss.config.OssType;
import io.naccoll.boilerplate.oss.dto.OssObject;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;

/**
 * 通用附件服务实现
 *
 * <AUTHOR>
 */
@Service
@Validated
public class OssAnnexServiceImpl implements OssAnnexService {

	@Resource
	private OssAnnexDao ossAnnexDao;

	@Resource
	private OssAnnexQueryService ossAnnexQueryService;

	@Resource
	private OssAnnexRefQueryService ossAnnexRefQueryService;

	@Resource
	private OssAnnexRefService ossAnnexRefService;

	@Resource
	private IdService idService;

	@Resource
	private AnnexHandlerFacade annexHandlerFacade;

	@Resource
	private DelegatingOssServiceImpl delegatingOssService;

	@Resource
	private OssServiceHelperImpl ossServiceHelper;

	@Resource
	private OssProperties ossProperties;

	@Override
	@OperateLog(value = "新增通用附件", id = "#result.id", type = "通用附件",
			afterDataAccess = "@ossAnnexQueryServiceImpl.findById(#result.id)")
	@Transactional(rollbackFor = Exception.class)
	public OssAnnexRefPo create(@Valid OssAnnexCreateCommand command, MultipartFile file) {
		OssAnnexPo annexPo = new OssAnnexPo();
		if (!annexHandlerFacade.existTarget(command.getTargetType(), command.getTargetId())) {
			throw new ClientException("目标实体不存在");
		}

		if (file != null) {
			annexPo = uploadFile(command, file, annexPo);
		}
		if (annexPo.isNew()) {
			annexPo.setId(idService.getId());
		}

		annexPo = ossAnnexDao.saveAndFlush(annexPo);
		return bindRef(command, annexPo);
	}

	private OssAnnexRefPo bindRef(OssAnnexCreateCommand command, OssAnnexPo annexPo) {
		if (command.hasTarget()) {
			OssAnnexRefCreateCommand ref = new OssAnnexRefCreateCommand();
			BeanUtils.copyProperties(command, ref);
			ref.setAnnexId(annexPo.getId());
			ref.setAnnexName(command.getAnnexName());
			return ossAnnexRefService.save(ref);
		}
		var ref = new OssAnnexRefPo();
		ref.setAnnexName(annexPo.getAnnexName());
		ref.setAnnexId(annexPo.getId());
		return ref;
	}

	@Override
	@OperateLog(value = "修改通用附件", id = "#command.id", type = "通用附件",
			beforeDataAccess = "@ossAnnexQueryServiceImpl.findById(#command.id)",
			afterDataAccess = "@ossAnnexQueryServiceImpl.findById(#result.id)")
	@Transactional(rollbackFor = Exception.class)
	public OssAnnexRefPo update(@Valid OssAnnexUpdateCommand command, MultipartFile file) {
		OssAnnexPo ossAnnex = ossAnnexQueryService.findByIdNotNull(command.getId());
		if (!annexHandlerFacade.existTarget(command.getTargetType(), command.getTargetId())) {
			throw new ClientException("目标实体不存在");
		}

		if (file != null) {
			if (StringUtils.hasText(ossAnnex.getAnnexUrl())) {
				throw new ClientException("附件已上传，不可更新附件");
			}
			OssAnnexPo uploadAnnex = uploadFile(command, file, ossAnnex);
			BeanUtils.copyProperties(uploadAnnex, ossAnnex, OssAnnexPo::getId);
		}
		ossAnnex = ossAnnexDao.saveAndFlush(ossAnnex);
		return bindRef(command, ossAnnex);
	}

	@Override
	@OperateLog(value = "删除通用附件", id = "#id", type = "通用附件",
			beforeDataAccess = "@ossAnnexQueryServiceImpl.findById(#id)")
	@Transactional(rollbackFor = Exception.class)
	@RateLimit(mode = RateLimiterMode.WINDOWS, rate = 1)
	public void deleteById(Long id) {
		var refs = ossAnnexRefQueryService.findAll(OssAnnexRefQueryCondition.builder().build());
		if (CollUtil.isNotEmpty(refs)) {
			throw new DataRelatedException("附件仍存在引用无法删除");
		}
		ossAnnexQueryService.findByIdNotNull(id);
		ossAnnexDao.deleteById(id);
	}

	private OssAnnexPo uploadFile(OssAnnexCreateCommand command, MultipartFile file, OssAnnexPo annexPo) {
		boolean usePublicBucket = Optional.of(command.getPublicBucket()).orElse(false);
		annexHandlerFacade.beforeUpdate(command.getTargetType(), command.getTargetId(), file);
		if (ObjectUtils.isEmpty(command.getAnnexName())) {
			command.setAnnexName(file.getOriginalFilename());
		}
		String fileType;
		if (command.hasTarget()) {
			fileType = String.format("oss-annex/%s/%s", command.getTargetType(), command.getTargetId());
		}
		else {
			fileType = String.format("oss-annex/no-target/%s",
					DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN));
		}

		try (InputStream stream = file.getInputStream()) {
			String annexSign = SecureUtil.sha256(file.getInputStream());
			OssType ossType = delegatingOssService.getOssType();
			String bucketName = usePublicBucket ? ossProperties.getPublicBucket() : ossProperties.getPrivateBucket();
			var exist = ossAnnexQueryService.findNoRepeat(annexSign, ossType, bucketName);
			if (exist != null) {
				return exist;
			}
			String originFilename = FileNameUtil.getName(file.getOriginalFilename());
			String contentType = file.getContentType();
			String ext = FileNameUtil.extName(originFilename);
			String objectName = String.format("%s/%s", fileType, originFilename);
			List<OssObject> objs = delegatingOssService.listObjects(bucketName, objectName);
			// 校验oss是否有同名文件已上传，若已上传，则比较文件内容是否一致，若不一致，则上传文件
			if (objs.stream().anyMatch(i -> Objects.equals(FileNameUtil.getName(i.getObjectName()), originFilename))) {
				var existOssObject = delegatingOssService.getObject(bucketName, objectName);
				try (var existStream = existOssObject.getInputStream()) {
					String annexSign2 = SecureUtil.sha256(existStream);
					if (!Objects.equals(annexSign, annexSign2)) {
						String basename = originFilename.substring(0, originFilename.indexOf("." + ext));
						String newName = basename + "-" + UUID.randomUUID().toString().replace("-", "");
						objectName = String.format("%s/%s.%s", fileType, newName, ext);
						delegatingOssService.putObject(bucketName, objectName, stream, contentType);
					}
				}
			}
			else {
				delegatingOssService.putObject(bucketName, objectName, stream, contentType);
			}
			String url = ossServiceHelper.generateUrl(bucketName, objectName);
			if (command.hasTarget()) {
				annexHandlerFacade.afterUpload(command.getTargetType(), command.getTargetId(), file);
			}
			annexPo.setOssStore(ossType.toString());
			annexPo.setAnnexUrl(url);
			annexPo.setAnnexName(Optional.ofNullable(command.getAnnexName()).orElse(originFilename));
			annexPo.setBucketName(bucketName);
			annexPo.setObjectName(objectName);
			annexPo.setContentType(contentType);
			annexPo.setSize(file.getSize());
			annexPo.setExtName(ext);
			annexPo.setAnnexSign(annexSign);
			annexPo.setRemark(command.getRemark());
			return annexPo;
		}
		catch (IOException e) {
			throw new RuntimeException(e);
		}
	}

}
