package io.naccoll.boilerplate.core.ratelimit;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.scripting.support.ResourceScriptSource;

import java.util.Arrays;
import java.util.List;

/**
 * 基于Redis的令牌桶限流器实现，用于分布式系统中的限流控制
 *
 * <AUTHOR>
 */
@Slf4j
public class RedisTokenBucketRateLimiter extends BaseTokenBucketRateLimiter implements RateLimiter {

	/**
	 * Redis操作模板，用于执行Redis命令和脚本
	 */
	private final StringRedisTemplate redisTemplate;

	/**
	 * Redis脚本对象，用于执行Lua脚本实现限流逻辑
	 */
	private final RedisScript<List<Long>> script;

	public RedisTokenBucketRateLimiter(StringRedisTemplate redisTemplate, RateLimiterProperties defaultConfig) {
		super(true, defaultConfig);
		this.redisTemplate = redisTemplate;
		this.script = redisRequestRateLimiterScript();
	}

	@Override
	protected Response check(String id, int replenishRate, int burstCapacity, int requestedTokens,
			RateLimiterProperties routeConfig) {
		try {
			List<String> keys = getKeys(id);

			List<Long> results;
			try {
				results = this.redisTemplate.execute(this.script, keys, replenishRate + "", burstCapacity + "", "",
						requestedTokens + "");
			}
			catch (Exception e) {
				if (log.isDebugEnabled()) {
					log.debug("Error calling rate limiter lua", e);
				}
				results = Arrays.asList(1L, -1L);
			}
			boolean allowed = results.getFirst() == 1L;
			Long tokensLeft = results.get(1);

			Response response = new Response(allowed, getHeaders(routeConfig, tokensLeft));

			if (log.isTraceEnabled()) {
				log.trace("response: " + response);
			}
			return response;
		}
		catch (Exception e) {
			/*
			 * We don't want a hard dependency on Redis to allow traffic. Make sure to set
			 * an alert so you know if this is happening too much. Stripe's observed
			 * failure rate is 0.01%.
			 */
			log.error("Error determining if user allowed from redis", e);
		}
		return null;
	}

	/**
	 * 加载Redis限流Lua脚本并配置脚本执行器
	 */
	@SuppressWarnings("unchecked")
	public RedisScript redisRequestRateLimiterScript() {
		DefaultRedisScript redisScript = new DefaultRedisScript<>();
		redisScript.setScriptSource(
				new ResourceScriptSource(new ClassPathResource("META-INF/scripts/token_bucket_rate_limiter.lua")));
		redisScript.setResultType(List.class);
		return redisScript;
	}

}
