package io.naccoll.boilerplate.core.statemachine;

import io.naccoll.boilerplate.core.statemachine.impl.TransitionType;

import java.util.Collection;
import java.util.List;

/**
 * State
 *
 * @param <S> the type of state
 * @param <E> the type of event
 * @param <C> the type parameter
 * <AUTHOR>
 * @date 2020 -02-07 2:12 PM
 */
public interface State<S, E, C> extends Visitable {

	/**
	 * Gets the state identifier.
	 * @return the state identifiers
	 */
	S getId();

	/**
	 * Add transition target the state
	 * @param event the event of the Transition
	 * @param target the target of the transition
	 * @param transitionType the transition type
	 * @return transition
	 */
	Transition<S, E, C> addTransition(E event, State<S, E, C> target, TransitionType transitionType);

	/**
	 * Gets event transitions.
	 * @param event the event
	 * @return the event transitions
	 */
	List<Transition<S, E, C>> getEventTransitions(E event);

	/**
	 * Gets all transitions.
	 * @return the all transitions
	 */
	Collection<Transition<S, E, C>> getAllTransitions();

}
