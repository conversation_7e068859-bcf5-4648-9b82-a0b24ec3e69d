package io.naccoll.boilerplate.core.audit;

import io.naccoll.boilerplate.core.security.enums.ClientId;
import io.naccoll.boilerplate.core.security.enums.Realm;

/**
 * 审计用户接口，定义了用户审计所需的基本信息获取方法
 *
 * 该接口用于获取用户审计所需的基本信息，包括用户唯一标识符、显示名称、所属安全域和客户端类型。
 *
 * <AUTHOR>
 */
public interface AuditUser {

	/**
	 * 获取用户唯一标识符 <br>
	 * 该ID应与用户认证系统保持一致
	 * @return 用户的唯一ID，通常为数据库主键
	 */
	Long getId();

	/**
	 * 获取用户显示名称 <br>
	 * 用于审计日志中的用户标识
	 * @return 用户的姓名或昵称
	 */
	String getName();

	/**
	 * 获取用户所属安全域 <br>
	 * 用于多租户系统的权限隔离
	 * @return 用户所在的安全域枚举
	 */
	Realm getRealm();

	/**
	 * 获取请求来源的客户端类型 <br>
	 * 用于区分不同终端设备的访问
	 * @return 客户端类型枚举
	 */
	ClientId getClientId();

}
