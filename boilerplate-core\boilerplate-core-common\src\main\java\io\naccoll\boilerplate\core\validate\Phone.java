package io.naccoll.boilerplate.core.validate;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 手机号码验证注解
 *
 * <AUTHOR>
 */
@Target({ ElementType.FIELD })
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = PhoneValidator.class)
public @interface Phone {

	/**
	 * 获取验证失败的消息
	 * @return 验证失败提示信息
	 */
	String message() default "{error.phone.format}";

	/**
	 * 获取验证组别
	 * @return 验证组别类数组
	 */
	Class<?>[] groups() default {};

	/**
	 * 获取负载信息
	 * @return 负载类数组
	 */
	Class<? extends Payload>[] payload() default {};

}
