package io.naccoll.boilerplate.core.interfaces.function;

import java.util.concurrent.Callable;

/**
 * 定义一个带有受检异常的函数式接口，扩展自标准库的Callable接口。 该接口主要用于需要抛出受检异常的场景，同时提供将受检异常转换为运行时异常的默认实现。
 *
 * <AUTHOR>
 */
@FunctionalInterface
public interface CheckedCallable<T, E extends Throwable> {

	/**
	 * 执行可能抛出受检异常的操作。
	 * @return 操作的结果
	 * @throws E 可能抛出的受检异常
	 */
	T call() throws E;

	/**
	 * 将CheckedCallable转换为标准库的Callable接口实现。 该方法会将受检异常转换为运行时异常，以便与标准库的Callable接口兼容。
	 * @return 转换后的Callable实现
	 */
	default Callable<T> unchecked() {
		return () -> {
			try {
				return call();
			}
			catch (Throwable t) { // NOSONAR
				if (t instanceof RuntimeException runtimeException) { // NOSONAR
					throw runtimeException;
				}
				else if (t instanceof Error error) { // NOSONAR
					throw error;
				}
				else {
					throw new IllegalStateException(t);
				}
			}
		};
	}

}
