package io.naccoll.boilerplate.core.ratelimit;

import lombok.Getter;
import org.springframework.util.Assert;

import java.util.Collections;
import java.util.Map;

/**
 * 限流器接口，定义了限流功能的基本行为规范
 *
 * <AUTHOR>
 */
public interface RateLimiter {

	/**
	 * 检查请求是否被允许
	 * @param key 限流唯一标识
	 * @param args 限流配置参数
	 * @return 限流响应结果，包含是否允许及头部信息
	 */
	Response isAllowed(String key, RateLimiterProperties args);

	/**
	 * 获取当前限流器模式
	 * @return 限流模式枚举值
	 */
	RateLimiterMode getMode();

	/**
	 * 限流响应结果封装类
	 */
	class Response {

		/**
		 * 请求是否被允许
		 */
		@Getter
		private final boolean allowed;

		/**
		 * 剩余令牌数
		 */
		private final long tokensRemaining;

		/**
		 * 响应头部信息（包含限流相关信息）
		 */
		private final Map<String, String> headers;

		/**
		 * 构造限流响应
		 * @param allowed 是否允许请求
		 * @param headers 响应头部信息
		 */
		public Response(boolean allowed, Map<String, String> headers) {
			this.allowed = allowed;
			this.tokensRemaining = -1;
			Assert.notNull(headers, "headers may not be null");
			this.headers = headers;
		}

		/**
		 * 获取不可修改的响应头信息
		 * @return 只读的头部信息Map
		 */
		public Map<String, String> getHeaders() {
			return Collections.unmodifiableMap(headers);
		}

		@Override
		public String toString() {
			return "Response{" + "allowed=" + allowed + ", headers=" + headers + ", tokensRemaining=" + tokensRemaining
					+ '}';
		}

	}

}
