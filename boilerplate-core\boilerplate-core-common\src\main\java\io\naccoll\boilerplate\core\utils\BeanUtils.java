package io.naccoll.boilerplate.core.utils;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ReflectUtil;
import com.esotericsoftware.kryo.Kryo;
import com.esotericsoftware.kryo.io.ByteBufferInput;
import com.esotericsoftware.kryo.io.ByteBufferOutput;
import io.naccoll.boilerplate.core.interfaces.function.PropertyFunc;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.beans.BeansException;
import org.springframework.cglib.beans.BeanCopier;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Bean操作工具类，提供对象属性复制、深拷贝等功能
 * <p>
 * 基于Spring BeanWrapper和Cglib BeanCopier实现高性能属性复制
 *
 * <AUTHOR>
 */
public class BeanUtils {

	private static final Map<String, BeanCopier> BEAN_COPIERS = new ConcurrentHashMap<>(100);

	/**
	 * 目标集合类型的前缀
	 */
	public static final String JAVA_UTIL_IMMUTABLE_COLLECTIONS = "java.util.ImmutableCollections";

	private BeanUtils() {
	}

	/**
	 * 复制源对象属性到目标对象
	 * @param source 源对象
	 * @param target 目标对象
	 */
	public static void copyProperties(Object source, Object target) {
		copy(source, target);
	}

	/**
	 * 复制源对象属性到新创建的目标对象
	 * @param source 源对象
	 * @param supplier 目标对象构造器
	 * @param <T> 目标对象类型
	 * @return 新创建并复制属性后的目标对象
	 */
	public static <T> T copyProperties(Object source, Supplier<T> supplier) {
		T target = supplier.get();
		copy(source, target);
		return target;
	}

	/**
	 * 复制列表中的每个元素到新创建的目标对象列表
	 * @param source 源对象列表
	 * @param supplier 目标对象构造器
	 * @param <T> 目标对象类型
	 * @return 新创建并复制属性后的目标对象列表
	 */
	public static <T> List<T> copyProperties(List<?> source, Supplier<T> supplier) {
		return source.stream().map(i -> copyProperties(i, supplier)).collect(Collectors.toList());
	}

	/**
	 * 复制集合中的每个元素到新创建的目标对象集合
	 * @param source 源对象集合
	 * @param supplier 目标对象构造器
	 * @param <T> 目标对象类型
	 * @return 新创建并复制属性后的目标对象集合
	 */
	public static <T> Set<T> copyProperties(Set<?> source, Supplier<T> supplier) {
		return source.stream().map(i -> copyProperties(i, supplier)).collect(Collectors.toSet());
	}

	/**
	 * 复制流中的每个元素到新创建的目标对象流
	 * @param source 源对象流
	 * @param supplier 目标对象构造器
	 * @param <T> 目标对象类型
	 * @return 新创建并复制属性后的目标对象流
	 */
	public static <T> Stream<T> copyProperties(Stream<?> source, Supplier<T> supplier) {
		return source.map(i -> copyProperties(i, supplier));
	}

	/**
	 * 基于序列化的深拷贝，要求被拷贝的类必须实现{@link java.io.Serializable}接口
	 * <p>
	 * 使用Kryo序列化框架实现高性能深拷贝
	 * @param t 待拷贝的对象
	 * @return 拷贝的结果
	 * @param <T> 拷贝对象类型
	 */
	@SuppressWarnings("unchecked")
	public static <T> T serializableCopy(T t) {
		Kryo kryo = new Kryo();
		kryo.setRegistrationRequired(false);
		try (var output = new ByteBufferOutput(4096, 65535)) {
			kryo.writeClassAndObject(output, t);
			byte[] bytes = output.toBytes();
			try (var input = new ByteBufferInput(bytes)) {
				return (T) kryo.readClassAndObject(input);
			}
		}
	}

	/**
	 * 仅支持浅拷贝跟简单的集合类型的拷贝，嵌套对象的内层对象不会被拷贝，性能比{@link #serializableCopy(Object)}高
	 * <p>
	 * 支持List、Set、Map等集合类型的拷贝
	 * @param t 待拷贝的对象
	 * @return 拷贝的结果
	 * @param <T> 拷贝对象类型
	 */
	@SuppressWarnings("unchecked")
	public static <T> T shallowCopy(T t) {
		Class<?> clazz = t.getClass();

		if (t instanceof Collection<?>collection) {
			if (collection.getClass().getName().startsWith(JAVA_UTIL_IMMUTABLE_COLLECTIONS)) {
				if (collection instanceof List list) {
					return (T) list.stream().map(BeanUtils::shallowCopy).collect(Collectors.toList());
				}
				if (collection instanceof Set set) {
					return (T) set.stream().map(BeanUtils::shallowCopy).collect(Collectors.toSet());
				}
			}
			Collection copyCollection = ReflectUtil.newInstance(collection.getClass());
			copyCollection.addAll(collection);
			return (T) copyCollection;
		}
		else if (t instanceof Map<?, ?>map) {
			if (map.getClass().getName().startsWith(JAVA_UTIL_IMMUTABLE_COLLECTIONS)) {
				Map copyMap = MapUtil.newHashMap(map.size());
				copyMap.putAll(map);
				return (T) map;
			}
			Map<Object, Object> newMap = ReflectUtil.newInstance(map.getClass());
			map.forEach((k, v) -> newMap.put(k, shallowCopy(v)));
			return (T) newMap;
		}
		else {
			// 处理其他对象类型
			Supplier<T> supplier = LambdaFactory.createConstructorSupplier(clazz);
			T result = supplier.get();
			copy(t, result);
			return result;
		}
	}

	public static void copyProperties(Object source, Object target, String... ignoreProperties) throws BeansException {
		org.springframework.beans.BeanUtils.copyProperties(source, target, ignoreProperties);
	}

	@SafeVarargs
	public static <T, R> void copyProperties(Object source, Object target, PropertyFunc<T, R>... ignorePropertiesFn)
			throws BeansException {
		String[] ignoreProperties = new String[ignorePropertiesFn.length];
		for (int i = 0; i < ignorePropertiesFn.length; i++) {
			ignoreProperties[i] = PojoHelper.getPropertyName(ignorePropertiesFn[i]);
		}
		copyProperties(source, target, ignoreProperties);
	}

	public static void copyPropertiesIgnoreNull(Object source, Object target) {
		Set<String> ignoreSet = getNullPropertyNames(source);
		String[] ignories = new String[ignoreSet.size()];
		ignoreSet.toArray(ignories);
		copyProperties(source, target, ignories);
	}

	public static void copyPropertiesIgnoreNull(Object source, Object target, String... ignoreProperties) {
		Set<String> ignoreSet = getNullPropertyNames(source);
		ignoreSet.addAll(Arrays.asList(ignoreProperties));
		String[] ignories = new String[ignoreSet.size()];
		ignoreSet.toArray(ignories);
		copyProperties(source, target, ignories);
	}

	@SafeVarargs
	public static <T, R> void copyPropertiesIgnoreNull(Object source, Object target,
			PropertyFunc<T, R>... ignorePropertiesFn) {
		String[] ignoreProperties = new String[ignorePropertiesFn.length];
		for (int i = 0; i < ignorePropertiesFn.length; i++) {
			ignoreProperties[i] = PojoHelper.getPropertyName(ignorePropertiesFn[i]);
		}
		copyPropertiesIgnoreNull(source, target, ignoreProperties);
	}

	private static void copy(Object srcObj, Object destObj) {

		String key = genKey(srcObj.getClass(), destObj.getClass());
		BeanCopier copier = null;
		if (!BEAN_COPIERS.containsKey(key)) {
			copier = BeanCopier.create(srcObj.getClass(), destObj.getClass(), false);
			BEAN_COPIERS.put(key, copier);
		}
		else {
			copier = BEAN_COPIERS.get(key);
		}
		copier.copy(srcObj, destObj, null);

	}

	private static String genKey(Class<?> srcClazz, Class<?> destClazz) {
		return srcClazz.getName() + destClazz.getName();
	}

	private static Set<String> getNullPropertyNames(Object source) {
		final BeanWrapper src = new BeanWrapperImpl(source);
		java.beans.PropertyDescriptor[] pds = src.getPropertyDescriptors();

		Set<String> emptyNames = new HashSet<>();
		for (java.beans.PropertyDescriptor pd : pds) {
			Object srcValue = src.getPropertyValue(pd.getName());
			if (srcValue == null) {
				emptyNames.add(pd.getName());
			}
		}
		return emptyNames;
	}

}
