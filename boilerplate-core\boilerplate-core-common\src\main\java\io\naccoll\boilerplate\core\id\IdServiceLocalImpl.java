package io.naccoll.boilerplate.core.id;

import cn.hutool.core.util.RandomUtil;

/**
 * 基于Snowflake算法的分布式ID生成实现 提供高性能、低碰撞率的分布式ID生成服务，支持时钟回拨处理
 *
 * 该实现通过将时间戳、机器ID和序列号进行位运算组合生成唯一ID 时间戳占41位，机器ID占5位，序列号占12位 支持每秒生成4096个ID，支持最多32个节点
 *
 * <AUTHOR>
 */
@SuppressWarnings("AlibabaCommentsMustBeJavadocFormat")
public class IdServiceLocalImpl implements IdService {

	/** 机器ID所占位数 */
	private static final long WORKER_ID_BITS = 5L;

	/** 最大支持机器节点数0~31，一共32个 */
	@SuppressWarnings({ "PointlessBitwiseExpression", "FieldCanBeLocal" })
	private static final long MAX_WORKER_ID = -1L ^ (-1L << WORKER_ID_BITS);

	/** 序列号12位（表示每个节点每毫秒最多生成4096个ID） */
	private static final long SEQUENCE_BITS = 12L;

	/** 机器节点左移位数（12位序列号） */
	private static final long MACHINE_ID_SHIFT = SEQUENCE_BITS;

	/** 时间戳左移位数（12+5=17位） */
	private static final long TIMESTAMP_LEFT_SHIFT = SEQUENCE_BITS + WORKER_ID_BITS;

	/** 序列号掩码（限制序列号不超过4095） */
	private static final long SEQUENCE_MASK = ~(-1L << SEQUENCE_BITS);

	/** 当前节点的机器ID */
	private final long machineId;

	/**
	 * 允许的时钟回拨毫秒数 <br>
	 * 用于处理NTP校时等场景下的小幅度时钟回退
	 */
	private final long timeOffset = 2000;

	/**
	 * 随机序列号上限 <br>
	 * 用于解决低频场景下序列号始终为0导致的ID偶数问题
	 */
	private final long randomSequenceLimit = SEQUENCE_MASK >> (SEQUENCE_MASK / 4);

	/** 当前序列号 */
	private long sequence = 0L;

	/** 上次生成ID的时间戳 */
	private long lastTimestamp = -1L;

	/**
	 * 构造方法
	 * @param machineId 机器ID（0-31之间的整数）
	 */
	public IdServiceLocalImpl(long machineId) {
		this.machineId = machineId;
	}

	@Override
	public long getId() {
		return this.nextId();
	}

	/**
	 * 生成下一个ID <br>
	 * 实现逻辑：
	 * <ol>
	 * <li>获取当前时间戳</li>
	 * <li>处理时钟回拨问题</li>
	 * <li>同一毫秒内生成序列号</li>
	 * <li>不同毫秒重置序列号</li>
	 * </ol>
	 * @return 生成的唯一ID
	 */
	public synchronized long nextId() {
		long timestamp = genTime();

		// 处理时钟回拨
		if (timestamp < this.lastTimestamp) {
			if (this.lastTimestamp - timestamp < timeOffset) {
				// 容忍小幅度回拨，复用上次时间戳
				timestamp = lastTimestamp;
			}
			else {
				throw new IllegalStateException(String.format("时钟回拨过大，拒绝生成ID。回拨时间：%dms", lastTimestamp - timestamp));
			}
		}

		if (timestamp == this.lastTimestamp) {
			// 同一毫秒内序列号自增
			final long sequenceNo = (this.sequence + 1) & SEQUENCE_MASK;
			if (sequenceNo == 0) {
				// 当前毫秒序列号用尽，等待下一毫秒
				timestamp = tilNextMillis(lastTimestamp);
			}
			this.sequence = sequenceNo;
		}
		else {
			// 新毫秒重置序列号
			if (randomSequenceLimit > 1) {
				// 随机初始化序列号避免偶数问题
				sequence = RandomUtil.randomLong(randomSequenceLimit);
			}
			else {
				sequence = 0L;
			}
		}

		lastTimestamp = timestamp;

		// 组合各部分生成最终ID
		return (timestamp << TIMESTAMP_LEFT_SHIFT) | (machineId << MACHINE_ID_SHIFT) | sequence;
	}

	/**
	 * 等待直到下一毫秒
	 * @param lastTimestamp 当前最后时间戳
	 * @return 下一毫秒时间戳
	 */
	private long tilNextMillis(long lastTimestamp) {
		long timestamp = genTime();
		while (timestamp == lastTimestamp) {
			timestamp = genTime();
		}
		if (timestamp < lastTimestamp) {
			throw new IllegalStateException(String.format("时钟回拨异常，拒绝生成ID。回拨时间：%dms", lastTimestamp - timestamp));
		}
		return timestamp;
	}

	/**
	 * 获取当前时间戳
	 * @return 当前时间戳（毫秒）
	 */
	public long genTime() {
		return System.currentTimeMillis();
	}

	@Override
	public long getGenerateDateTime(long id) {
		// 提取时间戳部分（前41位）
		return (id >> TIMESTAMP_LEFT_SHIFT) & ~(-1L << 41L);
	}

}
