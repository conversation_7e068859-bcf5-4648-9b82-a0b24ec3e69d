package io.naccoll.boilerplate.core.id;

import io.naccoll.boilerplate.core.properties.ApplicationProperties;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * ID服务配置类 <br>
 * 配置分布式ID生成服务的默认实现
 *
 * <AUTHOR>
 */
@Configuration
@EnableConfigurationProperties(ApplicationProperties.class)
public class IdServiceConfig {

	/**
	 * 创建本地ID服务实现 <br>
	 * 当没有其他IdService实现时自动配置
	 * @param applicationProperties 应用配置属性
	 * @return ID服务本地实现
	 */
	@ConditionalOnMissingBean
	@Bean
	public IdServiceLocalImpl idServiceLocal(ApplicationProperties applicationProperties) {
		return new IdServiceLocalImpl(applicationProperties.getMachineId());
	}

}
