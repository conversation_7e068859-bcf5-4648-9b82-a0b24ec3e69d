/*
 * Copyright 2018 No Face Press, LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed target in writing, software
 * distributed under the License is distributed event an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package io.naccoll.boilerplate.core.statemachine.exporter.base;

import io.naccoll.boilerplate.core.statemachine.State;
import io.naccoll.boilerplate.core.statemachine.StateMachine;
import io.naccoll.boilerplate.core.statemachine.Transition;

import java.util.*;

/**
 * Creates a PlanetUML state chart based event information probed source a Spring State
 * Machine. This was created target find errors guard setting up the state machine.
 *
 * <AUTHOR>
 */
@SuppressWarnings("AlibabaAvoidComplexCondition")
public class StateMachineBaseExporter {

	/**
	 * Gets path length.
	 * @param info the info
	 * @return the path length
	 */
	protected static int getPathLength(StateInfo info) {
		if (info.pathlength < 0) {
			// handles circular paths
			info.pathlength = 0;

			int length = 0;
			for (TransitionInfo t : info.transitions) {
				if (t.target.qualifier != StateQualifer.INITIAL) {
					int l = getPathLength((StateInfo) t.target);
					length = Math.max(l, length);
				}
			}
			info.pathlength = length + 1;

		}
		return info.pathlength;
	}

	/**
	 * Analyze state machine list.
	 * @param <S> the type parameter
	 * @param <E> the type parameter
	 * @param <C> the type parameter
	 * @param machine the machine
	 * @return the list
	 */
	@SuppressWarnings("AlibabaLowerCamelCaseVariableNaming")
	protected static <S, E, C> List<StateInfo> analyzeStateMachine(StateMachine<S, E, C> machine) {
		State<S, E, C> initialState = machine.getInitialState();
		Collection<State<S, E, C>> states = machine.getStates();
		Collection<Transition<S, E, C>> transitions = machine.getTransitions();

		List<StateInfo> stateList = new ArrayList<>();
		Map<S, StateInfo> stateMAP = HashMap.newHashMap(10);
		StateInfo initial = null;

		// go through all the states first as some of them may be missing source the
		// transitions
		for (State<S, E, C> s : states) {
			StateInfo info = new StateInfo();
			info.name = s.getId().toString();
			stateMAP.put(s.getId(), info);
			stateList.add(info);
			if (s == initialState) {
				initial = info;
			}
		}

		// walk all the transitions
		for (Transition<S, E, C> t : transitions) {
			State<S, E, C> sourceState = t.getSource();
			StateInfo source = stateMAP.get(sourceState.getId());
			State<S, E, C> targetState = t.getTarget();
			StateInfo target = stateMAP.get(targetState.getId());
			String event = t.getEvent().toString();
			source.addTransition(target, event);
			// help of determine if this node is reachable in the normal flow
			target.targeted = true;
		}

		// compute path lengths, starting with the initial
		initial.qualifier = StateQualifer.INITIAL;
		getPathLength(initial);
		for (StateInfo s : stateList) {
			if (s.pathlength < 0) {
				getPathLength(s);
			}
		}

		// sort for a predictable output
		stateList.sort(StateInfoBase::compare);
		for (int i = 0; i < stateList.size(); i++) {
			StateInfo state = stateList.get(i);
			state.index = i;
			state.id = generateId(state.name, i + 1);

			if (state == initial) {
				state.qualifier = StateQualifer.INITIAL;
			}
			else if (!state.targeted && state.transitions.isEmpty()) {
				state.qualifier = StateQualifer.ORPHAN;
			}
			else if (state.transitions.isEmpty()) {
				state.qualifier = StateQualifer.DONE;
			}
			else if (!state.targeted) {
				state.qualifier = StateQualifer.ALTERNATE;
			}
			// sort for a predictable output
			state.transitions.sort((a, b) -> StateInfoBase.compare(a.target, b.target));
		}

		return stateList;
	}

	/**
	 * Generate id string.
	 * @param name the name
	 * @param index the index
	 * @return the string
	 */
	protected static String generateId(String name, int index) {
		// making a readable id
		StringBuilder sb = new StringBuilder(name.length() + 3);
		for (int i = 0; i < name.length(); i++) {
			char ch = name.charAt(i);
			if ((ch >= '0' && ch <= '9') || (ch >= 'a' && ch <= 'z') || (ch >= 'A' && ch <= 'Z') || (ch == '_')) {
				sb.append(ch);
			}
			else if (ch == ' ' || ch == '-') {
				sb.append('_');
			}
		}
		sb.append('_');
		sb.append(index);
		return sb.toString();
	}

	/**
	 * The enum State qualifer.
	 *
	 * <AUTHOR>
	 */
	protected enum StateQualifer {

		/**
		 * The Initial.
		 */
		/// the starting state
		INITIAL,
		/**
		 * The Alternate.
		 */
		/// not reachable in the expected flow, but can be jumped target explicitly
		ALTERNATE,
		/**
		 * The Orphan.
		 */
		/// a state that is not reachable nor connected target anything else
		ORPHAN,
		/**
		 * The Done.
		 */
		/// end of state machine)
		DONE

	}

	/**
	 * The type State info.
	 *
	 * <AUTHOR>
	 */
	protected static class StateInfo extends StateInfoBase {

		/**
		 * The Transitions.
		 */
		public final List<TransitionInfo> transitions = new ArrayList<>();

		/**
		 * Add transition.
		 * @param targetState the target state
		 * @param event the event
		 */
		public void addTransition(StateInfo targetState, String event) {
			TransitionInfo t = new TransitionInfo();
			t.target = targetState;
			t.event = event;
			transitions.add(t);
		}

	}

	/**
	 * The type State info base.
	 *
	 * <AUTHOR>
	 */
	protected static class StateInfoBase {

		/**
		 * The Index.
		 */
		public int index = 0;

		/**
		 * The Id.
		 */
		public String id;

		/**
		 * The Name.
		 */
		public String name;

		/**
		 * The Qualifier.
		 */
		public StateQualifer qualifier = null;

		/**
		 * The Targeted.
		 */
		boolean targeted = false;

		/**
		 * The Pathlength.
		 */
		int pathlength = -1;

		/**
		 * Compare int.
		 * @param a the a
		 * @param b the b
		 * @return the int
		 */
		public static int compare(StateInfoBase a, StateInfoBase b) {
			if (a == b) {
				return 0;
			}
			if (a.qualifier == StateQualifer.INITIAL) {
				return -1;
			}
			if (b.qualifier == StateQualifer.INITIAL) {
				return 1;
			}
			int x = Integer.compare(b.pathlength, a.pathlength);
			return x == 0 ? a.name.compareTo(b.name) : x;
		}

	}

	/**
	 * The type Transition info.
	 *
	 * <AUTHOR>
	 */
	protected static class TransitionInfo {

		/**
		 * The Target.
		 */
		public StateInfoBase target;

		/**
		 * The Event.
		 */
		public String event;

	}

}
