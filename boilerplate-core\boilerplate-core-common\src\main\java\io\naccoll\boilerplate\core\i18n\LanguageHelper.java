package io.naccoll.boilerplate.core.i18n;

import io.naccoll.boilerplate.core.exception.BusinessException;
import jakarta.annotation.Resource;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;

import java.util.Locale;

/**
 * 语言助手类，用于处理国际化信息。 通过MessageSource获取国际化消息，并支持多种Locale。
 *
 * <AUTHOR>
 */
@Component
public class LanguageHelper {

	@Resource
	private MessageSource messageSource;

	/**
	 * 获取国际化消息。 根据给定的代码、参数和Locale获取对应的消息。
	 * @param code 消息代码
	 * @param args 消息参数
	 * @param locale 语言环境
	 * @return 对应的消息字符串
	 * @throws BusinessException 如果消息代码不存在
	 */
	public String getMessage(String code, Object[] args, Locale locale) {
		return messageSource.getMessage(code, args, code, locale);
	}

	/**
	 * 获取国际化消息。 根据给定的代码和参数获取对应的消息，使用当前线程的Locale。
	 * @param code 消息代码
	 * @param args 消息参数
	 * @return 对应的消息字符串
	 * @throws BusinessException 如果消息代码不存在
	 */
	public String getMessage(String code, Object[] args) {
		return messageSource.getMessage(code, args, code, LocaleContextHolder.getLocale());
	}

	/**
	 * 将业务异常转为字符串输出。 强制使用简体中文Locale获取异常消息。
	 * @param e 业务异常类
	 * @return 匹配的错误信息字符串
	 */
	public String getMessageForceLocale(BusinessException e) {
		return messageSource.getMessage(e.getMessageCode(), e.getParameters(), e.getMessageCode(),
				Locale.SIMPLIFIED_CHINESE);
	}

	/**
	 * 将业务异常转为字符串输出。 使用当前线程的Locale获取异常消息。
	 * @param e 业务异常类
	 * @return 匹配的错误信息字符串
	 */
	public String getMessage(BusinessException e) {
		return messageSource.getMessage(e.getMessageCode(), e.getParameters(), e.getMessageCode(),
				LocaleContextHolder.getLocale());
	}

	/**
	 * 获取国际化消息。 根据给定的消息代码获取对应的消息，使用当前线程的Locale。
	 * @param msg 消息代码
	 * @return 对应的消息字符串
	 * @throws BusinessException 如果消息代码不存在
	 */
	public String getMessage(String msg) {
		return messageSource.getMessage(msg, new Object[1], msg, LocaleContextHolder.getLocale());
	}

}
