package io.naccoll.boilerplate.core.enums;

import java.util.Objects;
import java.util.Optional;

/**
 * 枚举工具类，提供枚举相关操作方法
 *
 * <AUTHOR>
 */
public class EnumHelper {

	private EnumHelper() {
	}

	/**
	 * 根据ID获取对应的枚举实例
	 * @param <T> 枚举类型，必须实现IdableEnum接口
	 * @param enumClass 枚举类，必须是枚举类型
	 * @param id 枚举实例的唯一标识ID
	 * @return 返回对应的枚举实例，如果未找到则抛出IllegalArgumentException异常
	 */
	public static <T extends IdableEnum> T fromId(Class<? extends IdableEnum> enumClass, int id) {
		if (!enumClass.isEnum()) {
			throw new IllegalArgumentException("parse enum to int fail: the class is not a enum implements IdableEnum");
		}
		IdableEnum[] enums = enumClass.getEnumConstants();
		for (IdableEnum idableEnum : enums) {
			if (Objects.equals(idableEnum.getId(), id)) {
				return (T) idableEnum;
			}
		}
		throw new IllegalArgumentException("parse enum to int fail: the " + id + " is not legal id in " + enumClass);
	}

	/**
	 * 验证指定ID是否为有效枚举实例标识
	 * @param enumClass 枚举类，必须是枚举类型
	 * @param id 需要验证的枚举ID
	 * @return 如果ID有效则返回true，否则返回false
	 */
	public static boolean isValid(Class<? extends IdableEnum> enumClass, int id) {
		if (!enumClass.isEnum()) {
			return false;
		}
		IdableEnum[] enums = enumClass.getEnumConstants();
		for (IdableEnum idableEnum : enums) {
			if (Objects.equals(idableEnum.getId(), id)) {
				return true;
			}
		}
		return false;
	}

	/**
	 * 获取枚举实例的显示名称
	 * @param enumClass 枚举类，必须实现DisplayEnum接口
	 * @param id 枚举实例的唯一标识ID
	 * @return 返回枚举实例的显示名称，如果未找到对应实例则返回空字符串
	 */
	public static String getName(Class<? extends DisplayEnum> enumClass, Integer id) {
		return getName(enumClass, id, "");
	}

	/**
	 * 获取枚举实例的显示名称（带默认值）
	 * @param enumClass 枚举类，必须实现DisplayEnum接口
	 * @param id 枚举实例的唯一标识ID
	 * @param defaultValue 当枚举实例不存在时返回的默认值
	 * @return 返回枚举实例的显示名称，如果未找到对应实例则返回默认值
	 */
	public static String getName(Class<? extends DisplayEnum> enumClass, Integer id, String defaultValue) {
		if (id == null) {
			return defaultValue;
		}
		return Optional.of(id)
			.filter(i -> EnumHelper.isValid(enumClass, i))
			.map(i -> fromId(enumClass, i))
			.map(i -> ((DisplayEnum) i).getName())
			.orElse("");
	}

}
