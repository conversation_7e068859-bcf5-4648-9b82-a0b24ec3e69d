package io.naccoll.boilerplate.core.ratelimit;

import io.naccoll.boilerplate.core.cache.CacheTemplate;

import java.time.Duration;
import java.util.List;

/**
 * 简单漏桶限流器实现
 *
 * <AUTHOR>
 */
public class SimpleLeakBucketRateLimiter extends BaseLeakBucketRateLimiter {

	private final CacheTemplate cacheTemplate;

	/**
	 * 构造函数
	 * @param defaultConfig 默认限流配置
	 * @param cacheTemplate 缓存操作模板
	 */
	protected SimpleLeakBucketRateLimiter(RateLimiterProperties defaultConfig, CacheTemplate cacheTemplate) {
		super(true, defaultConfig);
		this.cacheTemplate = cacheTemplate;
	}

	/**
	 * 执行漏桶限流检查
	 * @param id 限流唯一标识
	 * @param leakRate 漏桶每秒流失速率
	 * @param capacity 漏桶容量
	 * @param requestedTokens 请求水量
	 * @param routeConfig 路由配置
	 * @return 限流响应结果
	 */
	@Override
	protected Response check(String id, int leakRate, int capacity, int requestedTokens,
			RateLimiterProperties routeConfig) {
		String lockKey = "ratelimit:leak:" + id;
		return cacheTemplate.executeLocked(lockKey, () -> {
			List<String> keys = getKeys(id);
			String waterKey = keys.getFirst().replace(".tokens", ".water");
			String lastLeakTimeKey = keys.get(1).replace(".timestamp", ".last_leak_time");

			double fillTime = 1.0 * capacity / leakRate;
			long ttl = (long) Math.floor(fillTime * 2);
			int now = (int) (System.currentTimeMillis() / 1000);

			Object lastWaterObj = cacheTemplate.get(waterKey);
			int lastWater = lastWaterObj == null ? 0 : (int) lastWaterObj;

			Object lastLeakTimeObj = cacheTemplate.get(lastLeakTimeKey);
			int lastLeakTime = lastLeakTimeObj == null ? now : (int) lastLeakTimeObj;

			int delta = Math.max(0, now - lastLeakTime);
			int leakedWater = delta * leakRate;
			int currentWater = Math.max(0, lastWater - leakedWater);

			boolean allowed = (currentWater + requestedTokens) <= capacity;
			int newWater = currentWater;
			long allowedNum = 0;
			if (allowed) {
				newWater = currentWater + requestedTokens;
				allowedNum = 1;
			}

			if (ttl > 0) {
				cacheTemplate.set(waterKey, newWater, Duration.ofSeconds(ttl));
				cacheTemplate.set(lastLeakTimeKey, now, Duration.ofSeconds(ttl));
			}
			return new Response(allowed, getHeaders(routeConfig, allowedNum));
		});
	}

}