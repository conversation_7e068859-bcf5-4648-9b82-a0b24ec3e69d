package io.naccoll.boilerplate.core.statemachine;

/**
 * Generic strategy interface used by a state machine to respond events by executing an
 * {@code Action} with a {@link StateContext}.
 *
 * @param <S> the type parameter
 * @param <E> the type parameter
 * @param <C> the type parameter
 * <AUTHOR>
 * @date 2020 -02-07 2:51 PM
 */
public interface Action<S, E, C> {

	/**
	 * Execute.
	 * @param from the from
	 * @param to the to
	 * @param event the event
	 * @param context the context
	 */
	void execute(S from, S to, E event, C context);

	/**
	 * 获取状态流转所需的参数类型
	 * @return
	 */
	default Class<?> getParameterType() {
		return null;
	}

}
