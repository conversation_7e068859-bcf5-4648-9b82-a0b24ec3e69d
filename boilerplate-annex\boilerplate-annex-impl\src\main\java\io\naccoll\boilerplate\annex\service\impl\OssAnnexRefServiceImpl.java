package io.naccoll.boilerplate.annex.service.impl;

import io.naccoll.boilerplate.annex.dao.OssAnnexRefDao;
import io.naccoll.boilerplate.annex.dto.OssAnnexRefCreateCommand;
import io.naccoll.boilerplate.annex.dto.OssAnnexRefQueryCondition;
import io.naccoll.boilerplate.annex.model.OssAnnexRefPo;
import io.naccoll.boilerplate.annex.service.AnnexHandlerFacade;
import io.naccoll.boilerplate.annex.service.OssAnnexQueryService;
import io.naccoll.boilerplate.annex.service.OssAnnexRefQueryService;
import io.naccoll.boilerplate.annex.service.OssAnnexRefService;
import io.naccoll.boilerplate.core.audit.operate.OperateLog;
import io.naccoll.boilerplate.core.exception.ClientException;
import io.naccoll.boilerplate.core.exception.ForbiddenException;
import io.naccoll.boilerplate.core.id.IdService;
import io.naccoll.boilerplate.core.lock.UseLock;
import io.naccoll.boilerplate.core.ratelimit.RateLimit;
import io.naccoll.boilerplate.core.ratelimit.RateLimiterMode;
import io.naccoll.boilerplate.core.utils.BeanUtils;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;

import java.util.*;

/**
 * 通用附件引用服务实现
 *
 * <AUTHOR>
 */
@Service
@Validated
public class OssAnnexRefServiceImpl implements OssAnnexRefService {

	@Resource
	private OssAnnexRefDao ossAnnexRefDao;

	@Resource
	private OssAnnexRefQueryService ossAnnexRefQueryService;

	@Resource
	private IdService idService;

	@Resource
	private OssAnnexQueryService ossAnnexQueryService;

	@Resource
	private AnnexHandlerFacade annexHandlerFacade;

	@Resource
	private ObjectProvider<OssAnnexRefServiceImpl> ossAnnexRefService;

	@Override
	@OperateLog(value = "保存通用附件引用", id = "#result.id", type = "通用附件引用",
			afterDataAccess = "@ossAnnexRefQueryServiceImpl.findUniq(#command.annexId,#command.targetType,#command.targetId,#command.annexGroup)")
	@Transactional(rollbackFor = Exception.class)
	@UseLock(prefix = "ossAnnexRef",
			key = "#command.annexId+'-'+#command.targetType+'-'+#command.targetId + '-'+#command.annexGroup")
	public OssAnnexRefPo save(@Valid OssAnnexRefCreateCommand command) {
		var annex = ossAnnexQueryService.findByIdNotNull(command.getAnnexId());
		OssAnnexRefPo ossAnnexRef = Optional
			.ofNullable(ossAnnexRefQueryService.findUniq(command.getAnnexId(), command.getTargetType(),
					command.getTargetId(), command.getAnnexGroup()))
			.orElse(new OssAnnexRefPo());
		if (!annexHandlerFacade.checkPermission(command.getTargetType(), command.getTargetId())) {
			throw new ForbiddenException("用户无权限新建附件引用");
		}
		BeanUtils.copyPropertiesIgnoreNull(command, ossAnnexRef);
		ossAnnexRef.setAnnexId(annex.getId());
		if (ossAnnexRef.isNew()) {
			ossAnnexRef.setId(idService.getId());
		}
		if (StringUtils.hasText(command.getAnnexName())) {
			ossAnnexRef.setAnnexName(command.getAnnexName());
		}
		annexHandlerFacade.beforeSave(ossAnnexRef);
		ossAnnexRef = ossAnnexRefDao.save(ossAnnexRef);
		annexHandlerFacade.afterSave(ossAnnexRef);
		return ossAnnexRef;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public List<OssAnnexRefPo> deleteAndSave(String targetType, String targetId, String annexGroup,
			List<OssAnnexRefCreateCommand> commands) {
		if (!commands.stream().allMatch(c -> equalsTarget(targetType, targetId, annexGroup, c))) {
			throw new ClientException("提交附件并不归属于相同目标");
		}
		if (!annexHandlerFacade.checkPermission(targetType, targetId)) {
			throw new ForbiddenException("用户无权限重建附件引用");
		}
		List<Long> newAnnexIds = commands.stream().map(OssAnnexRefCreateCommand::getAnnexId).toList();
		OssAnnexRefQueryCondition condition = new OssAnnexRefQueryCondition();
		condition.setTargetId(Collections.singletonList(targetId));
		condition.setTargetType(targetType);
		condition.setAnnexGroup(annexGroup);
		var exists = ossAnnexRefQueryService.findAll(condition);
		var deleteIds = exists.stream().filter(i -> !newAnnexIds.contains(i.getAnnexId())).distinct().toList();
		for (OssAnnexRefPo deleteId : deleteIds) {
			ossAnnexRefService.getObject().deleteById(deleteId.getId());
		}
		List<OssAnnexRefPo> refs = new ArrayList<>();
		for (OssAnnexRefCreateCommand command : commands) {
			refs.add(ossAnnexRefService.getObject().save(command));
		}
		return refs;
	}

	private static boolean equalsTarget(String targetType, String targetId, String annexGroup,
			OssAnnexRefCreateCommand c) {
		return Objects.equals(c.getTargetId(), targetId) && Objects.equals(c.getTargetType(), targetType)
				&& Objects.equals(c.getAnnexGroup(), annexGroup);
	}

	@Override
	@OperateLog(value = "删除通用附件引用", id = "#id", type = "通用附件引用",
			beforeDataAccess = "@ossAnnexRefQueryServiceImpl.findById(#id)")
	@Transactional(rollbackFor = Exception.class)
	@RateLimit(mode = RateLimiterMode.WINDOWS, rate = 1)
	public void deleteById(Long id) {
		OssAnnexRefPo ossAnnexRef = ossAnnexRefQueryService.findByIdNotNull(id);
		if (!annexHandlerFacade.checkPermission(ossAnnexRef.getTargetType(), ossAnnexRef.getTargetId())) {
			throw new ForbiddenException("用户无权限删除附件引用");
		}
		ossAnnexRefDao.delete(ossAnnexRef);
	}

}
