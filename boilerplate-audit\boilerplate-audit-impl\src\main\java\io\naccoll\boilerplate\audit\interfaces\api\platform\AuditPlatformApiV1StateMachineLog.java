package io.naccoll.boilerplate.audit.interfaces.api.platform;

import io.naccoll.boilerplate.audit.constant.AuditApiConstant;
import io.naccoll.boilerplate.audit.convert.AuditStateMachineLogConvert;
import io.naccoll.boilerplate.audit.dto.AuditStateMachineLogDto;
import io.naccoll.boilerplate.audit.dto.AuditStateMachineLogQueryCondition;
import io.naccoll.boilerplate.audit.model.AuditStateMachineLogPo;
import io.naccoll.boilerplate.audit.service.AuditStateMachineLogQueryService;
import io.naccoll.boilerplate.audit.service.AuditStateMachineLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 状态机日志管理API控制器 提供状态机日志的分页查询和单条查询接口
 *
 * <AUTHOR>
 */
@RequestMapping(AuditApiConstant.PlatformApiV1.STATE_MACHINE_LOG)
@RestController
@Tag(name = "状态机日志管理")
public class AuditPlatformApiV1StateMachineLog {

	@Resource
	private AuditStateMachineLogService auditStateMachineLogService;

	@Resource
	private AuditStateMachineLogQueryService auditStateMachineLogQueryService;

	@Resource
	private AuditStateMachineLogConvert auditStateMachineLogConvert;

	/**
	 * 分页查询状态机日志
	 * @param condition 查询条件
	 * @param pageable 分页参数
	 * @return 状态机日志分页结果
	 */
	@GetMapping("/page")
	@Operation(summary = "分页查询状态机日志")
	@PreAuthorize("hasPermission(0L,'GLOBAL','audit/state-machine-log:read')")
	public ResponseEntity<Page<AuditStateMachineLogDto>> page(AuditStateMachineLogQueryCondition condition,
			Pageable pageable) {
		Page<AuditStateMachineLogPo> auditStateMachineLogPage = auditStateMachineLogQueryService.page(condition,
				pageable);
		Page<AuditStateMachineLogDto> auditStateMachineLogDtoPage = auditStateMachineLogConvert
			.convertAuditStateMachineLogDtoPage(auditStateMachineLogPage);
		return new ResponseEntity<>(auditStateMachineLogDtoPage, HttpStatus.OK);
	}

	/**
	 * 查询单条状态机日志
	 * @param id 状态机日志ID
	 * @return 状态机日志DTO对象
	 */
	@GetMapping("/{id}")
	@Operation(summary = "查询状态机日志")
	@PreAuthorize("hasPermission(0L,'GLOBAL','audit/state-machine-log:read')")
	public ResponseEntity<AuditStateMachineLogDto> queryOne(@PathVariable Long id) {
		AuditStateMachineLogPo auditStateMachineLogPo = auditStateMachineLogQueryService.findByIdNotNull(id);
		AuditStateMachineLogDto auditStateMachineLogDto = auditStateMachineLogConvert
			.convertAuditStateMachineLogDto(auditStateMachineLogPo);
		return new ResponseEntity<>(auditStateMachineLogDto, HttpStatus.OK);
	}

}
