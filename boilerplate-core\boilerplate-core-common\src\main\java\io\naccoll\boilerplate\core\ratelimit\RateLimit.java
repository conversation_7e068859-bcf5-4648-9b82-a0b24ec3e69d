package io.naccoll.boilerplate.core.ratelimit;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 限流注解，用于方法级别限流控制 支持三种限流模式：令牌桶、漏桶和时间窗口 通过配置不同的参数实现灵活的限流策略
 *
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RateLimit {

	/**
	 * 上锁用的key的前缀，通常是数据记录的类型，但也可以为空
	 * @return 前缀字符串
	 */
	String prefix() default "";

	/**
	 * 上锁用的key，通常是数据记录的id
	 * @return key字符串
	 */
	String key() default "";

	/**
	 * 限流模式 支持三种模式：令牌桶、漏桶和时间窗口
	 * @return 当前使用的限流模式
	 */
	RateLimiterMode mode() default RateLimiterMode.TOKEN_BUCKET;

	/**
	 * 速率配置 根据不同模式有不同的含义： <br>
	 * {@link RateLimiterMode#WINDOWS} 时间窗口内的最大请求次数<br>
	 * {@link RateLimiterMode#LEAK_BUCKET} 漏桶每秒流失的令牌数<br>
	 * {@link RateLimiterMode#TOKEN_BUCKET} 令牌桶每秒添加的令牌数
	 * @return 速率值
	 */
	int rate() default 10;

	/**
	 * {@link RateLimiterMode#LEAK_BUCKET} 漏桶的上限数
	 * <p>
	 * {@link RateLimiterMode#TOKEN_BUCKET} 令牌桶的令牌上限数
	 * @return
	 */
	int capacity() default 60;

	/**
	 * 时间窗口, 仅在{@link RateLimiterMode#WINDOWS}时有效
	 * @return
	 */
	int ttl() default 3;

}
