package io.naccoll.boilerplate.audit.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 数据审计模型创建命令对象，用于创建新的数据审计模型实例。 该对象包含创建数据审计模型所需的所有必要信息。
 *
 * <AUTHOR>
 *
 */
@Data
public class AuditDataModelCreateCommand {

	/**
	 * 数据模型的JSON Schema定义，描述数据模型的结构和字段信息。
	 */
	@Schema(description = "数据模型的JSON Schema定义")
	private String dataSchema;

	/**
	 * 数据模型对应的Java类名称，用于标识和关联具体的业务类。
	 */
	@Schema(description = "数据模型对应的Java类名称")
	private String className;

	/**
	 * 数据模型的唯一标识签名，用于快速校验和识别数据模型。 签名通常通过MD5等加密算法生成。
	 */
	@Schema(description = "数据模型的唯一标识签名")
	private String modelSign;

}
