package io.naccoll.boilerplate.core.utils;

import lombok.extern.slf4j.Slf4j;

import java.util.function.Supplier;

/**
 * 日志计时工具类，用于记录方法执行时间
 *
 * <AUTHOR>
 */
@Slf4j
public class LogTimeUtil {

	/**
	 * 记录Runnable任务的执行时间
	 * @param runnable 要执行的任务
	 */
	public static void logTime(Runnable runnable) {
		logTime("", runnable);
	}

	/**
	 * 记录Runnable任务的执行时间并添加自定义消息
	 * @param msg 自定义消息
	 * @param runnable 要执行的任务
	 */
	public static void logTime(String msg, Runnable runnable) {
		long startTime = System.currentTimeMillis();
		runnable.run();
		long endTime = System.currentTimeMillis();
		log.info(msg + ": " + (endTime - startTime));
	}

	/**
	 * 记录Supplier任务的执行时间并返回结果
	 * @param runnable 要执行的任务
	 * @return 任务执行结果
	 * @param <T> 返回类型
	 */
	public static <T> T logTime(Supplier<T> runnable) {
		return logTime("", runnable);
	}

	/**
	 * 记录Supplier任务的执行时间并添加自定义消息
	 * @param msg 自定义消息
	 * @param runnable 要执行的任务
	 * @return 任务执行结果
	 * @param <T> 返回类型
	 */
	public static <T> T logTime(String msg, Supplier<T> runnable) {
		long startTime = System.currentTimeMillis();
		T result = runnable.get();
		long endTime = System.currentTimeMillis();
		log.info(msg + (endTime - startTime));
		return result;
	}

}
