package io.naccoll.boilerplate.annex.config;

import io.naccoll.boilerplate.core.openapi.springdoc.AbstractWebMvcSpringdocConfig;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 附件管理模块 OpenAPI 文档配置
 *
 * <AUTHOR>
 */
@Configuration
public class AnnexSpringdocConfig extends AbstractWebMvcSpringdocConfig {

	/**
	 * 平台附件管理 API 文档配置
	 */
	@Bean
	public GroupedOpenApi ossPlatformApiDoc() {
		return GroupedOpenApi.builder()
			.group("附件管理-平台")
			.packagesToScan("io.naccoll.boilerplate.annex.interfaces.api.platform")
			.addOpenApiCustomizer(jwtHeaderOpenApiCustomiser())
			.build();
	}

	/**
	 * 用户附件管理 API 文档配置
	 */
	@Bean
	public GroupedOpenApi ossAnnexUserApiDoc() {
		return GroupedOpenApi.builder()
			.group("附件管理-用户")
			.packagesToScan("io.naccoll.boilerplate.annex.interfaces.api.user")
			.addOpenApiCustomizer(jwtHeaderOpenApiCustomiser())
			.build();
	}

}
