package io.naccoll.boilerplate.core.cache.simple;

import io.naccoll.boilerplate.core.cache.CacheTemplate;
import org.springframework.util.Assert;

import java.time.Duration;
import java.util.Set;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * 内存缓存模板实现类
 * <p>
 * 基于内存的缓存实现，提供基本的缓存操作和锁机制支持
 * </p>
 *
 * <AUTHOR>
 */
public class InMemoryCacheTemplateImpl implements CacheTemplate {

	private final InMemoryCacheWriter inMemoryCacheWriter;

	private final String prefix;

	private final Lock[] lockTable;

	private final int mask;

	/**
	 * 构造内存缓存模板
	 * @param inMemoryCacheWriter 内存缓存写入器
	 * @param mask 锁表掩码，用于计算锁索引
	 */
	public InMemoryCacheTemplateImpl(InMemoryCacheWriter inMemoryCacheWriter, int mask) {
		this(inMemoryCacheWriter, mask, "");
	}

	/**
	 * 构造内存缓存模板
	 * @param inMemoryCacheWriter 内存缓存写入器
	 * @param mask 锁表掩码，用于计算锁索引
	 * @param prefix 缓存键前缀
	 */
	public InMemoryCacheTemplateImpl(InMemoryCacheWriter inMemoryCacheWriter, int mask, String prefix) {
		this.inMemoryCacheWriter = inMemoryCacheWriter;
		this.prefix = prefix;
		String bits = Integer.toBinaryString(mask);
		Assert.isTrue(bits.length() < 32 && (mask == 0 || bits.lastIndexOf('0') < bits.indexOf('1')),
				"Mask must be a power of 2 - 1");
		this.mask = mask;
		int arraySize = this.mask + 1;
		this.lockTable = new ReentrantLock[arraySize];
		for (int i = 0; i < arraySize; i++) {
			this.lockTable[i] = new ReentrantLock();
		}
	}

	@Override
	public boolean hasKey(String key) {
		return inMemoryCacheWriter.get(key) == null;
	}

	@Override
	public Set<String> keys(String pattern) {
		return inMemoryCacheWriter.keys(pattern)
			.stream()
			.map(s -> s.replaceFirst(getPrefix(), ""))
			.collect(Collectors.toSet());
	}

	@Override
	public void set(String key, Object obj, Duration duration) {
		key = String.format("%s%s", getPrefix(), key);
		inMemoryCacheWriter.put(key, obj, duration);
	}

	@Override
	public boolean setIfAbsent(String key, Object obj, Duration duration) {
		key = String.format("%s%s", getPrefix(), key);
		return inMemoryCacheWriter.putIfAbsent(key, obj, duration);
	}

	@Override
	public Object get(String key) {
		key = String.format("%s%s", getPrefix(), key);
		return inMemoryCacheWriter.get(key);
	}

	@Override
	public boolean delete(String key) {
		key = String.format("%s%s", getPrefix(), key);
		return inMemoryCacheWriter.remove(key);
	}

	@Override
	public boolean expire(String key, Duration duration) {
		key = String.format("%s%s", getPrefix(), key);
		return inMemoryCacheWriter.expire(key, duration);
	}

	@Override
	public Duration getExpire(String key) {
		key = String.format("%s%s", getPrefix(), key);
		return inMemoryCacheWriter.getExpire(key);
	}

	@Override
	public Long increment(String key) {
		key = String.format("%s%s", getPrefix(), key);
		return inMemoryCacheWriter.increment(key);
	}

	@Override
	public Lock getLock(Object lockKey) {
		int lockIndex = lockKey.hashCode() & this.mask;
		return this.lockTable[lockIndex];
	}

	/**
	 * 获取缓存键前缀
	 * @return 当前使用的缓存键前缀
	 */
	private String getPrefix() {
		return prefix;
	}

}
