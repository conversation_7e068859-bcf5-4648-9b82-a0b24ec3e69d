package io.naccoll.boilerplate.sys.service;

import io.naccoll.boilerplate.sys.dto.SysVariableCreateCommand;
import io.naccoll.boilerplate.sys.dto.SysVariableUpdateCommand;
import io.naccoll.boilerplate.sys.model.SysVariablePo;
import jakarta.validation.Valid;

/**
 * 系统变量服务接口
 *
 * <AUTHOR>
 */
public interface SysVariableService {

	/**
	 * 创建系统变量
	 * @param command 创建系统变量的参数对象
	 * @return 创建成功的系统变量实体
	 */
	SysVariablePo create(@Valid SysVariableCreateCommand command);

	/**
	 * 更新系统变量
	 * @param command 更新系统变量的参数对象
	 * @return 更新后的系统变量实体
	 */
	SysVariablePo update(@Valid SysVariableUpdateCommand command);

	/**
	 * 根据ID删除系统变量
	 * @param id 要删除的系统变量ID
	 */
	void deleteById(Long id);

}
