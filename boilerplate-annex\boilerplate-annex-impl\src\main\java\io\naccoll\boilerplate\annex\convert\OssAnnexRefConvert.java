package io.naccoll.boilerplate.annex.convert;

import io.naccoll.boilerplate.annex.dto.OssAnnexRefDto;
import io.naccoll.boilerplate.annex.model.OssAnnexRefPo;
import io.naccoll.boilerplate.annex.service.OssAnnexQueryService;
import io.naccoll.boilerplate.core.utils.BeanUtils;
import io.naccoll.boilerplate.oss.OssServiceHelper;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 通用附件引用转换器
 *
 * <AUTHOR>
 */
@Component
public class OssAnnexRefConvert {

	@Resource
	private OssServiceHelper ossServiceHelper;

	@Resource
	private OssAnnexQueryService ossAnnexQueryService;

	/**
	 * 将单个附件引用实体转换为DTO
	 * @param po 附件引用实体
	 * @return 转换后的DTO
	 */
	public OssAnnexRefDto convertOssAnnexRefDto(OssAnnexRefPo po) {
		if (po == null) {
			return null;
		}
		return convertOssAnnexRefDtoList(List.of(po)).getFirst();
	}

	/**
	 * 将附件引用实体列表转换为DTO列表
	 * @param list 附件引用实体列表
	 * @return 转换后的DTO列表
	 */
	public List<OssAnnexRefDto> convertOssAnnexRefDtoList(List<OssAnnexRefPo> list) {
		var annexIds = list.stream().map(OssAnnexRefPo::getAnnexId).filter(Objects::nonNull).toList();
		var annexMap = ossAnnexQueryService.findMapByIds(annexIds);

		return list.stream().map(po -> {
			OssAnnexRefDto dto = new OssAnnexRefDto();
			BeanUtils.copyProperties(po, dto);
			var annex = annexMap.get(po.getAnnexId());
			if (annex != null) {
				dto.setAnnexUrl(ossServiceHelper.parseUrl(annex.getAnnexUrl()));
				dto.setSize(annex.getSize());
				dto.setContentType(annex.getContentType());
				dto.setExtName(annex.getExtName());
				dto.setAnnexRemark(annex.getRemark());
			}
			return dto;
		}).toList();
	}

	/**
	 * 将附件引用实体分页转换为DTO分页
	 * @param page 附件引用实体分页
	 * @return 转换后的DTO分页
	 */
	public Page<OssAnnexRefDto> convertOssAnnexRefDtoPage(Page<OssAnnexRefPo> page) {
		List<OssAnnexRefDto> ossAnnexRefList = convertOssAnnexRefDtoList(page.getContent());
		return new PageImpl<>(ossAnnexRefList, page.getPageable(), page.getTotalElements());
	}

}
