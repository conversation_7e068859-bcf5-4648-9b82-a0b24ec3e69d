package io.naccoll.boilerplate.core.exception;

import lombok.Getter;

/**
 * 业务异常枚举类，定义了系统中可能发生的各种业务错误。 每个枚举常量代表一个具体的业务错误类型，包含错误别名、错误代码和HTTP状态码。
 * 该枚举采用不可变设计，确保错误信息的一致性和安全性。
 *
 * <AUTHOR>
 * @see BusinessException
 */
@Getter
public enum BusinessError {

	/**
	 * 客户端通用错误，表示客户端请求包含无效数据或格式错误。 通常用于处理前端传入的无效参数或请求格式问题。
	 */
	GENERAL_CLIENT(Constants.BAD_REQUEST, 40000000, 400),

	/**
	 * 数据关联错误，表示当前操作影响到其他相关数据，无法直接执行。 例如删除一个包含外键约束的数据库记录时会触发此错误。
	 */
	DATA_RELATED(Constants.BAD_REQUEST, 40000001, 400),

	/**
	 * 数据缺失错误，表示请求中缺少必要的数据参数。 通常用于验证必填字段缺失的情况。
	 */
	DATA_NOT_FOUND(Constants.BAD_REQUEST, 40000002, 400),

	/**
	 * 数据库记录属性冲突错误，表示数据库中存在与当前操作冲突的记录。 例如更新记录时发现某些字段与其他记录冲突。
	 */
	DATA_DUPLICATE(Constants.BAD_REQUEST, 40000003, 400),

	/**
	 * 数据参数校验失败错误，表示输入的数据参数未通过业务规则校验。 用于处理数据格式、范围、逻辑等校验失败的情况。
	 */
	DATA_VALIDATION_FAILED(Constants.BAD_REQUEST, 40000004, 400),

	/**
	 * 特殊条件不满足错误，表示操作需要额外确认但未通过。 例如需要二次验证但未通过的情况。
	 */
	NEED_CONFIRM("Need Confirm", 40000005, 400),

	/**
	 * 用户未认证错误，表示用户未登录或认证信息无效。 用于处理未登录用户尝试访问受保护资源的情况。
	 */
	UNAUTHORIZED("Unauthorized", 40100000, 401),

	/**
	 * 权限不足错误，表示用户没有执行当前操作的权限。 用于处理用户尝试访问超出其权限范围的资源。
	 */
	FORBIDDEN("Forbidden", 40300000, 403),

	/**
	 * 请求签名验证失败错误，表示请求签名不正确或已过期。 用于处理API请求签名验证失败的情况。
	 */
	SIGN_ERROR("SIGN ERROR", 40300001, 403),

	/**
	 * 二次确认失败错误，表示需要二次确认的操作未通过。 例如需要短信验证码确认但未通过的情况。
	 */
	TWICE_FORBIDDEN("TWICE FORBIDDEN", 40300002, 403),

	/**
	 * 请求频率超过限制错误，表示用户在单位时间内请求次数过多。 用于实现API的限流控制。
	 */
	RATE_LIMIT(Constants.BAD_REQUEST, 42900001, 429),

	/**
	 * 重复提交错误，表示同一请求被重复提交。 通常用于防止表单重复提交或重复下单等场景。
	 */
	DUPLICATE_SUBMIT(Constants.BAD_REQUEST, 42900002, 429),

	/**
	 * 未定义错误，表示发生了未知的内部错误。 用于捕获和处理未预期的异常情况。
	 */
	UNDEFINED_ERROR("Internal Server Error", 50000000, 500),

	/**
	 * 对象存储服务（OSS）错误，表示与OSS相关的操作失败。 例如文件上传、下载或删除失败。
	 */
	OSS_ERROR("Oss Error", 50000001, 500),

	/**
	 * 短信服务错误，表示与短信相关的操作失败。 例如短信发送失败或配置错误。
	 */
	SMS_ERROR("SMS Error", 50000002, 500),

	/**
	 * 第三方系统错误，表示与第三方服务交互失败。 例如调用外部API失败或响应异常。
	 */
	THIRD_SYSTEM_ERROR("Third System Error", 50000003, 500),

	/**
	 * 第三方系统返回错误，表示第三方服务返回了错误响应。 例如调用外部API返回了非200状态码。
	 */
	THIRD_RETURN_ERROR("Third Return Error", 50000004, 500),;

	/**
	 * 错误别名，用于错误信息的简要描述。 通常用于日志记录和用户提示。
	 */
	private final String alias;

	/**
	 * 错误代码，用于唯一标识一个错误类型。 通常用于系统内部错误处理和日志记录。
	 */
	private final long code;

	/**
	 * HTTP状态码，表示该错误对应的HTTP响应状态码。 用于统一错误处理和API响应。
	 */
	private final int status;

	BusinessError(String alias, long code, int status) {
		this.alias = alias;
		this.code = code;
		this.status = status;
	}

	private static class Constants {

		/**
		 * The constant BAD_REQUEST.
		 */
		public static final String BAD_REQUEST = "Bad Request";

	}

}
