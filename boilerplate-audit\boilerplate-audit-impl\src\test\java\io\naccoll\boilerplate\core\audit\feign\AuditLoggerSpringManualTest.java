package io.naccoll.boilerplate.core.audit.feign;

import feign.*;
import feign.codec.Decoder;
import feign.jackson.JacksonEncoder;
import io.naccoll.boilerplate.core.id.IdService;
import io.naccoll.boilerplate.core.id.IdServiceConfig;
import jakarta.annotation.Resource;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Import;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

import java.util.HashMap;
import java.util.Map;

/**
 * The interface Test spring feign.
 *
 * <AUTHOR>
 */
interface TestSpringFeign {

	/**
	 * Test get string.
	 * @param wd the wd
	 * @return the string
	 */
	@RequestLine("GET /s?wd={wd}")
	@Headers({ "Token:abc" })
	String testGet(@Param String wd);

	/**
	 * Test post string.
	 * @param wd the wd
	 * @param map the map
	 * @return the string
	 */
	@RequestLine("POST /s?wd={wd}")
	@Headers({ "Token:abc" })
	String testPost(@Param String wd, Map<String, Object> map);

}

/**
 * The type Audit logger spring manual test.
 *
 * <AUTHOR>
 */
@SpringBootTest(classes = AuditLoggerSpringManualTest.TestApplication.class)
class AuditLoggerSpringManualTest {

	/**
	 * The Id service.
	 */
	@Resource
	IdService idService;

	/**
	 * The Publisher.
	 */
	@Resource
	ApplicationEventPublisher publisher;

	/**
	 * The Test feign.
	 */
	TestSpringFeign testFeign;

	/**
	 * Sets up.
	 */
	@BeforeEach
	public void setUp() {
		testFeign = Feign.builder()
			.logger(new FeignAuditLogger(idService, publisher))
			.logLevel(Logger.Level.FULL)
			.retryer(Retryer.NEVER_RETRY)
			.encoder(new JacksonEncoder())
			.decoder(new Decoder.Default())
			.target(TestSpringFeign.class, "https://www.baidu.com");
	}

	/**
	 * Test get.
	 */
	@Test
	void testGet() {
		String a = testFeign.testGet("Java");
		Assertions.assertThat(a).isNotEmpty();
	}

	/**
	 * Test post.
	 */
	@Test
	void testPost() {
		try {
			Map<String, Object> map = HashMap.newHashMap(10);
			map.put("keyword", "Java");
			testFeign.testPost("Java", map);
		}
		catch (Exception e) {
			Assertions.assertThat(e).isInstanceOf(RetryableException.class);
		}
	}

	/**
	 * The type Test application.
	 *
	 * <AUTHOR>
	 */
	@EnableJpaRepositories(basePackages = "io.naccoll")
	@SpringBootApplication(scanBasePackages = "io.naccoll")
	@EntityScan(basePackages = "io.naccoll")
	@Import(IdServiceConfig.class)
	static class TestApplication {

		/**
		 * The entry point of application.
		 * @param args the input arguments
		 */
		public static void main(String[] args) {
			SpringApplication.run(TestApplication.class, args);
		}

	}

}
