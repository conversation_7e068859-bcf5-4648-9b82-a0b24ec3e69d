package io.naccoll.boilerplate.cms.config;

import io.naccoll.boilerplate.core.openapi.springdoc.AbstractWebMvcSpringdocConfig;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * CMS SpringDoc配置类
 *
 * 该类用于配置CMS模块的OpenAPI文档，定义了不同的API分组和对应的文档信息
 *
 * <AUTHOR>
 */
@Configuration
public class CmsSpringdocConfig extends AbstractWebMvcSpringdocConfig {

	/**
	 * 内容管理平台API文档配置
	 *
	 * 定义了平台相关的API分组和文档扫描路径
	 * @return GroupedOpenApi 对象
	 */
	@Bean
	public GroupedOpenApi cmsPlatformApiDoc() {
		return GroupedOpenApi.builder()
			.group("内容管理-平台")
			.packagesToScan("io.naccoll.boilerplate.cms.interfaces.api.platform")
			.addOpenApiCustomizer(jwtHeaderOpenApiCustomiser())
			.build();
	}

	/**
	 * 内容管理公开API文档配置
	 *
	 * 定义了公开相关的API分组和文档扫描路径
	 * @return GroupedOpenApi 对象
	 */
	@Bean
	public GroupedOpenApi cmsAdminApiDoc() {
		return GroupedOpenApi.builder()
			.group("内容管理-公开")
			.packagesToScan("io.naccoll.boilerplate.cms.interfaces.api.pub")
			.addOpenApiCustomizer(jwtHeaderOpenApiCustomiser())
			.build();
	}

}
