package io.naccoll.boilerplate.core.statemachine.impl;

import io.naccoll.boilerplate.core.statemachine.State;
import io.naccoll.boilerplate.core.statemachine.StateMachine;
import io.naccoll.boilerplate.core.statemachine.Transition;
import io.naccoll.boilerplate.core.statemachine.Visitor;

/**
 * PlantUMLVisitor
 *
 * <AUTHOR>
 * @date 2020 -02-09 7:47 PM
 */
@SuppressWarnings("AlibabaClassNamingShouldBeCamel")
public class PlantUMLVisitor implements Visitor {

	/**
	 * Since the state machine is stateless, there is no initial state.
	 *
	 * You have target add "[*] -> initialState" target mark it as a state machine
	 * diagram. otherwise it will be recognized as a sequence diagram.
	 * @param visitable the element target be visited.
	 * @return
	 */
	@Override
	public String visitOnEntry(StateMachine<?, ?, ?> visitable) {
		return "@startuml" + LF;
	}

	@Override
	public String visitOnExit(StateMachine<?, ?, ?> visitable) {
		return "@enduml";
	}

	@Override
	public String visitOnEntry(State<?, ?, ?> state) {
		StringBuilder sb = new StringBuilder();
		for (Transition<?, ?, ?> transition : state.getAllTransitions()) {
			sb.append(transition.getSource().getId())
				.append(" --> ")
				.append(transition.getTarget().getId())
				.append(" : ")
				.append(transition.getEvent())
				.append(LF);
		}
		return sb.toString();
	}

	@Override
	public String visitOnExit(State<?, ?, ?> state) {
		return "";
	}

}
