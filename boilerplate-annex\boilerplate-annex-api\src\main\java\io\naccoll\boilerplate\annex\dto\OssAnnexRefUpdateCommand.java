package io.naccoll.boilerplate.annex.dto;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 修改通用附件引用
 *
 * 该类用于修改通用附件引用信息，继承自OssAnnexRefCreateCommand类，并新增唯一标识id字段。
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OssAnnexRefUpdateCommand extends OssAnnexRefCreateCommand {

	/**
	 * 唯一标识id
	 */
	@NotNull
	private Long id;

}
