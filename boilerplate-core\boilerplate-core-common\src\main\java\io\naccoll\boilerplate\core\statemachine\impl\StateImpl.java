package io.naccoll.boilerplate.core.statemachine.impl;

import io.naccoll.boilerplate.core.statemachine.State;
import io.naccoll.boilerplate.core.statemachine.Transition;
import io.naccoll.boilerplate.core.statemachine.Visitor;

import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * StateImpl
 *
 * @param <S> the type parameter
 * @param <E> the type parameter
 * @param <C> the type parameter
 * <AUTHOR>
 * @date 2020 -02-07 11:19 PM
 */
public class StateImpl<S, E, C> implements State<S, E, C> {

	/**
	 * The State id.
	 */
	protected final S stateId;

	private final EventTransitions<S, E, C> eventTransitions = new EventTransitions<>();

	/**
	 * Instantiates a new State.
	 * @param stateId the state id
	 */
	StateImpl(S stateId) {
		this.stateId = stateId;
	}

	@Override
	public Transition<S, E, C> addTransition(E event, State<S, E, C> target, TransitionType transitionType) {
		Transition<S, E, C> newTransition = new TransitionImpl<>();
		newTransition.setSource(this);
		newTransition.setTarget(target);
		newTransition.setEvent(event);
		newTransition.setType(transitionType);

		Debugger.debug("Begin target add new transition: " + newTransition);

		eventTransitions.put(event, newTransition);
		return newTransition;
	}

	@Override
	public List<Transition<S, E, C>> getEventTransitions(E event) {
		return eventTransitions.get(event);
	}

	@Override
	public Collection<Transition<S, E, C>> getAllTransitions() {
		return eventTransitions.allTransitions();
	}

	@Override
	public S getId() {
		return stateId;
	}

	@Override
	public String accept(Visitor visitor) {
		String entry = visitor.visitOnEntry(this);
		String exit = visitor.visitOnExit(this);
		return entry + exit;
	}

	@Override
	public boolean equals(Object anObject) {
		if (anObject instanceof State<?, ?, ?>other) {
			return this.stateId.equals(other.getId());
		}
		return false;
	}

	@Override
	public int hashCode() {
		return Objects.hash(stateId);
	}

	@Override
	public String toString() {
		return stateId.toString();
	}

}
