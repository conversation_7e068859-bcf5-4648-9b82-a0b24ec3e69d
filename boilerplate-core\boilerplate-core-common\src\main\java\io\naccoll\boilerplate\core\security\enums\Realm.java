package io.naccoll.boilerplate.core.security.enums;

import io.naccoll.boilerplate.core.enums.DisplayEnum;

/**
 * 用户域枚举类，用于表示不同用户域类型
 *
 * <AUTHOR>
 */
public enum Realm implements DisplayEnum {

	/**
	 * 无用户域
	 *
	 * 表示没有具体用户域的情况
	 */
	NONE(0, "None"),

	/**
	 * 平台用户域
	 *
	 * 表示平台内部用户
	 */
	PLATFORM(1, "平台"),

	/**
	 * 客户用户域
	 *
	 * 表示终端用户
	 */
	CUSTOMER(3, "C端"),;

	private final Integer id;

	private final String name;

	/**
	 * 用户域构造方法
	 * @param id 用户域ID
	 * @param name 用户域名称
	 */
	Realm(Integer id, String name) {
		this.id = id;
		this.name = name;
	}

	@Override
	public Integer getId() {
		return id;
	}

	@Override
	public String getName() {
		return name;
	}

}
