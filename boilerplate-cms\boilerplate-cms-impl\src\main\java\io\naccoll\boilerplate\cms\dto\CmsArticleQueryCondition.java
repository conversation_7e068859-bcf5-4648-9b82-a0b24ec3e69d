package io.naccoll.boilerplate.cms.dto;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 文章查询条件
 *
 * <AUTHOR>
 */
@Data
@Schema
public class CmsArticleQueryCondition {

	/**
	 * 栏目ID
	 */
	@Schema(description = "栏目id")
	private Long columnId;

	/**
	 * 文章标题
	 */
	@Parameter(description = "标题")
	private String title;

	/**
	 * 文章状态
	 */
	@Parameter(description = "状态")
	private Integer status;

}
