package io.naccoll.boilerplate.core.mvc;

import org.springframework.context.annotation.Configuration;
import org.springframework.format.FormatterRegistry;
import org.springframework.format.datetime.DateFormatter;
import org.springframework.format.datetime.DateFormatterRegistrar;
import org.springframework.format.datetime.standard.DateTimeFormatterRegistrar;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.time.format.DateTimeFormatter;

/**
 * 日期时间参数转换配置类，用于配置日期时间格式的转换规则
 *
 * <AUTHOR>
 */
@Configuration
public class QueryParamDateTimeConvertConfig implements WebMvcConfigurer {

	/**
	 * 注册日期时间格式转换器<br>
	 * 支持格式：yyyy-MM-dd'T'HH:mm:ss.SSSXXX
	 * @param registry 格式注册器
	 */
	@Override
	public void addFormatters(FormatterRegistry registry) {
		DateTimeFormatterRegistrar dateTimeRegistrar = new DateTimeFormatterRegistrar();
		dateTimeRegistrar.setDateFormatter(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSXXX"));
		dateTimeRegistrar.registerFormatters(registry);

		DateFormatterRegistrar dateRegistrar = new DateFormatterRegistrar();
		dateRegistrar.setFormatter(new DateFormatter("yyyy-MM-dd'T'HH:mm:ss.SSSXXX"));
		dateRegistrar.registerFormatters(registry);
	}

}
