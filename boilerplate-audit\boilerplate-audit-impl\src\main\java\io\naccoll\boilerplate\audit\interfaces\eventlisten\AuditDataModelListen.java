package io.naccoll.boilerplate.audit.interfaces.eventlisten;

import io.naccoll.boilerplate.audit.dto.event.DataModelCreateEvent;
import io.naccoll.boilerplate.audit.service.AuditDataModelQueryService;
import io.naccoll.boilerplate.audit.service.AuditDataModelService;
import io.naccoll.boilerplate.core.lock.UseLock;
import jakarta.annotation.Resource;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * 审计数据模型监听器
 *
 * <AUTHOR>
 */
@Component
public class AuditDataModelListen {

	/**
	 * 审计数据模型服务
	 */
	@Resource
	private AuditDataModelService auditDataModelService;

	/**
	 * 审计数据模型查询服务
	 */
	@Resource
	private AuditDataModelQueryService auditDataModelQueryService;

	/**
	 * 处理数据模型创建事件
	 * @param event 数据模型创建事件
	 */
	@EventListener
	@UseLock(prefix = "audit:data-model:create", key = "#event.payload.modelSign")
	public void onDataModelCreate(DataModelCreateEvent event) {
		if (auditDataModelQueryService.findByModelSign(event.getPayload().getModelSign()) == null) {
			auditDataModelService.create(event.getPayload());
		}
	}

}
