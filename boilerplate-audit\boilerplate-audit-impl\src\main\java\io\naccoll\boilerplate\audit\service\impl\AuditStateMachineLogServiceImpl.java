package io.naccoll.boilerplate.audit.service.impl;

import io.naccoll.boilerplate.audit.dao.AuditStateMachineLogDao;
import io.naccoll.boilerplate.audit.dto.AuditStateMachineLogCreateCommand;
import io.naccoll.boilerplate.audit.dto.AuditStateMachineLogUpdateCommand;
import io.naccoll.boilerplate.audit.model.AuditStateMachineLogPo;
import io.naccoll.boilerplate.audit.service.AuditStateMachineLogQueryService;
import io.naccoll.boilerplate.audit.service.AuditStateMachineLogService;
import io.naccoll.boilerplate.core.audit.operate.OperateLog;
import io.naccoll.boilerplate.core.id.IdService;
import io.naccoll.boilerplate.core.utils.BeanUtils;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * 状态机日志服务实现
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AuditStateMachineLogServiceImpl implements AuditStateMachineLogService {

	@Resource
	private AuditStateMachineLogDao auditStateMachineLogDao;

	@Resource
	private AuditStateMachineLogQueryService auditStateMachineLogQueryService;

	@Resource
	private IdService idService;

	@Override
	@OperateLog(value = "新增状态机日志", id = "#result.id", type = "状态机日志")
	@Transactional(rollbackFor = Exception.class)
	public AuditStateMachineLogPo create(@Valid AuditStateMachineLogCreateCommand command) {
		AuditStateMachineLogPo auditStateMachineLog = new AuditStateMachineLogPo();
		BeanUtils.copyProperties(command, auditStateMachineLog);
		auditStateMachineLog.setId(idService.getId());
		return auditStateMachineLogDao.save(auditStateMachineLog);
	}

	@Override
	@OperateLog(value = "修改状态机日志", id = "#command.id", type = "状态机日志")
	@Transactional(rollbackFor = Exception.class)
	public AuditStateMachineLogPo update(@Valid AuditStateMachineLogUpdateCommand command) {
		AuditStateMachineLogPo auditStateMachineLog = auditStateMachineLogQueryService.findByIdNotNull(command.getId());
		BeanUtils.copyProperties(command, auditStateMachineLog);
		return auditStateMachineLogDao.save(auditStateMachineLog);
	}

	@Override
	@OperateLog(value = "删除状态机日志", id = "#id", type = "状态机日志")
	@Transactional(rollbackFor = Exception.class)
	public void deleteById(Long id) {
		auditStateMachineLogDao.deleteById(id);
	}

}
