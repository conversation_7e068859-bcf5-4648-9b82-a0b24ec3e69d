package io.naccoll.boilerplate.core.audit;

import io.naccoll.boilerplate.core.security.enums.ClientId;
import io.naccoll.boilerplate.core.security.enums.Realm;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.stereotype.Component;

/**
 * 默认审计上下文管理实现 <br>
 * 当系统中未配置其他AuditContextManager实现时自动启用
 * <p>
 * 该类提供一个空实现，用于在未配置正式审计用户信息时返回默认值
 *
 * <AUTHOR>
 */
@ConditionalOnMissingBean(AuditContextManager.class)
@Component
public class EmptyAuditContextManagerImpl implements AuditContextManager {

	/**
	 * 获取默认审计用户信息 <br>
	 * 返回一个包含空值的匿名实现，用于系统未配置正式审计用户时
	 * <p>
	 * 匿名实现包含以下默认值：
	 * <ul>
	 * <li>用户ID：0L</li>
	 * <li>用户名：空字符串</li>
	 * <li>安全域：NONE</li>
	 * <li>客户端类型：NONE</li>
	 * </ul>
	 */
	@Override
	@SuppressWarnings("unchecked")
	public AuditUser getAuditUser() {
		return new AuditUser() {
			/**
			 * 返回默认用户ID 0L
			 */
			@Override
			public Long getId() {
				return 0L;
			}

			/**
			 * 返回空字符串作为用户名
			 */
			@Override
			public String getName() {
				return "";
			}

			/**
			 * 返回未配置的安全域枚举
			 */
			@Override
			public Realm getRealm() {
				return Realm.NONE;
			}

			/**
			 * 返回未配置的客户端类型枚举
			 */
			@Override
			public ClientId getClientId() {
				return ClientId.NONE;
			}
		};
	}

}
