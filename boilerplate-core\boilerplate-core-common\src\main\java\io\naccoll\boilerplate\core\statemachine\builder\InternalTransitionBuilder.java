package io.naccoll.boilerplate.core.statemachine.builder;

/**
 * InternalTransitionBuilder
 *
 * @param <S> the type parameter
 * @param <E> the type parameter
 * @param <C> the type parameter
 * <AUTHOR>
 * @date 2020 -02-07 9:39 PM
 */
public interface InternalTransitionBuilder<S, E, C> {

	/**
	 * Build a internal transition
	 * @param stateId id of transition
	 * @return To clause builder
	 */
	Target<S, E, C> within(S stateId);

}
