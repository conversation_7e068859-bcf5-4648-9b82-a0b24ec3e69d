package io.naccoll.boilerplate.audit.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * 状态机触发事件命令类型。
 *
 * <AUTHOR>
 */
@Data
public class StateMachineFireEventCommand {

	/**
	 * 状态机ID
	 */
	@Schema(hidden = true, description = "状态机ID")
	@NotBlank
	private String machineId;

	/**
	 * 目标ID，表示状态机操作的目标实体唯一标识
	 */
	@NotNull
	@Schema(description = "目标ID")
	private Long targetId;

	/**
	 * 事件编码，表示需要触发的状态机事件唯一标识
	 */
	@NotNull
	@Schema(description = "事件编码")
	private Integer event;

	/**
	 * 额外参数，用于传递状态机执行过程中需要的其他相关信息
	 */
	@NotNull
	@Schema(description = "额外参数")
	private Map<String, Object> extraParams = new HashMap<>(10);

}
