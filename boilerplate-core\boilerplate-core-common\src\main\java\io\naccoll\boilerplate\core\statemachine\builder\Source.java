package io.naccoll.boilerplate.core.statemachine.builder;

/**
 * Source
 *
 * @param <S> the type parameter
 * @param <E> the type parameter
 * @param <C> the type parameter
 * <AUTHOR>
 * @date 2020 -02-07 6:13 PM
 */
public interface Source<S, E, C> {

	/**
	 * Build transition target state and return target clause builder
	 * @param stateId id of state
	 * @return To clause builder
	 */
	Target<S, E, C> target(S stateId);

}
