package io.naccoll.boilerplate.core.exception;

import java.io.Serial;

/**
 * 权限不允许
 *
 * <AUTHOR>
 */
public class ForbiddenException extends BusinessException {

	@Serial
	private static final long serialVersionUID = 23413335518709L;

	/**
	 * 创建一个权限异常实例
	 * @param code 错误代码，用于标识具体的错误情况
	 * @param parameters 可变参数，用于提供错误相关的上下文信息
	 */
	public ForbiddenException(String code, Object... parameters) {
		super(code, BusinessError.FORBIDDEN, parameters);
	}

	/**
	 * 创建一个权限异常实例，允许指定具体的业务错误类型
	 * @param code 错误代码，用于标识具体的错误情况
	 * @param error 业务错误类型，提供更详细的错误分类
	 * @param parameters 可变参数，用于提供错误相关的上下文信息
	 */
	public ForbiddenException(String code, BusinessError error, Object... parameters) {
		super(code, error, parameters);
	}

	/**
	 * 创建一个包含原始异常的权限异常实例
	 * @param code 错误代码，用于标识具体的错误情况
	 * @param throwable 原始异常，提供导致该权限异常的错误原因
	 * @param parameters 可变参数，用于提供错误相关的上下文信息
	 */
	public ForbiddenException(String code, Throwable throwable, Object... parameters) {
		super(code, BusinessError.FORBIDDEN, throwable, parameters);
	}

}
