package io.naccoll.boilerplate.cms.enums;

import io.naccoll.boilerplate.core.enums.DisplayEnum;
import io.naccoll.boilerplate.core.enums.EnumHelper;

/**
 * The enum Cms column type.
 *
 * <AUTHOR>
 */
public enum CmsColumnType implements DisplayEnum {

	/**
	 * 不分类
	 */
	COMMON(0, "不分类");

	private final Integer id;

	private final String name;

	CmsColumnType(Integer id, String name) {
		this.id = id;
		this.name = name;
	}

	/**
	 * From id cms column type.
	 * @param id the id
	 * @return the cms column type
	 */
	public static CmsColumnType fromId(int id) {
		return EnumHelper.fromId(CmsColumnType.class, id);
	}

	@Override
	public Integer getId() {
		return id;
	}

	@Override
	public String getName() {
		return name;
	}

}
