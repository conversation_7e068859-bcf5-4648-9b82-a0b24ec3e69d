package io.naccoll.boilerplate.core.security.service;

import io.naccoll.boilerplate.core.security.enums.SecurityDataEncodeAlgorithm;
import io.naccoll.boilerplate.core.security.enums.SecurityDataType;

/**
 * 数据安全服务接口，提供数据加密解密功能
 *
 * <AUTHOR>
 */
public interface DataSecurityService {

	/**
	 * 加密字符串
	 * @param origin 原始字符串
	 * @param securityDataType 安全数据类型
	 * @return 加密后的字符串
	 */
	String encryptStr(String origin, SecurityDataType securityDataType);

	/**
	 * 使用指定算法加密字符串
	 * @param origin 原始字符串
	 * @param securityDataType 安全数据类型
	 * @param algorithm 加密算法
	 * @return 加密后的字符串
	 */
	String encryptStr(String origin, SecurityDataType securityDataType, SecurityDataEncodeAlgorithm algorithm);

	/**
	 * 加密字节数组
	 * @param origin 原始字节数组
	 * @param securityDataType 安全数据类型
	 * @return 加密后的字节数组
	 */
	byte[] encrypt(byte[] origin, SecurityDataType securityDataType);

	/**
	 * 解密字符串
	 * @param encodeStr 加密字符串
	 * @return 解密后的原始字符串
	 */
	String decryptStr(String encodeStr);

	/**
	 * 使用指定算法解密字符串
	 * @param encodeStr 加密字符串
	 * @param algorithm 加密算法
	 * @return 解密后的原始字符串
	 */
	String decryptStr(String encodeStr, SecurityDataEncodeAlgorithm algorithm);

	/**
	 * 解密字节数组
	 * @param encode 加密字节数组
	 * @return 解密后的原始字节数组
	 */
	byte[] decode(byte[] encode);

	/**
	 * 获取默认加密算法
	 * @return 默认加密算法
	 */
	SecurityDataEncodeAlgorithm getDefaultAlgorithm();

	/**
	 * 加密电话号码
	 * @param tel 电话号码
	 * @return 加密后的电话号码
	 */
	String encryptPhone(String tel);

	/**
	 * 加密身份证号
	 * @param idCard 身份证号
	 * @return 加密后的身份证号
	 */
	String encryptIdCard(String idCard);

	/**
	 * 加密访问密钥
	 * @param accessSecret 访问密钥
	 * @return 加密后的访问密钥
	 */
	String encryptAccessSecret(String accessSecret);

	/**
	 * 解密并隐藏电话号码
	 * @param tel 加密的电话号码
	 * @return 解密并隐藏后的电话号码
	 */
	String decryptAndHidePhone(String tel);

	/**
	 * 解密并隐藏身份证号
	 * @param idCard 加密的身份证号
	 * @return 解密并隐藏后的身份证号
	 */
	String decryptAndHideIdCard(String idCard);

	/**
	 * 解密访问密钥并隐藏
	 * @param accessSecret 加密的访问密钥
	 * @return 解密并隐藏后的访问密钥
	 */
	String decryptAndHideAccessSecret(String accessSecret);

}
