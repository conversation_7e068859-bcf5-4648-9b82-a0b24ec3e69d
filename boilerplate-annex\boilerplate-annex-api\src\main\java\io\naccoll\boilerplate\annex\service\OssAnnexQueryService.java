package io.naccoll.boilerplate.annex.service;

import io.naccoll.boilerplate.annex.dto.OssAnnexQueryCondition;
import io.naccoll.boilerplate.annex.model.OssAnnexPo;
import io.naccoll.boilerplate.oss.config.OssType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 通用附件查询服务接口
 *
 * 提供通用附件的查询功能，包括分页查询、条件查询、根据ID查询等。
 *
 * <AUTHOR>
 */
public interface OssAnnexQueryService {

	/**
	 * 根据查询条件分页查询附件数据
	 * @param condition 查询条件
	 * @param pageable 分页参数
	 * @return 包含附件数据的分页结果
	 */
	Page<OssAnnexPo> page(OssAnnexQueryCondition condition, Pageable pageable);

	/**
	 * 根据查询条件查询所有附件数据（不分页）
	 * @param condition 查询条件
	 * @return 附件数据列表
	 */
	List<OssAnnexPo> findAll(OssAnnexQueryCondition condition);

	/**
	 * 根据附件ID查询单个附件信息
	 * @param id 附件ID
	 * @return 附件信息，若未找到返回null
	 */
	OssAnnexPo findById(Long id);

	/**
	 * 根据附件ID查询单个附件信息，若不存在则抛出异常
	 * @param id 附件ID
	 * @return 附件信息
	 * @throws ResourceNotFoundException 如果附件不存在
	 */
	OssAnnexPo findByIdNotNull(Long id);

	/**
	 * 根据附件ID集合批量查询附件信息
	 * @param ids 附件ID集合
	 * @return 附件信息列表
	 */
	List<OssAnnexPo> findByIds(Collection<Long> ids);

	/**
	 * 根据附件ID集合批量查询附件信息并返回Map
	 * @param ids 附件ID集合
	 * @return 附件信息Map，键为附件ID，值为附件信息
	 */
	Map<Long, OssAnnexPo> findMapByIds(Collection<Long> ids);

	/**
	 * 根据附件签名、存储类型和桶名查询附件，避免重复上传
	 * @param annexSign 附件签名
	 * @param ossStore 存储类型
	 * @param bucketName 桶名
	 * @return 已存在的附件信息，若不存在返回null
	 */
	OssAnnexPo findNoRepeat(String annexSign, OssType ossStore, String bucketName);

}
