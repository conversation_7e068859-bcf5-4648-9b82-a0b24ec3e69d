package io.naccoll.boilerplate.core.ratelimit;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

/**
 * <AUTHOR>
 */
@Data
@Validated
@ConfigurationProperties("custom.ratelimit")
public class RateLimiterProperties {

	/**
	 * 限流模式配置
	 * <p>
	 * 支持的模式包括：TOKEN_BUCKET（令牌桶）、LEAK_BUCKET（漏桶）、WINDOWS（时间窗口）
	 * </p>
	 */
	private RateLimiterMode mode = RateLimiterMode.TOKEN_BUCKET;

	/**
	 * 限流键解析器Bean名称
	 * <p>
	 * 用于解析限流键的Bean名称，必须指定有效Bean名称
	 * </p>
	 */
	@NotNull
	private String resolver = "ipRateLimitKeyResolver";

	/**
	 * 时间窗口长度（秒）- 仅WINDOWS模式有效
	 * <p>
	 * 设置时间窗口的长度，单位为秒
	 * </p>
	 */
	@Min(0)
	private int ttl = 3;

	/**
	 * 速率配置
	 * <p>
	 * 根据不同模式配置：<br>
	 * - WINDOWS：时间窗口内最大请求数<br>
	 * - LEAK_BUCKET：漏桶每秒流失令牌数<br>
	 * - TOKEN_BUCKET：令牌桶每秒添加令牌数
	 * </p>
	 */
	@Min(1)
	private int rate = 10;

	/**
	 * 容量配置
	 * <p>
	 * 根据不同模式配置：<br>
	 * - LEAK_BUCKET：漏桶容量<br>
	 * - TOKEN_BUCKET：令牌桶容量
	 * </p>
	 */
	@Min(0)
	private int capacity = 60;

}
