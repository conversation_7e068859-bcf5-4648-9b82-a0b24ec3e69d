package io.naccoll.boilerplate.audit.model;

import io.naccoll.boilerplate.core.persistence.model.JpaAuditable;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

/**
 * 数据审计模型
 *
 * 该类用于存储和管理数据模型的审计信息，包括数据模型的元数据和版本信息。
 *
 * <AUTHOR>
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_audit_data_model")
public class AuditDataModelPo extends JpaAuditable implements Serializable {

	@Serial
	private static final long serialVersionUID = 1845475725726208000L;

	/**
	 * 主键ID，用于唯一标识数据模型记录
	 */
	@Id
	@Schema(description = "数据模型记录的唯一标识")
	private Long id;

	/**
	 * 数据模型的JSON Schema定义，描述数据模型的结构和字段信息
	 */
	@Schema(description = "数据模型的JSON Schema定义")
	private String dataSchema;

	/**
	 * 对应的Java类名，用于标识数据模型的具体实现类
	 */
	@Schema(description = "对应的Java类名")
	private String className;

	/**
	 * 数据模型的唯一标识签名，用于快速校验数据模型的一致性
	 */
	@Schema(description = "数据模型的唯一标识签名")
	private String modelSign;

}
