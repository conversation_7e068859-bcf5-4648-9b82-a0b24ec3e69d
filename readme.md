# 脚手架项目

## 项目概述

本项目是一个基于Spring Boot的企业级开发脚手架，集成了常用功能模块和最佳实践，帮助开发者快速构建企业级应用。

## 主要功能

- 系统管理模块
- 内容管理模块
- 支付模块（支持支付宝、微信支付）
- 对象存储模块（支持阿里云、腾讯云、MinIO、本地存储）
- 消息通知模块（邮件、短信）
- 应用监控
- 代码生成器
- 审计日志

## 环境描述

- Java 21
- PostgreSQL 17.4 (建议)
- MySQL 8.4.5 (可选)
- Redis 7.4.3
- SpringBoot3.5.3

可考虑使用[docker-compose.yml](resources/component/docker-compose.yml)启动PostgreSQL和Redis

## 快速开始

### 1. 数据库初始化

#### MySQL

```sql
CREATE USER 'boilerplate'@'%' IDENTIFIED BY 'boilerplate';
CREATE DATABASE boilerplate DEFAULT CHARACTER SET utf8mb4 DEFAULT COLLATE utf8mb4_unicode_ci;
GRANT ALL PRIVILEGES ON boilerplate.* TO boilerplate@'%';
CREATE DATABASE boilerplate_test DEFAULT CHARACTER SET utf8mb4 DEFAULT COLLATE utf8mb4_unicode_ci;
GRANT ALL PRIVILEGES ON boilerplate_test.* TO boilerplate@'%';
FLUSH PRIVILEGES;
```

#### PostgreSQL

```sql
create user boilerplate with password 'boilerplate';
create database boilerplate owner boilerplate;
grant all privileges on database boilerplate to boilerplate;
```

### 2. 数据初始化

`boilerplate-sys/src/main/resources/init-data`目录下存放项目基础的数据初始化CSV

初始用户信息：

- 用户名：boilerplate
- 密码：Boilerplate123!

**注意**：当前初始化数据的CSV兼容PostgreSQL，如需在MySQL使用，需将true改为1，false改为0。

## 项目模块

├── boilerplate-annex
│   ├── boilerplate-annex-api
│   ├── boilerplate-annex-impl
├── boilerplate-audit
│   ├── boilerplate-audit-api
│   ├── boilerplate-audit-impl
├── boilerplate-cms
│   ├── boilerplate-cms-api
│   ├── boilerplate-cms-impl
├── boilerplate-core
│   ├── boilerplate-core-common
│   ├── boilerplate-core-integrate
│   ├── boilerplate-core-persistence
│   ├── boilerplate-core-redis
│   ├── boilerplate-core-security
│   ├── boilerplate-core-web
├── boilerplate-customer
│   ├── boilerplate-customer-all
│   ├── boilerplate-customer-api
│   ├── boilerplate-customer-wx-miniapp
│   ├── boilerplate-customer-wx-mp
├── boilerplate-generator
├── boilerplate-messenger
├── boilerplate-monitor
│   ├── boilerplate-codecentric-admin
│   ├── boilerplate-codecentric-client
│   ├── boilerplate-monitor-all
├── boilerplate-ocr
├── boilerplate-oss
│   ├── boilerplate-oss-aliyun
│   ├── boilerplate-oss-all
│   ├── boilerplate-oss-api
│   ├── boilerplate-oss-local
│   ├── boilerplate-oss-localfile
│   ├── boilerplate-oss-minio
│   ├── boilerplate-oss-tencent
├── boilerplate-payment
│   ├── boilerplate-payment-alipay
│   ├── boilerplate-payment-all
│   ├── boilerplate-payment-api
│   ├── boilerplate-payment-wechat
├── boilerplate-server
├── boilerplate-sys
│   ├── boilerplate-sys-api
│   ├── boilerplate-sys-impl
│   ├── boilerplate-sys-wx-miniapp
│   ├── boilerplate-sys-wx-mp
├── boilerplate-thirdparty
│   ├── boilerplate-thirdparty-api
│   ├── boilerplate-wx-miniapp
│   ├── boilerplate-wx-mp
├── boilerplate-workflow
│   ├── boilerplate-workflow-api
│   ├── boilerplate-workflow-impl

api模块一般存放实体，DTO跟Service接口层等可以给外部模块引用的部分
impl模块一般存放Controller，Service实现，Repository以及其他业务逻辑实现

## 代码规范

本项目使用[spring-javaformat](https://github.com/spring-io/spring-javaformat)代码规范。

### 格式化代码

#### Maven

```bash
./mvnw spring-javaformat:apply
```

#### Gradle

```bash
./gradlew format
```

### 配置Git钩子

```bash
# Maven
./mvnw -N git-build-hook:configure

# Gradle
./gradlew clean bootJar
```

配置后，Git钩子目录为`gradle/git-hooks`或`.mvn/git-hooks`，commit前会自动格式化代码。

## 代码生成器

在`boilerplate-generator`模块中，创建`LocalGeneratorTest`测试用例：

```java
package io.naccoll.boilerplate;

import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.LinkedList;

@SpringBootTest
@ExtendWith(SpringExtension.class)
class LocalGeneratorManualTest {

    @Resource
    private LocalGenerator localGenerator;

    @Test
    void generate() {
        var commands = new LinkedList<LocalGenerateCommand>();
        commands.add(LocalGenerateCommand.builder()
            .apiAlias("编码规则")
            .tableName("t_customer_user")
            .build());
        
        for (LocalGenerateCommand command : commands) {
            command.setAuthor("NaccOll");
            command.setPrefix("t_");
            command.setApiModule("sys");
            command.setModuleName("boilerplate-sys");
            command.setPack("io.naccoll.boilerplate.customer");
            command.setHasPermission(true);
            localGenerator.generate(command);
        }
    }
}
```

该用例已被gitignore忽略，避免开发者本地冲突

此外因ManualTest后缀的测试用例为人工执行测试用例，已经被排除在打包的测试环境外，所以本地执行时，
需要临时将LocalGeneratorManualTest更名为LocalGeneratorTest，执行完成后再改回来

### 代码生成器参数

- pack: 生成代码package的前缀地址
- apiAlias: 生成代码管理对象
- apiModule: api前缀
- tableName: 生成代码使用的表名
- author: 作者
- moduleName: 代码所属的模块
- prefix: 要移除的表前缀
- hasPermission: 是否生成权限代码
