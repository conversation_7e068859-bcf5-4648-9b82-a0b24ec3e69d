package io.naccoll.boilerplate.core.statemachine.builder;

import io.naccoll.boilerplate.core.statemachine.impl.StateMachineException;

/**
 * <PERSON>ert fail callback, throw an {@code TransitionFailException}
 *
 * @param <S> the type parameter
 * @param <E> the type parameter
 * @param <C> the type parameter
 * <AUTHOR>
 * @date 2022 /9/15 12:02 PM
 */
public class AlertFailCallback<S, E, C> implements FailCallback<S, E, C> {

	@Override
	public void onFail(S sourceState, E event, C context) {
		throw new StateMachineException("Cannot fire event [" + event + "] event current state [" + sourceState
				+ "] with context [" + context + "]");
	}

}
