package io.naccoll.boilerplate.core.persistence.jpa;

import io.naccoll.boilerplate.core.persistence.dao.BaseDaoImpl;
import org.springframework.aop.scope.ScopedProxyUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.jdbc.datasource.LazyConnectionDataSourceProxy;

import javax.sql.DataSource;

/**
 * JPA配置类，用于配置Spring Data JPA仓库、实体扫描和审计功能 配置功能包括： 1. 启用JPA仓库支持，指定仓库接口扫描包 2. 配置实体类扫描包 3.
 * 启用JPA审计功能 4. 配置LazyConnectionDataSourceProxy以优化数据库连接
 *
 * <AUTHOR>
 */
@Configuration
@EnableJpaRepositories(basePackages = { "io.naccoll.boilerplate" }, repositoryBaseClass = BaseDaoImpl.class)
@EntityScan(basePackages = "io.naccoll.boilerplate")
@EnableJpaAuditing
public class JpaConfig {

	/**
	 * 创建LazyConnectionDataSourceProxyBeanPostProcessor实例 该处理器用于优化数据库连接代理，提升性能
	 * 通过代理DataSource实现延迟连接，减少不必要的数据库连接开销
	 * @return LazyConnectionDataSourceProxyBeanPostProcessor实例
	 */
	@Bean
	public LazyConnectionDataSourceProxyBeanPostProcessor lazyConnectionDataSourceProxyBeanPostProcessor() {
		return new LazyConnectionDataSourceProxyBeanPostProcessor();
	}

	/**
	 * 数据源代理处理器，用于优化数据库连接管理 实现BeanPostProcessor接口，用于处理数据源代理 实现Ordered接口，定义处理顺序
	 * 该处理器会在Bean初始化后进行代理包装
	 */
	public static class LazyConnectionDataSourceProxyBeanPostProcessor implements BeanPostProcessor, Ordered {

		@Override
		public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
			return bean;
		}

		/**
		 * 在Bean初始化后进行处理，为DataSource创建LazyConnectionDataSourceProxy代理
		 * 该代理会在实际使用时才建立数据库连接，提升性能
		 * @param bean 被处理的Bean实例
		 * @param beanName 被处理的Bean名称
		 * @return 处理后的Bean实例
		 */
		@Override
		public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
			if (bean instanceof DataSource dataSource && !ScopedProxyUtils.isScopedTarget(beanName)) {
				return new LazyConnectionDataSourceProxy(dataSource);
			}
			return bean;
		}

		/**
		 * 定义处理顺序，确保该处理器在较低优先级执行
		 * @return 处理顺序值
		 */
		@Override
		public int getOrder() {
			return Ordered.LOWEST_PRECEDENCE - 19;
		}

	}

}
