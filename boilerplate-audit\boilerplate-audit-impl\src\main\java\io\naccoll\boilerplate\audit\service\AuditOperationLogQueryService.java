package io.naccoll.boilerplate.audit.service;

import io.naccoll.boilerplate.audit.dao.AuditOperationLogDao;
import io.naccoll.boilerplate.audit.dto.AuditOperationLogQueryCondition;
import io.naccoll.boilerplate.audit.model.AuditOperationLogPo;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.web.client.ResourceAccessException;

import java.util.Optional;

/**
 * 操作日志查询服务
 *
 * <AUTHOR>
 */
@Service
public class AuditOperationLogQueryService {

	@Resource
	private AuditOperationLogDao auditOperationLogDao;

	/**
	 * 分页查询操作日志
	 * @param pageable 分页参数
	 * @param condition 查询条件
	 * @return 操作日志分页结果
	 */
	public Page<AuditOperationLogPo> queryPage(Pageable pageable, AuditOperationLogQueryCondition condition) {
		return auditOperationLogDao.page(condition, pageable);
	}

	/**
	 * 根据ID查询操作日志
	 * @param id 操作日志ID
	 * @return 操作日志对象
	 */
	public AuditOperationLogPo findById(Long id) {
		return auditOperationLogDao.findById(id).orElse(null);
	}

	/**
	 * 根据ID查询操作日志（不为空）
	 * @param id 操作日志ID
	 * @return 操作日志对象
	 * @throws ResourceAccessException 如果操作日志不存在
	 */
	public AuditOperationLogPo findByIdNotNull(Long id) {
		return Optional.ofNullable(findById(id)).orElseThrow(() -> new ResourceAccessException("审计日志不存在"));
	}

}
