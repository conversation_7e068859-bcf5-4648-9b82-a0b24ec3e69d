package io.naccoll.boilerplate.annex.service.impl;

import io.naccoll.boilerplate.annex.dao.OssAnnexRefDao;
import io.naccoll.boilerplate.annex.dto.OssAnnexRefQueryCondition;
import io.naccoll.boilerplate.annex.model.OssAnnexPo;
import io.naccoll.boilerplate.annex.model.OssAnnexRefPo;
import io.naccoll.boilerplate.annex.service.OssAnnexQueryService;
import io.naccoll.boilerplate.annex.service.OssAnnexRefQueryService;
import io.naccoll.boilerplate.core.exception.ResourceNotFoundException;
import io.naccoll.boilerplate.core.persistence.dao.DaoUtils;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 通用附件引用查询服务实现
 *
 * <AUTHOR>
 */
@Service
public class OssAnnexRefQueryServiceImpl implements OssAnnexRefQueryService {

	@Resource
	private OssAnnexRefDao ossAnnexRefDao;

	@Resource
	private OssAnnexQueryService ossAnnexQueryService;

	@Override
	public Page<OssAnnexRefPo> page(OssAnnexRefQueryCondition condition, Pageable pageable) {
		var page = ossAnnexRefDao.page(condition, pageable);
		fillWithAnnex(page.getContent(), condition.getWithAnnex());
		return page;
	}

	@Override
	public List<OssAnnexRefPo> findAll(OssAnnexRefQueryCondition condition) {
		var list = ossAnnexRefDao.findAll(condition);
		fillWithAnnex(list, condition.getWithAnnex());
		return list;
	}

	/**
	 * 填充附件信息
	 * @param list 附件引用列表
	 * @param withAnnex 是否包含附件信息
	 */
	private void fillWithAnnex(List<OssAnnexRefPo> list, Boolean withAnnex) {
		if (!CollectionUtils.isEmpty(list) && Boolean.TRUE.equals(withAnnex)) {
			List<Long> annexIds = list.stream().map(OssAnnexRefPo::getAnnexId).distinct().toList();
			Map<Long, OssAnnexPo> annexMap = ossAnnexQueryService.findMapByIds(annexIds);
			list.forEach(item -> {
				OssAnnexPo annex = annexMap.get(item.getAnnexId());
				if (annex != null) {
					item.setAnnexUrl(annex.getAnnexUrl());
					item.setContentType(annex.getContentType());
					item.setSize(annex.getSize());
					item.setExtName(annex.getExtName());
					item.setAnnexRemark(annex.getRemark());
				}
			});
		}
	}

	@Override
	public OssAnnexRefPo findById(Long id) {
		return ossAnnexRefDao.findById(id).orElse(null);
	}

	@Override
	public OssAnnexRefPo findByIdNotNull(Long id) {
		return Optional.ofNullable(findById(id)).orElseThrow(() -> new ResourceNotFoundException("通用附件引用不存在"));
	}

	@Override
	public List<OssAnnexRefPo> findByIds(Collection<Long> ids) {
		return DaoUtils.findByPrimaryKeyIn(ossAnnexRefDao, ids);
	}

	@Override
	public Map<Long, OssAnnexRefPo> findMapByIds(Collection<Long> ids) {
		return DaoUtils.findMapByPrimaryKeyIn(ossAnnexRefDao, ids);
	}

	@Override
	public OssAnnexRefPo findUniq(Long annexId, String targetType, String targetId, String annexGroup) {
		return ossAnnexRefDao.findFirstByTargetIdAndTargetTypeAndAnnexIdAndAnnexGroup(targetId, targetType, annexId,
				annexGroup);
	}

}
