package io.naccoll.boilerplate.cms.dto;

import io.naccoll.boilerplate.cms.model.CmsColumnPo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 栏目数据传输对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CmsColumnDto extends CmsColumnPo {

	/**
	 * 子栏目列表
	 */
	private List<CmsColumnDto> children;

	/**
	 * 栏目类型名称
	 */
	private String typeName;

}
