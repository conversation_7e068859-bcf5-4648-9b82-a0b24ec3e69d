package io.naccoll.boilerplate.core.statemachine.builder;

/**
 * ExternalTransitionBuilder
 *
 * @param <S> the type parameter
 * @param <E> the type parameter
 * @param <C> the type parameter
 * <AUTHOR>
 * @date 2020 -02-07 6:11 PM
 */
public interface ExternalTransitionBuilder<S, E, C> {

	/**
	 * Build transition source state.
	 * @param stateId id of state
	 * @return source clause builder
	 */
	Source<S, E, C> source(S stateId);

}
