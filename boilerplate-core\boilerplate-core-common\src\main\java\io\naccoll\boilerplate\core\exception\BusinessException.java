package io.naccoll.boilerplate.core.exception;

import cn.hutool.extra.spring.SpringUtil;
import io.naccoll.boilerplate.core.i18n.LanguageHelper;
import lombok.Getter;

import java.io.Serial;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 业务异常基类，用于处理业务逻辑错误。 该类提供统一的异常处理机制，支持国际化消息和错误码。
 *
 * <AUTHOR>
 */
@Getter
public class BusinessException extends RuntimeException {

	@Serial
	private static final long serialVersionUID = 2348912758348709L;

	/**
	 * 异常参数，用于传递错误信息中的可变部分。
	 */
	private final transient Object[] parameters;

	/**
	 * 错误消息的代码，用于国际化消息的查找。
	 */
	private final String messageCode;

	/**
	 * 业务错误类型，用于区分不同的业务错误。
	 */
	private final BusinessError error;

	public BusinessException(String messageCode, Object... parameters) {
		super(messageCode);
		this.parameters = parameters;
		this.messageCode = messageCode;
		this.error = BusinessError.UNDEFINED_ERROR;
	}

	/**
	 * 创建带有业务错误类型的业务异常。
	 * @param messageCode 错误码
	 * @param error 业务错误类型
	 * @param parameters 错误参数
	 */
	public BusinessException(String messageCode, BusinessError error, Object... parameters) {
		super(messageCode);
		this.parameters = parameters;
		this.messageCode = messageCode;
		this.error = error;
	}

	public BusinessException(String messageCode, Throwable cause, Object... parameters) {
		super(messageCode, cause);
		this.parameters = parameters;
		this.messageCode = messageCode;
		this.error = BusinessError.UNDEFINED_ERROR;
	}

	/**
	 * 创建带有原因的业务异常。
	 * @param messageCode 错误码
	 * @param cause 原始异常
	 * @param error 业务错误类型
	 * @param parameters 错误参数
	 */
	public BusinessException(String messageCode, BusinessError error, Throwable cause, Object... parameters) {
		super(messageCode, cause);
		this.parameters = parameters;
		this.messageCode = messageCode;
		this.error = error;
	}

	/**
	 * 将异常信息转换为Map结构。
	 * @return 包含错误码和消息的Map对象
	 */
	public Map<String, Object> toMap() {
		HashMap<String, Object> map = new LinkedHashMap<>();
		map.put("messageCode", messageCode);
		map.put("message", super.getMessage());
		return map;
	}

	public String toI18nString() {
		LanguageHelper languageHelper = SpringUtil.getBean(LanguageHelper.class);
		return languageHelper.getMessage(messageCode, parameters);
	}

}
