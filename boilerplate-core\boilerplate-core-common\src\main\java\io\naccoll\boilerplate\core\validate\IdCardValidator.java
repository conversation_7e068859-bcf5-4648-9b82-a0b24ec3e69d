package io.naccoll.boilerplate.core.validate;

import cn.hutool.core.util.IdcardUtil;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

/**
 * 身份证号码验证器实现类
 * <p>
 * 基于hutool的身份证校验工具实现，支持15位和18位身份证号码验证
 * </p>
 *
 * <AUTHOR>
 */
public class IdCardValidator implements ConstraintValidator<IdCard, Object> {

	/**
	 * 验证身份证号码
	 * @param idCard 身份证号码
	 * @param constraintValidatorContext 约束验证上下文
	 * @return boolean 验证结果，true表示有效，false表示无效
	 */
	@Override
	public boolean isValid(Object idCard, ConstraintValidatorContext constraintValidatorContext) {
		if (ObjectUtils.isEmpty(idCard) || !StringUtils.hasText(idCard.toString())) {
			return true;
		}
		return IdcardUtil.isValidCard(idCard.toString());
	}

}
