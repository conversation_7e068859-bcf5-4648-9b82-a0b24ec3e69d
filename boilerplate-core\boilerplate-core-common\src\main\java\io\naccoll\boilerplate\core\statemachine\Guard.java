package io.naccoll.boilerplate.core.statemachine;

/**
 * Guard
 *
 * @param <C> the type parameter
 * <AUTHOR>
 */
public interface Guard<C> {

	/**
	 * Is satisfied boolean.
	 * @param context context object
	 * @return whether the context satisfied current condition
	 */
	boolean isSatisfied(C context);

	/**
	 * Name string.
	 * @return the string
	 */
	default String name() {
		return this.getClass().getSimpleName();
	}

}
