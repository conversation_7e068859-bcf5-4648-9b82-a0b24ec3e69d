package io.naccoll.boilerplate.core.exception;

import java.io.Serial;

/**
 * 资源未找到异常
 * <p>
 * 当请求的资源不存在时抛出
 * </p>
 *
 * <AUTHOR>
 */
public class ResourceNotFoundException extends BusinessException {

	@Serial
	private static final long serialVersionUID = 2348912758348609L;

	/**
	 * 构造资源未找到异常
	 * <p>
	 * 使用指定的错误代码和参数创建异常实例
	 * </p>
	 * @param code 错误代码
	 * @param parameters 错误参数
	 */
	public ResourceNotFoundException(String code, Object... parameters) {
		super(code, BusinessError.DATA_NOT_FOUND, parameters);
	}

	/**
	 * 构造资源未找到异常（带原始异常）
	 * <p>
	 * 使用指定的错误代码、原始异常和参数创建异常实例
	 * </p>
	 * @param code 错误代码
	 * @param throwable 原始异常
	 * @param parameters 错误参数
	 */
	public ResourceNotFoundException(String code, Throwable throwable, Object... parameters) {
		super(code, BusinessError.DATA_NOT_FOUND, throwable, parameters);
	}

}
