package io.naccoll.boilerplate.cms.dto;

import io.naccoll.boilerplate.cms.enums.CmsColumnStatus;
import io.naccoll.boilerplate.core.validate.IsIdableEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 栏目更新命令
 *
 * <AUTHOR>
 */
@Data
public class CmsColumnUpdateCommand {

	/**
	 * 栏目ID
	 */
	@Schema(description = "栏目id")
	private Long id;

	/**
	 * 栏目状态
	 */
	@Schema(description = "栏目状态 0:禁用 1:启用")
	@IsIdableEnum(support = CmsColumnStatus.class)
	private Integer status;

	/**
	 * 栏目描述
	 */
	@Schema(description = "栏目描述")
	private String description;

}
