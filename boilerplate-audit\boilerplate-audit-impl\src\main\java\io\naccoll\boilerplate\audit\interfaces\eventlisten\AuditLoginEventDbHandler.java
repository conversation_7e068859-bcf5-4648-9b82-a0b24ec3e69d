package io.naccoll.boilerplate.audit.interfaces.eventlisten;

import io.naccoll.boilerplate.audit.config.AuditLogProperties;
import io.naccoll.boilerplate.audit.service.AuditLoginLogService;
import io.naccoll.boilerplate.core.audit.enums.AuditStorageType;
import io.naccoll.boilerplate.core.audit.login.LoginSuccessEvent;
import jakarta.annotation.Resource;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 审计登录事件数据库处理器
 *
 * <AUTHOR>
 */
@Component
@EnableConfigurationProperties(AuditLogProperties.class)
public class AuditLoginEventDbHandler {

	/**
	 * 审计登录日志服务
	 */
	@Resource
	private AuditLoginLogService auditLoginLogService;

	/**
	 * 审计日志配置属性
	 */
	@Resource
	private AuditLogProperties auditLogProperties;

	/**
	 * 处理登录成功事件
	 * @param event 登录成功事件
	 */
	@EventListener
	@Async
	public void onLoginSuccess(LoginSuccessEvent event) {
		AuditLogProperties.Login login = auditLogProperties.getLogin();
		if (login.isEnabled() && login.getType().contains(AuditStorageType.DB)) {
			auditLoginLogService.loginSuccess(event);
		}
	}

}
