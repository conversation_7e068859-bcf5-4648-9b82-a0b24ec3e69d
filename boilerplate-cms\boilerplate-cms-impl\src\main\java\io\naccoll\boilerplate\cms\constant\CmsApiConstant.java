package io.naccoll.boilerplate.cms.constant;

/**
 * CMS API常量类
 *
 * 该类定义了CMS模块所有API的常量路径和相关配置
 *
 * <AUTHOR>
 */
public class CmsApiConstant {

	/**
	 * CMS模块的基础路径前缀
	 */
	public static final String CMS_PREFIX = "/cms";

	private CmsApiConstant() {
	}

	/**
	 * 平台API V1相关常量
	 */
	public static class PlatformApiV1 {

		/**
		 * 平台API V1的基础路径
		 */
		public static final String PREFIX = CMS_PREFIX + "/platform/api/v1";

		/**
		 * 平台API V1的栏目相关路径
		 */
		public static final String COLUMN = PREFIX + "/column";

		/**
		 * 平台API V1的文章相关路径
		 */
		public static final String ARTICLE = PREFIX + "/article";

		private PlatformApiV1() {
		}

	}

	/**
	 * 公开API V1相关常量
	 */
	public static class PublicApiV1 {

		/**
		 * 公开API V1的基础路径
		 */
		public static final String PREFIX = CMS_PREFIX + "/public/api/v1";

		/**
		 * 公开API V1的栏目相关路径
		 */
		public static final String COLUMN = PREFIX + "/column";

		/**
		 * 公开API V1的文章相关路径
		 */
		public static final String ARTICLE = PREFIX + "/article";

		private PublicApiV1() {
		}

	}

}
