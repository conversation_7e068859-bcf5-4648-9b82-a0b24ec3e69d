package io.naccoll.boilerplate.core.exception;

import java.io.Serial;

/**
 * 频率限制异常，用于表示请求超过系统设定的频率限制
 *
 * <AUTHOR>
 */
public class RateLimitException extends BusinessException {

	@Serial
	private static final long serialVersionUID = 23434985633551312L;

	/**
	 * 构造频率限制异常，创建一个包含错误代码和参数的频率限制异常实例
	 * @param code 错误代码，用于标识具体的错误类型
	 * @param parameters 可变参数，用于提供与错误相关的上下文信息
	 */
	public RateLimitException(String code, Object... parameters) {
		super(code, BusinessError.RATE_LIMIT, parameters);
	}

	/**
	 * 构造频率限制异常（带原始异常），创建一个包含错误代码、原始异常和参数的频率限制异常实例
	 * @param code 错误代码，用于标识具体的错误类型
	 * @param throwable 原始异常，提供导致该异常的底层异常信息
	 * @param parameters 可变参数，用于提供与错误相关的上下文信息
	 */
	public RateLimitException(String code, Throwable throwable, Object... parameters) {
		super(code, BusinessError.RATE_LIMIT, throwable, parameters);
	}

}
