package io.naccoll.boilerplate.annex.interfaces.api.platform;

import io.naccoll.boilerplate.annex.convert.OssAnnexConvert;
import io.naccoll.boilerplate.annex.convert.OssAnnexRefConvert;
import io.naccoll.boilerplate.annex.dto.*;
import io.naccoll.boilerplate.annex.model.OssAnnexPo;
import io.naccoll.boilerplate.annex.model.OssAnnexRefPo;
import io.naccoll.boilerplate.annex.service.OssAnnexQueryService;
import io.naccoll.boilerplate.annex.service.OssAnnexService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collection;

/**
 * 通用附件管理
 *
 * <AUTHOR>
 */
@RestController
@Tag(name = "通用附件管理")
@RequestMapping("/annex/platform/api/v1/annex")
public class AnnexPlatformApiV1Annex {

	@Resource
	private OssAnnexService ossAnnexService;

	@Resource
	private OssAnnexQueryService ossAnnexQueryService;

	@Resource
	private OssAnnexConvert ossAnnexConvert;

	@Resource
	private OssAnnexRefConvert ossAnnexRefConvert;

	/**
	 * 分页查询通用附件
	 * @param condition the condition
	 * @param pageable the pageable
	 * @return the response entity
	 */
	@GetMapping("/page")
	@Operation(summary = "分页查询通用附件")
	@PreAuthorize("hasPermission(0L,'GLOBAL','audit/common-annex:read')")
	public ResponseEntity<Page<OssAnnexDto>> page(OssAnnexQueryCondition condition, Pageable pageable) {
		Page<OssAnnexPo> ossAnnexPage = ossAnnexQueryService.page(condition, pageable);
		Page<OssAnnexDto> ossAnnexDtoPage = ossAnnexConvert.convertOssAnnexDtoPage(ossAnnexPage);
		return new ResponseEntity<>(ossAnnexDtoPage, HttpStatus.OK);
	}

	/**
	 * 查询通用附件
	 * @param id the id
	 * @return the response entity
	 */
	@GetMapping("/{id}")
	@Operation(summary = "查询通用附件")
	@PreAuthorize("hasPermission(0L,'GLOBAL','oss/annex:read')")
	public ResponseEntity<OssAnnexDto> queryOne(@PathVariable Long id) {
		OssAnnexPo ossAnnexPo = ossAnnexQueryService.findByIdNotNull(id);
		OssAnnexDto ossAnnexDto = ossAnnexConvert.convertOssAnnexDto(ossAnnexPo);
		return new ResponseEntity<>(ossAnnexDto, HttpStatus.OK);
	}

	/**
	 * 新增通用附件
	 * @param command the command
	 * @param file the file
	 * @return the response entity
	 */
	@PostMapping(consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
	@Operation(summary = "新增通用附件")
	@PreAuthorize("hasPermission(0L,'GLOBAL','oss/annex:add')")
	public ResponseEntity<OssAnnexRefDto> createOssAnnex(OssAnnexCreateCommand command,
			@RequestPart(value = "file", required = false) MultipartFile file) {
		OssAnnexRefPo ossAnnexPo = ossAnnexService.create(command, file);
		OssAnnexRefDto refDto = ossAnnexRefConvert.convertOssAnnexRefDto(ossAnnexPo);
		return new ResponseEntity<>(refDto, HttpStatus.CREATED);
	}

	/**
	 * 修改通用附件
	 * @param command the command
	 * @param file the file
	 * @return the response entity
	 */
	@PutMapping(consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
	@Operation(summary = "修改通用附件")
	@PreAuthorize("hasPermission(0L,'GLOBAL','oss/annex:edit')")
	public ResponseEntity<OssAnnexRefDto> updateOssAnnex(OssAnnexUpdateCommand command,
			@RequestPart(value = "file", required = false) MultipartFile file) {
		OssAnnexRefPo ossAnnexPo = ossAnnexService.update(command, file);
		OssAnnexRefDto refDto = ossAnnexRefConvert.convertOssAnnexRefDto(ossAnnexPo);
		return new ResponseEntity<>(refDto, HttpStatus.OK);
	}

	/**
	 * 删除通用附件
	 * @param id the id
	 * @return the response entity
	 */
	@DeleteMapping("/{id}")
	@Operation(summary = "删除通用附件")
	@PreAuthorize("hasPermission(0L,'GLOBAL','oss/annex:del')")
	public ResponseEntity<Void> deleteOssAnnex(@PathVariable Long id) {
		ossAnnexService.deleteById(id);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	/**
	 * 批量删除通用附件
	 * @param ids the ids
	 * @return the response entity
	 */
	@DeleteMapping("/batch")
	@Operation(summary = "批量删除通用附件")
	@PreAuthorize("hasPermission(0L,'GLOBAL','oss/annex:del')")
	@Transactional(rollbackFor = Exception.class)
	public ResponseEntity<Void> batchDeleteOssAnnex(@RequestParam Collection<Long> ids) {
		for (Long id : ids) {
			ossAnnexService.deleteById(id);
		}
		return new ResponseEntity<>(HttpStatus.OK);
	}

}
