<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>boilerplate</artifactId>
        <groupId>io.naccoll</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>boilerplate-audit</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>boilerplate-audit-api</module>
        <module>boilerplate-audit-impl</module>
    </modules>

</project>
