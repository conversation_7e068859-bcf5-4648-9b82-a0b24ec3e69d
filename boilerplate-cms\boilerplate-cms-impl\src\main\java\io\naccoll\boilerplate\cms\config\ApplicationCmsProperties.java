package io.naccoll.boilerplate.cms.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * CMS应用配置属性类
 *
 * 该类用于配置CMS模块的相关属性，包括内容摘要长度等核心参数
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ConfigurationProperties("custom.application.cms")
public class ApplicationCmsProperties {

	/**
	 * 内容摘要允许的最大额外长度，单位：字符
	 */
	private static final Integer ALLOW_LIMIT = 20;

	/**
	 * 内容摘要默认长度，单位：字符
	 */
	private Integer summaryLength = 200;

	/**
	 * 获取摘要总长度，包含基础长度和允许额外长度
	 * @return 摘要总长度
	 */
	public Integer getUseSummaryLength() {
		return summaryLength + ALLOW_LIMIT;
	}

}
