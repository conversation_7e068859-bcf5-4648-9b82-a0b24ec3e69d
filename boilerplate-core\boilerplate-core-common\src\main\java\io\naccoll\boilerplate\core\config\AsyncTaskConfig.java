package io.naccoll.boilerplate.core.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * 异步任务配置类
 * <p>
 * 该类用于配置和启用基于注解的异步任务执行支持。通过@EnableAsync注解， Spring将能够识别和处理带有@Async注解的方法，实现异步任务执行。
 * </p>
 *
 * <p>
 * 配置说明： - proxyTargetClass = true：启用CGLIB代理，支持对非公开方法和类的代理
 * </p>
 *
 * <AUTHOR>
 */
@Configuration
@EnableAsync(proxyTargetClass = true)
public class AsyncTaskConfig {

}
