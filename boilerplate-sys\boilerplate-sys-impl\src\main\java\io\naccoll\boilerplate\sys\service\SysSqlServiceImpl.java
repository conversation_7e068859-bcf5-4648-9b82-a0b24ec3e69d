package io.naccoll.boilerplate.sys.service;

import io.naccoll.boilerplate.core.audit.operate.OperateLog;
import io.naccoll.boilerplate.core.cache.CacheTemplate;
import io.naccoll.boilerplate.core.exception.ClientException;
import io.naccoll.boilerplate.core.exception.ForbiddenException;
import io.naccoll.boilerplate.core.id.IdService;
import io.naccoll.boilerplate.core.persistence.mybatis.MybatisSqlParser;
import io.naccoll.boilerplate.core.persistence.mybatis.SqlMapper;
import io.naccoll.boilerplate.core.persistence.utils.TransactionUtil;
import io.naccoll.boilerplate.core.ratelimit.RateLimit;
import io.naccoll.boilerplate.core.ratelimit.RateLimiterMode;
import io.naccoll.boilerplate.core.security.authtication.session.SessionHelper;
import io.naccoll.boilerplate.core.utils.BeanUtils;
import io.naccoll.boilerplate.core.utils.ExcelHelper;
import io.naccoll.boilerplate.sys.dao.SysSqlDao;
import io.naccoll.boilerplate.sys.dto.*;
import io.naccoll.boilerplate.sys.enums.PermissionLevel;
import io.naccoll.boilerplate.sys.enums.SysSqlType;
import io.naccoll.boilerplate.sys.model.SysPermissionPo;
import io.naccoll.boilerplate.sys.model.SysSqlPo;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;

import java.io.IOException;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 动态SQL集服务实现
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SysSqlServiceImpl implements SysSqlService {

	/**
	 * 当前用户ID的键
	 */
	public static final String CURRENT_USER_ID = "_currentUserId";

	/**
	 * 当前用户名的键
	 */
	public static final String CURRENT_NAME = "_currentName";

	/**
	 * 当前用户名的键
	 */
	public static final String CURRENT_USERNAME = "_currentUsername";

	/**
	 * 当前部门ID的键
	 */
	public static final String CURRENT_DEPART_ID = "_currentDepartId";

	/**
	 * 当前组织ID的键
	 */
	public static final String CURRENT_ORGANIZATION_ID = "_currentOrganizationId";

	/**
	 * 查询权限
	 */
	private static final String READ_PERMIT = "sys/sql:query-execute";

	/**
	 * 写入权限
	 */
	private static final String WRITE_PERMIT = "sys/sql:operate-execute";

	@Resource
	private SysSqlDao sysSqlDao;

	@Resource
	private SysSqlQueryService sysSqlQueryService;

	@Resource
	private SysSqlChangelogService sysSqlChangelogService;

	@Resource
	private IdService idService;

	@Resource
	private SessionHelper sessionHelper;

	@Resource
	private SqlMapper sqlMapper;

	@Resource
	private CacheTemplate cacheTemplate;

	@Resource
	private SysPermissionQueryService sysPermissionQueryService;

	@Resource
	private SysPermissionService sysPermissionService;

	/**
	 * 获取缓存键
	 * @param code SQL标识
	 * @param hash 参数哈希值
	 * @return 缓存键
	 */
	private static String getCacheKey(String code, String hash) {
		return String.format("sql:%s:%s", code, hash);
	}

	@Override
	@OperateLog(value = "新增动态SQL集", id = "#result.id", type = "动态SQL集",
			afterDataAccess = "@sysSqlQueryServiceImpl.findById(#result.id)")
	@Transactional(rollbackFor = Exception.class)
	@RateLimit(mode = RateLimiterMode.WINDOWS, rate = 1)
	public SysSqlPo create(@Valid SysSqlCreateCommand command) {
		SysSqlPo old = sysSqlQueryService.findByCode(command.getCode());
		if (old != null) {
			throw new ClientException("该SQL标识已被使用");
		}
		if (!Objects.equals(command.getIgnoreValidSql(), true)) {
			MybatisSqlParser.validateSql(command.getContent());
		}
		SysSqlPo sysSql = new SysSqlPo();
		BeanUtils.copyProperties(command, sysSql);
		sysSql.setId(idService.getId());
		sysSql = sysSqlDao.save(sysSql);
		sysSqlChangelogService.create(sysSql);
		createPermission(sysSql);
		return sysSql;
	}

	@Override
	@OperateLog(value = "修改动态SQL集", id = "#command.id", type = "动态SQL集",
			beforeDataAccess = "@sysSqlQueryServiceImpl.findById(#command.id)",
			afterDataAccess = "@sysSqlQueryServiceImpl.findById(#result.id)")
	@Transactional(rollbackFor = Exception.class)
	public SysSqlPo update(@Valid SysSqlUpdateCommand command) {
		SysSqlPo old = sysSqlQueryService.findByCode(command.getCode());
		if (old != null && !Objects.equals(old.getId(), command.getId())) {
			throw new ClientException("该SQL标识已被使用");
		}
		if (!Objects.equals(command.getIgnoreValidSql(), true)) {
			MybatisSqlParser.validateSql(command.getContent());
		}
		SysSqlPo sysSql = sysSqlQueryService.findByIdNotNull(command.getId());
		String oldCode = sysSql.getCode();
		BeanUtils.copyProperties(command, sysSql, "id");
		sysSql = sysSqlDao.save(sysSql);
		sysSqlChangelogService.create(sysSql);
		cacheTemplate.deleteAll(getCacheKey(sysSql.getCode(), "*"));
		updatePermission(oldCode, sysSql);
		return sysSql;
	}

	@Override
	@OperateLog(value = "修改动态SQL集", id = "id", type = "动态SQL集",
			beforeDataAccess = "@sysSqlQueryServiceImpl.findByIds(#commands.![id])",
			afterDataAccess = "@sysSqlQueryServiceImpl.findByIds(#commands.![id])", batch = true)
	@Transactional(rollbackFor = Exception.class)
	public List<SysSqlUpdateCommand> update(List<SysSqlUpdateCommand> commands) {
		if (CollectionUtils.isEmpty(commands)) {
			return List.of();
		}
		List<Long> ids = commands.stream().map(SysSqlUpdateCommand::getId).toList();
		List<String> codes = commands.stream().map(SysSqlUpdateCommand::getCode).toList();
		Map<String, SysSqlPo> sqlCodes = sysSqlQueryService.findMapByCodes(codes);
		Map<Long, SysSqlPo> sysSqls = sysSqlQueryService.findMapByIds(ids);

		List<SysSqlPo> sysSqlsList = new ArrayList<>(sysSqls.values());
		Map<String, SysSqlPo> updatePermissions = new HashMap<>(sqlCodes);
		for (SysSqlUpdateCommand command : commands) {
			SysSqlPo old = sqlCodes.get(command.getCode());
			if (old != null && !Objects.equals(old.getId(), command.getId())) {
				throw new ClientException("该SQL标识已被使用: " + command.getCode());
			}
			if (!Objects.equals(command.getIgnoreValidSql(), true)) {
				MybatisSqlParser.validateSql(command.getContent());
			}
			SysSqlPo sysSql = sysSqls.get(command.getId());
			if (sysSql == null) {
				throw new ClientException("动态SQL集不存在，ID：" + command.getId());
			}
			String oldCode = sysSql.getCode();
			BeanUtils.copyProperties(command, sysSql, "id");
			sysSql = sysSqlDao.save(sysSql);
			sysSqlsList.add(sysSql);
			updatePermissions.put(oldCode, sysSql);

			String code = sysSql.getCode();
			TransactionUtil.completeTransaction(() -> {
				cacheTemplate.deleteAll(getCacheKey(code, "*"));
			});
		}
		sysSqlDao.saveAll(sysSqlsList);
		sysSqlChangelogService.batchCreate(sysSqlsList);
		updatePermission(updatePermissions);
		return List.of();
	}

	@Override
	@OperateLog(value = "删除动态SQL集", id = "#id", type = "动态SQL集",
			beforeDataAccess = "@sysSqlQueryServiceImpl.findById(#id)")
	@Transactional(rollbackFor = Exception.class)
	@RateLimit(mode = RateLimiterMode.WINDOWS, rate = 1)
	public void deleteById(Long id) {
		sysSqlQueryService.findByIdNotNull(id);
		sysSqlDao.deleteById(id);
	}

	@OperateLog(value = "删除动态SQL集", id = "id", type = "动态SQL集",
			beforeDataAccess = "@sysSqlQueryServiceImpl.findByIds(#ids)",
			afterDataAccess = "@sysSqlQueryServiceImpl.findByIds(#ids)", batch = true)
	@Transactional(rollbackFor = Exception.class)
	@RateLimit(mode = RateLimiterMode.WINDOWS, rate = 1)
	@Override
	public void deleteByIds(Collection<Long> ids) {
		if (CollectionUtils.isEmpty(ids)) {
			return;
		}
		sysSqlDao.deleteAllByIdInBatch(ids);
	}

	@Override
	public byte[] exportExcel(@Valid List<SysSqlExecuteCommand> commands) throws IOException {
		List<ExcelHelper.ExcelSheetItem> results = new ArrayList<>(commands.size());
		for (int i = 0; i < commands.size(); i++) {
			SysSqlExecuteCommand command = commands.get(i);
			String sheetName = Optional.ofNullable(command.getSheetName())
				.filter(s -> !StringUtils.hasText(s))
				.orElse("Sheet" + (i + 1));
			command.setSheetName(sheetName);
			results.add(new ExcelHelper.ExcelSheetItem(command.getSheetName(),
					executeQuerySql(command).stream().flatMap(Collection::stream).toList()));
		}
		return ExcelHelper.exportExcel(results);
	}

	@Override
	@SuppressWarnings("unchecked")
	public List<List<Map<String, Object>>> executeQuerySql(@Valid SysSqlExecuteCommand command) {
		SysSqlPo sql = sysSqlQueryService.findByCodeNotNull(command.getCode());
		if (!Objects.equals(sql.getSqlType(), SysSqlType.SELECT.getId())) {
			throw new ClientException("该SQL并非查询SQL，无法执行");
		}

		checkSqlExecPermit(sql, READ_PERMIT);
		String sqlContent = sql.getContent();
		if (Objects.equals(sql.getUseScript(), true)) {
			sqlContent = String.format("<script>%s</script>", sqlContent);
		}

		Map<String, Object> params = fillParams(command, sqlContent);

		String cacheKey = getCacheKey(sql.getCode(), String.valueOf(params.toString().hashCode()));
		if (Objects.equals(sql.getCacheable(), true)) {
			Object obj = cacheTemplate.get(cacheKey);
			if (obj != null) {
				return (List<List<Map<String, Object>>>) obj;
			}
		}
		List<List<Map<String, Object>>> result = sqlMapper.selectListMultipleResult(sqlContent, params);
		if (Objects.equals(sql.getCacheable(), true)) {
			cacheTemplate.set(cacheKey, result, Duration.ofSeconds(sql.getCacheTime()));
		}
		return result;
	}

	private Map<String, Object> fillParams(SysSqlExecuteCommand command, String sqlContent) {
		Map<String, Object> params = Optional.ofNullable(command.getParams()).orElse(HashMap.newHashMap(10));
		if (sqlContent.contains(CURRENT_USER_ID)) {
			params.put(CURRENT_USER_ID, sessionHelper.getUserId());
		}
		if (sqlContent.contains(CURRENT_NAME)) {
			params.put(CURRENT_NAME, sessionHelper.getName());
		}
		if (sqlContent.contains(CURRENT_USERNAME)) {
			params.put(CURRENT_USERNAME, sessionHelper.getUsername());
		}
		if (sqlContent.contains(CURRENT_DEPART_ID)) {
			params.put(CURRENT_DEPART_ID, sessionHelper.getDepartId());
		}
		if (sqlContent.contains(CURRENT_ORGANIZATION_ID)) {
			params.put(CURRENT_ORGANIZATION_ID, sessionHelper.getOrganizationId());
		}
		return params;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public int executeOperateSql(@Valid SysSqlExecuteCommand command) {
		SysSqlPo sql = sysSqlQueryService.findByCodeNotNull(command.getCode());
		SysSqlType sqlType = SysSqlType.fromId(sql.getSqlType());

		checkSqlExecPermit(sql, WRITE_PERMIT);
		String sqlContent = sql.getContent();
		if (Objects.equals(sql.getUseScript(), true)) {
			sqlContent = String.format("<script>%s</script>", sqlContent);
		}

		Map<String, Object> params = fillParams(command, sqlContent);
		return switch (sqlType) {
			case INSERT -> sqlMapper.insert(sqlContent, params);
			case UPDATE -> sqlMapper.update(sqlContent, params);
			case DELETE -> sqlMapper.delete(sqlContent, params);
			default -> throw new ClientException("该SQL并非操作SQL，无法执行");
		};
	}

	private void checkSqlExecPermit(SysSqlPo sql, String permit) {
		if (!Objects.equals(sql.getIsPublic(), true)) {
			long userId = sessionHelper.getCurrentUser().getId();
			boolean execSqlPermit = sysPermissionQueryService.hasAnyPermission(userId, PermissionLevel.GLOBAL, 0L,
					Set.of("sql/" + sql.getCode(), permit));
			if (!execSqlPermit) {
				throw new ForbiddenException("无权限执行该动态SQL");
			}
		}

	}

	private void createPermission(SysSqlPo sql) {
		String identity = "sql/" + sql.getCode();
		SysPermissionCreateCommand command = new SysPermissionCreateCommand();
		command.setIdentity(identity);
		command.setName("SQL-" + sql.getCode());
		command.setDescription(sql.getDescription());
		command.setLevel(PermissionLevel.GLOBAL.getId());
		command.setOrigin(0L);
		sysPermissionService.create(command);
	}

	private void updatePermission(String oldCode, SysSqlPo sql) {
		updatePermission(Map.of(oldCode, sql));
	}

	private void updatePermission(Map<String, SysSqlPo> sqlMap) {
		if (CollectionUtils.isEmpty(sqlMap)) {
			return;
		}
		Set<String> oldCodes = sqlMap.keySet().stream().map(i -> "sql/" + i).collect(Collectors.toSet());
		Map<String, SysPermissionPo> permissions = sysPermissionQueryService.findMapByIdentities(oldCodes);
		List<SysPermissionCreateCommand> createCommands = new ArrayList<>();
		List<SysPermissionUpdateCommand> updateCommands = new ArrayList<>();
		for (Map.Entry<String, SysSqlPo> entry : sqlMap.entrySet()) {
			String oldCode = entry.getKey();
			SysSqlPo sql = entry.getValue();

			String identity = "sql/" + oldCode;
			SysPermissionPo permission = permissions.get(identity);
			if (permission == null) {
				createPermission(sql);
				return;
			}
			String newIdentity = "sql/" + sql.getCode();
			String newName = "SQL-" + sql.getCode();
			boolean needUpdate = false;
			SysPermissionUpdateCommand command = new SysPermissionUpdateCommand();
			command.setId(permission.getId());
			command.setIdentity(identity);
			command.setName(permission.getName());
			command.setDescription(permission.getDescription());
			if (!Objects.equals(identity, newIdentity)) {
				needUpdate = true;
				command.setIdentity(newIdentity);
			}
			if (!Objects.equals(permission.getName(), newName)) {
				needUpdate = true;
				command.setName(newName);
			}
			if (!Objects.equals(permission.getDescription(), sql.getDescription())) {
				command.setDescription(sql.getDescription());
				needUpdate = true;
			}
			command.setLevel(permission.getLevel());
			command.setOrigin(permission.getOrigin());
			if (needUpdate) {
				updateCommands.add(command);
			}
		}
		for (SysPermissionUpdateCommand updateCommand : updateCommands) {
			sysPermissionService.update(updateCommand);
		}
	}

}
