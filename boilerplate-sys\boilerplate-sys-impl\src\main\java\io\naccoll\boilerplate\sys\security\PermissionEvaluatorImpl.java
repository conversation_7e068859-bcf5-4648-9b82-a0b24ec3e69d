package io.naccoll.boilerplate.sys.security;

import io.naccoll.boilerplate.core.security.authtication.entity.UserDetailsImpl;
import io.naccoll.boilerplate.core.security.enums.Realm;
import io.naccoll.boilerplate.core.security.properties.PermissionSecurityProperties;
import io.naccoll.boilerplate.sys.enums.PermissionLevel;
import io.naccoll.boilerplate.sys.service.SysPermissionQueryService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.security.access.PermissionEvaluator;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.servlet.util.matcher.PathPatternRequestMatcher;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 权限评估器实现类，用于评估用户是否具有特定权限
 *
 * <AUTHOR>
 */
@Component
@EnableConfigurationProperties(PermissionSecurityProperties.class)
public class PermissionEvaluatorImpl implements PermissionEvaluator, InitializingBean {

	@Resource
	private ObjectProvider<SysPermissionQueryService> sysPermissionQueryService;

	@Resource
	private PermissionSecurityProperties permissionSecurityProperties;

	/**
	 * 路径匹配器列表，用于匹配需要权限检查的URL路径
	 */
	private List<PathPatternRequestMatcher> pathMatchers = Collections.emptyList();

	/**
	 * 检查用户是否具有指定对象的权限
	 * @param authentication 认证信息
	 * @param targetDomainObject 目标域对象
	 * @param permission 权限
	 * @return 是否具有权限
	 */
	@Override
	public boolean hasPermission(Authentication authentication, Object targetDomainObject, Object permission) {
		return hasPermission(authentication, 0, targetDomainObject.toString(), permission);
	}

	/**
	 * 检查用户是否具有指定类型和ID对象的权限
	 * @param authentication 认证信息
	 * @param targetId 目标对象ID
	 * @param targetType 目标对象类型
	 * @param permission 权限
	 * @return 是否具有权限
	 */
	@Override
	public boolean hasPermission(Authentication authentication, Serializable targetId, String targetType,
			Object permission) {

		if (authentication == null || authentication.getDetails() == null) {
			return false;
		}
		if (!(authentication.getDetails() instanceof UserDetailsImpl userDetails)) {
			return false;
		}

		if (!Objects.equals(userDetails.getRealm(), Realm.PLATFORM)) {
			return false;
		}

		if (!permissionSecurityProperties.isEnable()) {
			if (pathMatchers.isEmpty()) {
				return true;
			}
			// 在全局权限检查关闭时，检查当前路径是否在检查路径中
			ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder
				.getRequestAttributes();
			if (servletRequestAttributes != null) {
				HttpServletRequest request = servletRequestAttributes.getRequest();
				if (pathMatchers.stream().noneMatch(pattern -> pattern.matches(request))) {
					return true;
				}
			}
		}

		long userId = userDetails.getId();
		PermissionLevel permissionLevel = PermissionLevel.valueOf(targetType);
		if (Objects.equals(permissionLevel, PermissionLevel.GLOBAL)) {
			targetId = 0;
		}
		return sysPermissionQueryService.getObject()
			.hasPermission(userId, permissionLevel, Long.valueOf(targetId.toString()), permission.toString());
	}

	@Override
	public void afterPropertiesSet() {
		pathMatchers = permissionSecurityProperties.getRequirePaths()
			.stream()
			.map(pattern -> PathPatternRequestMatcher.withDefaults().matcher(pattern.getMethod(), pattern.getPath()))
			.toList();
	}

}
