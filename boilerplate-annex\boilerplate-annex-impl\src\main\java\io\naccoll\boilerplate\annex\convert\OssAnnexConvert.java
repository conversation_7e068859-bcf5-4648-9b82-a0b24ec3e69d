package io.naccoll.boilerplate.annex.convert;

import io.naccoll.boilerplate.annex.dto.OssAnnexDto;
import io.naccoll.boilerplate.annex.model.OssAnnexPo;
import io.naccoll.boilerplate.core.utils.BeanUtils;
import io.naccoll.boilerplate.oss.OssServiceHelper;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 通用附件转换器
 *
 * <AUTHOR>
 */
@Component
public class OssAnnexConvert {

	@Resource
	private OssServiceHelper ossServiceHelper;

	/**
	 * 将单个附件实体转换为DTO
	 * @param po 附件实体
	 * @return 转换后的DTO
	 */
	public OssAnnexDto convertOssAnnexDto(OssAnnexPo po) {
		return convertOssAnnexDtoList(List.of(po)).getFirst();
	}

	/**
	 * 将附件实体列表转换为DTO列表
	 * @param list 附件实体列表
	 * @return 转换后的DTO列表
	 */
	public List<OssAnnexDto> convertOssAnnexDtoList(List<OssAnnexPo> list) {
		return list.stream().map(po -> {
			OssAnnexDto dto = new OssAnnexDto();
			BeanUtils.copyProperties(po, dto);
			if (StringUtils.hasText(po.getAnnexUrl())) {
				dto.setAnnexUrl(ossServiceHelper.parseUrl(po.getAnnexUrl()));
			}
			return dto;
		}).toList();
	}

	/**
	 * 将附件实体分页转换为DTO分页
	 * @param page 附件实体分页
	 * @return 转换后的DTO分页
	 */
	public Page<OssAnnexDto> convertOssAnnexDtoPage(Page<OssAnnexPo> page) {
		List<OssAnnexDto> ossAnnexList = convertOssAnnexDtoList(page.getContent());
		return new PageImpl<>(ossAnnexList, page.getPageable(), page.getTotalElements());
	}

}
