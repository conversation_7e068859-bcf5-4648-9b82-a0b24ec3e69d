
package ${package}.service;

import ${package}.dto.${className}CreateCommand;
import ${package}.dto.${className}UpdateCommand;
import ${package}.model.${className}Po;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Collection;

/**
 * ${apiAlias}服务
 *
 * <AUTHOR>
 */
public interface ${className}Service {

	/**
	 * 创建${apiAlias}
	 * @param command 创建参数
	 * @return ${className}Po
	 */
	${className}Po create(@Valid ${className}CreateCommand command);

	/**
	 * 批量创建${apiAlias}
	 * @param commands 创建参数列表
	 * @return 创建结果列表
	 */
	List<${className}Po> batchCreate(List<${className}CreateCommand> commands);

	/**
	 * 更新${apiAlias}
	 * @param command 更新参数
	 * @return ${className}Po
	 */
	${className}Po update(@Valid ${className}UpdateCommand command);

	/**
	 * 批量更新${apiAlias}
	 * @param commands 更新参数列表
	 * @return 更新结果列表
	 */
	List<${className}Po> batchUpdate(List<${className}UpdateCommand> commands);

	/**
	 * 删除${apiAlias}
	 * @param id ${apiAlias}Id
	 */
	void deleteById(${pkColumnType} id);

	/**
	 * 批量删除${apiAlias}
	 * @param ids id集合
	 */
	void batchDelete(Collection<${pkColumnType}> ids);

}
