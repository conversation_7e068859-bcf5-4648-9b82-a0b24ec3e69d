package io.naccoll.boilerplate.core.utils;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.poi.excel.BigExcelWriter;
import cn.hutool.poi.excel.WorkbookUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.poi.ss.usermodel.*;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * Excel操作工具类，提供Excel导出功能
 * <p>
 * 基于Apache POI和Hutool实现高性能Excel导出，支持大数据量分页处理和列宽自适应
 *
 * <AUTHOR>
 */
public class ExcelHelper {

	private ExcelHelper() {
	}

	/**
	 * 将数据列表导出为Excel字节数组
	 * <p>
	 * 支持大数据量分页处理，自动调整列宽
	 * @param sheets Excel工作表数据列表
	 * @return Excel文件字节数组
	 * @throws IOException 当IO操作异常时抛出
	 */
	public static byte[] exportExcel(List<ExcelSheetItem> sheets) throws IOException {
		try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
			int rowAccessWindowSize = 100;
			try (BigExcelWriter writer = new BigExcelWriter(rowAccessWindowSize)) {
				Workbook workbook = writer.getWorkbook();
				for (ExcelSheetItem sheetData : sheets) {
					WorkbookUtil.getOrCreateSheet(workbook, sheetData.getSheetName());
					List<Map<String, Object>> dataList = sheetData.getData();
					writer.setSheet(sheetData.getSheetName());
					var lists = ListUtil.partition(dataList, 100);
					for (int i = 0; i < lists.size(); i++) {
						writer.write(lists.get(i));
						writer.autoSizeColumnAll();
						autoColumnWidthForChineseChar(i * 100, writer.getSheet());
					}
				}
				writer.flush(outputStream);
				return outputStream.toByteArray();
			}
		}
	}

	/**
	 * 自动调整列宽适应中文字符串
	 * <p>
	 * 根据单元格内容自动计算最佳列宽
	 * @param firstRowNum 起始行号
	 * @param sheet Excel工作表对象
	 */
	private static void autoColumnWidthForChineseChar(int firstRowNum, Sheet sheet) {
		final int columnCount = sheet.getRow(sheet.getLastRowNum()).getLastCellNum();
		for (int columnNum = 0; columnNum < columnCount; columnNum++) {
			final int columnWidth = sheet.getColumnWidth(columnNum);
			int newWidth = columnWidth;
			for (int rowNum = firstRowNum; rowNum <= sheet.getLastRowNum(); rowNum++) {
				Row currentRow;
				if (sheet.getRow(rowNum) == null) {
					continue;
				}
				else {
					currentRow = sheet.getRow(rowNum);
				}
				if (currentRow.getCell(columnNum) != null) {
					Cell currentCell = currentRow.getCell(columnNum);
					if (currentCell.getCellType() == CellType.STRING) {
						String value = currentCell.getStringCellValue();
						int length = value.getBytes().length * 256;
						if (newWidth < length && length < 256 * 256) {
							newWidth = length;
						}
					}
				}
			}
			if (newWidth > columnWidth) {
				sheet.setColumnWidth(columnNum, newWidth);
			}
		}
	}

	/**
	 * Excel工作表数据项
	 */
	@Data
	@NoArgsConstructor
	@AllArgsConstructor
	public static class ExcelSheetItem {

		/**
		 * 工作表名称
		 */
		private String sheetName;

		/**
		 * 工作表数据列表
		 */
		private List<Map<String, Object>> data;

	}

}
