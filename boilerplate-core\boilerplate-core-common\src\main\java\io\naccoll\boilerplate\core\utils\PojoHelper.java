package io.naccoll.boilerplate.core.utils;

import io.naccoll.boilerplate.core.exception.ServerException;
import io.naccoll.boilerplate.core.interfaces.function.PropertyFunc;

import java.lang.invoke.SerializedLambda;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * POJO工具类，提供JavaBean属性相关操作
 *
 * <AUTHOR>
 */
public class PojoHelper {

	private static final Map<PropertyFunc<?, ?>, String> PROPERTY_COLUMN_CACHE = new ConcurrentHashMap<>(50);

	/**
	 * 私有构造函数，防止实例化
	 */
	private PojoHelper() {
	}

	/**
	 * 从属性函数获取对应的字段
	 * @param fn 属性函数
	 * @return 对应的字段对象
	 * @param <T> 源类型
	 * @param <R> 属性类型
	 */
	public static <T, R> Field getField(PropertyFunc<T, R> fn) {
		// 从function取出序列化方法
		Method writeReplaceMethod;
		try {
			writeReplaceMethod = fn.getClass().getDeclaredMethod("writeReplace");
		}
		catch (NoSuchMethodException e) {
			throw new ServerException(e);
		}

		// 从序列化方法取出序列化的lambda信息
		boolean isAccessible = writeReplaceMethod.isAccessible();
		writeReplaceMethod.setAccessible(true);
		SerializedLambda serializedLambda;
		try {
			serializedLambda = (SerializedLambda) writeReplaceMethod.invoke(fn);
		}
		catch (IllegalAccessException | InvocationTargetException e) {
			throw new ServerException(e);
		}
		writeReplaceMethod.setAccessible(isAccessible);
		String fieldName = uncapitalize(serializedLambda.getImplMethodName());
		Field field;
		try {
			field = Class.forName(serializedLambda.getImplClass().replace("/", ".")).getDeclaredField(fieldName);
		}
		catch (ClassNotFoundException | NoSuchFieldException e) {
			throw new ServerException(e);
		}

		return field;
	}

	/**
	 * 获取属性函数的属性名
	 * <p>
	 * 支持递归查找父类方法
	 * </p>
	 * @param fn 属性函数
	 * @return 属性名
	 * @param <T> 源类型
	 * @param <R> 属性类型
	 */
	public static <T, R> String getPropertyName(PropertyFunc<T, R> fn) {
		if (PROPERTY_COLUMN_CACHE.containsKey(fn)) {
			return PROPERTY_COLUMN_CACHE.get(fn);
		}
		Class<?> clazz = fn.getClass();
		try {
			Method writeReplaceMethod = clazz.getDeclaredMethod("writeReplace");
			boolean isAccessible = writeReplaceMethod.isAccessible();
			writeReplaceMethod.setAccessible(true);
			SerializedLambda serializedLambda = (SerializedLambda) writeReplaceMethod.invoke(fn);
			writeReplaceMethod.setAccessible(isAccessible);
			String property = uncapitalize(serializedLambda.getImplMethodName());
			PROPERTY_COLUMN_CACHE.put(fn, property);
			return property;
		}
		catch (Exception e) {
			if (!Object.class.isAssignableFrom(clazz.getSuperclass())) {
				return getPropertyName(fn);
			}
			throw new ServerException("解析属性出错", e);
		}
	}

	/**
	 * 将方法名转换为属性名
	 * <p>
	 * 支持get/set/is前缀的方法名转换
	 * </p>
	 * @param methodName 方法名
	 * @return 属性名
	 * @throws IllegalArgumentException 如果不是get/set/is开头的方法名
	 */
	public static String uncapitalize(String methodName) {
		String propertyName;
		if (methodName.startsWith("is")) {
			propertyName = methodName.substring(2);
		}
		else if (methodName.startsWith("get") || methodName.startsWith("set")) {
			propertyName = methodName.substring(3);
		}
		else {
			throw new IllegalArgumentException(
					"Error parsing property name '" + methodName + "'.  Didn't start with 'is', 'get' or 'set'.");
		}

		boolean onlyOneChar = propertyName.length() == 1;
		boolean firstCharUpper = propertyName.length() > 1 && !Character.isUpperCase(propertyName.charAt(1));
		if (onlyOneChar || firstCharUpper) {
			propertyName = propertyName.substring(0, 1).toLowerCase(Locale.ENGLISH) + propertyName.substring(1);
		}

		return propertyName;
	}

}
