package io.naccoll.boilerplate.audit.interfaces.api.user;

import io.naccoll.boilerplate.audit.convert.AuditDataModelConvert;
import io.naccoll.boilerplate.audit.dto.AuditDataModelDto;
import io.naccoll.boilerplate.audit.model.AuditDataModelPo;
import io.naccoll.boilerplate.audit.service.AuditDataModelQueryService;
import io.naccoll.boilerplate.core.exception.ResourceNotFoundException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

/**
 * 数据审计模型管理
 *
 * <AUTHOR>
 */
@RestController
@Tag(name = "数据审计模型管理")
@RequestMapping("/audit/user/api/v1/data-model")
public class AuditUserApiV1DataModel {

	/**
	 * 审计数据模型查询服务
	 */
	@Resource
	private AuditDataModelQueryService auditDataModelQueryService;

	/**
	 * 审计数据模型转换器
	 */
	@Resource
	private AuditDataModelConvert auditDataModelConvert;

	/**
	 * 根据模型标识查询审计数据模型
	 * @param modelSign 模型标识
	 * @return 审计数据模型DTO
	 */
	@GetMapping("/model-sign/{modelSign}")
	@Operation(summary = "查询审计的数据模型")
	public AuditDataModelDto queryByModelSign(@PathVariable String modelSign) {
		AuditDataModelPo model = Optional.ofNullable(auditDataModelQueryService.findByModelSign(modelSign))
			.orElseThrow(() -> new ResourceNotFoundException("数据模型不存在"));
		return auditDataModelConvert.convertAuditDataModelDto(model);
	}

}
