package io.naccoll.boilerplate.sys.service;

import io.naccoll.boilerplate.core.audit.operate.OperateLog;
import io.naccoll.boilerplate.core.ratelimit.RateLimit;
import io.naccoll.boilerplate.core.ratelimit.RateLimiterMode;
import io.naccoll.boilerplate.sys.dto.SysSqlCreateCommand;
import io.naccoll.boilerplate.sys.dto.SysSqlExecuteCommand;
import io.naccoll.boilerplate.sys.dto.SysSqlUpdateCommand;
import io.naccoll.boilerplate.sys.model.SysSqlPo;
import jakarta.validation.Valid;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 动态SQL集服务接口 提供动态SQL集的创建、更新、删除及执行等操作
 *
 * <AUTHOR>
 */
public interface SysSqlService {

	/**
	 * 创建新的动态SQL集
	 * @param command 包含动态SQL集创建参数的对象
	 * @return 创建成功的动态SQL集对象
	 */
	SysSqlPo create(@Valid SysSqlCreateCommand command);

	/**
	 * 更新现有的动态SQL集
	 * @param command 包含动态SQL集更新参数的对象
	 * @return 更新后的动态SQL集对象
	 */
	SysSqlPo update(@Valid SysSqlUpdateCommand command);

	/**
	 * 根据ID删除动态SQL集
	 * @param id 要删除的动态SQL集ID
	 */
	void deleteById(Long id);

	@OperateLog(value = "删除动态SQL集", id = "#id", type = "动态SQL集",
			beforeDataAccess = "@sysSqlQueryServiceImpl.findById(#id)")
	@Transactional(rollbackFor = Exception.class)
	@RateLimit(mode = RateLimiterMode.WINDOWS, rate = 1)
	void deleteByIds(Collection<Long> ids);

	/**
	 * 将动态SQL集导出为Excel文件
	 * @param commands 要导出的动态SQL集执行命令列表
	 * @return Excel文件的字节数组
	 * @throws IOException 当导出过程中发生IO异常时抛出
	 */
	byte[] exportExcel(@Valid List<SysSqlExecuteCommand> commands) throws IOException;

	/**
	 * 执行查询类型的SQL语句
	 * @param command 包含SQL执行参数的对象
	 * @return 查询结果列表，每个结果是一个字段名到值的映射表
	 */
	List<List<Map<String, Object>>> executeQuerySql(@Valid SysSqlExecuteCommand command);

	/**
	 * 执行操作类型的SQL语句（如INSERT、UPDATE、DELETE等）
	 * @param command 包含SQL执行参数的对象
	 * @return 受影响的行数
	 */
	int executeOperateSql(@Valid SysSqlExecuteCommand command);

	/**
	 * 更新多个动态SQL集
	 * @param commands 包含多个动态SQL集更新参数的对象列表
	 */
	List<SysSqlUpdateCommand> update(List<SysSqlUpdateCommand> commands);

}
