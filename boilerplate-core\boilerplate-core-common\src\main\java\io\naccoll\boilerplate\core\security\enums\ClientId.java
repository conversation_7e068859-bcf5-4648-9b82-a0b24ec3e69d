package io.naccoll.boilerplate.core.security.enums;

import io.naccoll.boilerplate.core.enums.DisplayEnum;
import io.naccoll.boilerplate.core.enums.EnumHelper;

/**
 * 客户端枚举类，用于标识不同类型的客户端
 * <p>
 * 该枚举类定义了系统中支持的客户端类型，每个客户端类型都有唯一的标识和名称
 * </p>
 *
 * <AUTHOR>
 */
public enum ClientId implements DisplayEnum {

	/**
	 * 无客户端类型
	 */
	NONE(0, "None"),

	/**
	 * 浏览器客户端类型
	 */
	BROWSER(1, "浏览器"),

	/**
	 * 移动应用客户端类型
	 */
	APP(2, "APP"),

	/**
	 * 服务器端客户端类型
	 */
	SERVER(3, "第三方服务"),

	/**
	 * 微信公众号网页客户端类型
	 */
	MP_H5(4, "微信公众号网页"),

	/**
	 * 微信小程序客户端类型
	 */
	MP_MINIAPP(5, "微信小程序"),;

	private final Integer id;

	private final String name;

	/**
	 * 构造方法，初始化客户端ID和名称
	 * @param id 客户端ID
	 * @param name 客户端名称
	 */
	ClientId(Integer id, String name) {
		this.id = id;
		this.name = name;
	}

	/**
	 * 根据客户端ID获取对应的客户端枚举
	 * @param id 客户端ID
	 * @return 对应的客户端枚举实例
	 */
	public static ClientId fromId(Integer id) {
		return EnumHelper.fromId(ClientId.class, id);
	}

	@Override
	public Integer getId() {
		return id;
	}

	@Override
	public String getName() {
		return name;
	}

}
