package io.naccoll.boilerplate.core.cache;

import io.naccoll.boilerplate.core.interfaces.function.CheckedCallable;
import io.naccoll.boilerplate.core.interfaces.function.CheckedRunnable;

import java.time.Duration;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.locks.Lock;

/**
 * 缓存模板接口
 * <p>
 * 定义统一的缓存操作规范，提供基础缓存操作和锁机制支持
 * </p>
 *
 * <AUTHOR>
 */
public interface CacheTemplate {

	/**
	 * 检查指定缓存键是否存在
	 * @param key 缓存键
	 * @return 是否存在该缓存键
	 */
	boolean hasKey(String key);

	/**
	 * 根据模式匹配获取缓存键集合
	 * @param pattern 键模式表达式（支持*和?通配符）
	 * @return 匹配的缓存键集合
	 */
	Set<String> keys(String pattern);

	/**
	 * 设置缓存值及其过期时间
	 * @param key 缓存键
	 * @param value 缓存值
	 * @param duration 缓存有效时间
	 */
	void set(String key, Object value, Duration duration);

	/**
	 * 获取指定缓存键的值
	 * @param key 缓存键
	 * @return 缓存值
	 */
	Object get(String key);

	/**
	 * 删除指定缓存键
	 * @param key 缓存键
	 * @return 是否删除成功
	 */
	boolean delete(String key);

	/**
	 * 批量删除匹配模式的缓存键 <br>
	 * 默认实现遍历匹配键并逐个删除
	 * @param pattern 正则
	 * @return
	 */
	default boolean deleteAll(String pattern) {
		return keys(pattern).stream().allMatch(this::delete);
	}

	/**
	 * 如果缓存键不存在，则设置缓存值及其过期时间
	 * @param key 缓存键
	 * @param obj 缓存值
	 * @param duration 缓存有效时间
	 * @return 是否成功设置缓存
	 */
	boolean setIfAbsent(String key, Object obj, Duration duration);

	/**
	 * 设置缓存的过期时间
	 * @param key 缓存键
	 * @param duration 缓存有效时间
	 * @return 是否成功设置过期时间
	 */
	boolean expire(String key, Duration duration);

	/**
	 * 获取缓存的过期时间
	 * @param key 缓存键
	 * @return 缓存的过期时间
	 */
	Duration getExpire(String key);

	/**
	 * 将缓存值自增1
	 * @param key 缓存键
	 * @return 自增后的值
	 */
	Long increment(String key);

	/**
	 * 获取锁对象
	 * @param key 锁键
	 * @return 锁对象
	 */
	Lock getLock(Object key);

	/**
	 * 使用默认锁实现，确保任务执行的原子性
	 * @param lockKey 锁键
	 * @param runnable 无返回值的任务
	 * @param <E> 可能抛出的异常类型
	 * @throws E 可能抛出的异常
	 */
	default <E extends Throwable> void executeLocked(Object lockKey, CheckedRunnable<E> runnable) throws E {
		executeLocked(lockKey, () -> {
			runnable.run();
			return null;
		});
	}

	/**
	 * 使用默认锁实现，确保任务执行的原子性
	 * @param lockKey 锁键
	 * @param callable 有返回值的任务
	 * @param <T> 任务返回值类型
	 * @param <E> 可能抛出的异常类型
	 * @throws E 可能抛出的异常
	 */
	default <T, E extends Throwable> T executeLocked(Object lockKey, CheckedCallable<T, E> callable) throws E {
		Lock lock = getLock(lockKey);
		lock.lock();
		try {
			return callable.call();
		}
		finally {
			lock.unlock();
		}
	}

	/**
	 * 尝试获取锁并执行无返回值的任务
	 * @param lockKey 锁键
	 * @param waitLockDuration 等待获取锁的超时时间
	 * @param runnable 无返回值的任务
	 * @param <E> 可能抛出的异常类型
	 * @throws E 可能抛出的异常
	 * @throws TimeoutException 如果在指定时间内未能获取锁
	 * @throws InterruptedException 如果当前线程被中断
	 */
	default <E extends Throwable> void executeLocked(Object lockKey, Duration waitLockDuration,
			CheckedRunnable<E> runnable) throws E, InterruptedException, TimeoutException {
		executeLocked(lockKey, waitLockDuration, () -> {
			runnable.run();
			return null;
		});
	}

	/**
	 * 尝试获取锁并执行有返回值的任务
	 * @param lockKey 锁键
	 * @param waitLockDuration 等待获取锁的超时时间
	 * @param callable 有返回值的任务
	 * @param <T> 任务返回值类型
	 * @param <E> 可能抛出的异常类型
	 * @return 任务执行结果
	 * @throws E 可能抛出的异常
	 * @throws TimeoutException 如果在指定时间内未能获取锁
	 * @throws InterruptedException 如果当前线程被中断
	 */
	default <T, E extends Throwable> T executeLocked(Object lockKey, Duration waitLockDuration,
			CheckedCallable<T, E> callable) throws E, TimeoutException, InterruptedException {
		Lock lock = getLock(lockKey);
		if (!lock.tryLock(waitLockDuration.toMillis(), TimeUnit.MILLISECONDS)) {
			throw new TimeoutException(
					"The lock [%s] was not acquired in time: %s".formatted(lockKey, waitLockDuration));
		}

		try {
			return callable.call();
		}
		finally {
			lock.unlock();
		}
	}

}
