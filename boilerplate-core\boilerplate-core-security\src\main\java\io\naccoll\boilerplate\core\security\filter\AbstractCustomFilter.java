package io.naccoll.boilerplate.core.security.filter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.Getter;
import lombok.Setter;
import org.springframework.security.web.servlet.util.matcher.PathPatternRequestMatcher;
import org.springframework.util.CollectionUtils;
import org.springframework.web.filter.GenericFilterBean;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 自定义过滤器抽象类，用于实现具体的过滤逻辑 提供URL匹配和排除功能，支持动态添加匹配规则
 *
 * <AUTHOR>
 */
@Setter
@Getter
public abstract class AbstractCustomFilter extends GenericFilterBean {

	/**
	 * 过滤器实例名称，用于标识区分
	 */
	private String name;

	/**
	 * 过滤器执行顺序，数值越小执行越早
	 */
	private int order;

	/**
	 * URL匹配规则列表，用于匹配需要处理的请求路径
	 */
	private List<PathPatternRequestMatcher> urlPatterns = new ArrayList<>();

	/**
	 * 排除URL匹配规则列表，用于排除不需要处理的请求路径
	 */
	private List<PathPatternRequestMatcher> excludePatterns = new ArrayList<>();

	/**
	 * 添加单个URL匹配规则
	 * @param urlPattern 匹配规则，支持Ant风格
	 */
	public void addUrlPattern(String urlPattern) {
		this.urlPatterns.add(PathPatternRequestMatcher.withDefaults().matcher(urlPattern));
	}

	/**
	 * 添加多个URL匹配规则
	 * @param urlPatterns 匹配规则列表，支持Ant风格
	 */
	public void addUrlPatterns(List<PathPatternRequestMatcher> urlPatterns) {
		this.urlPatterns.addAll(urlPatterns);
	}

	/**
	 * 添加单个排除URL匹配规则
	 * @param excludePattern 排除规则，支持Ant风格
	 */
	public void addExcludePattern(String excludePattern) {
		this.excludePatterns.add(PathPatternRequestMatcher.withDefaults().matcher(excludePattern));
	}

	/**
	 * 批量添加排除URL匹配规则
	 * @param excludePatterns 排除规则列表，支持Ant风格
	 */
	public void addExcludePatternItems(List<String> excludePatterns) {
		if (CollectionUtils.isEmpty(excludePatterns)) {
			return;
		}
		for (String excludePattern : excludePatterns) {
			this.addExcludePattern(excludePattern);
		}
	}

	/**
	 * 添加多个排除URL匹配规则
	 * @param excludePatterns 排除规则列表，支持Ant风格
	 */
	public void addExcludePatterns(List<PathPatternRequestMatcher> excludePatterns) {
		if (CollectionUtils.isEmpty(excludePatterns)) {
			return;
		}
		this.excludePatterns.addAll(excludePatterns);
	}

	/**
	 * 判断是否需要跳过当前过滤器
	 * @param request 当前请求对象
	 * @return 是否跳过
	 */
	public boolean skipDispatch(HttpServletRequest request) {
		if (!CollectionUtils.isEmpty(urlPatterns) && urlPatterns.stream().noneMatch(i -> i.matches(request))) {
			return true;
		}
		return excludePatterns.stream().anyMatch(i -> i.matches(request));
	}

	@Override
	public void doFilter(ServletRequest request, ServletResponse response, FilterChain filterChain)
			throws ServletException, IOException {
		if ((request instanceof HttpServletRequest httpRequest)
				&& (response instanceof HttpServletResponse httpResponse)) {
			if (skipDispatch(httpRequest)) {
				filterChain.doFilter(request, response);
				return;
			}
			doFilterReal(httpRequest, httpResponse, filterChain);
		}
		else {
			throw new ServletException("OncePerRequestFilter only supports HTTP requests");
		}

	}

	/**
	 * 实现具体的过滤逻辑
	 * @param request 请求对象
	 * @param response 响应对象
	 * @param filterChain 过滤器链
	 * @throws ServletException
	 * @throws IOException
	 */
	public abstract void doFilterReal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
			throws ServletException, IOException;

}
