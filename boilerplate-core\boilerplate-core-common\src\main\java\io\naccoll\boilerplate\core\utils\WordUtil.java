package io.naccoll.boilerplate.core.utils;

import org.apache.poi.xwpf.usermodel.*;
import org.springframework.core.io.Resource;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Word工具类
 *
 * <AUTHOR>
 */
public class WordUtil {

	/**
	 * 替换域代码
	 * @param pathResource
	 * @param replaceParamMap
	 * @return
	 * @throws IOException
	 */
	public static byte[] replaceDomainCode(Resource pathResource, Map<String, Object> replaceParamMap)
			throws IOException {
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		var bytes = pathResource.getContentAsByteArray();
		InputStream inputStream = new ByteArrayInputStream(bytes);
		XWPFDocument document = new XWPFDocument(inputStream, true);
		for (XWPFParagraph paragraph : document.getParagraphs()) {
			replaceDomainCodeInParagraph(paragraph, replaceParamMap);
		}

		// 遍历所有表格
		for (XWPFTable table : document.getTables()) {
			for (XWPFTableRow row : table.getRows()) {
				for (XWPFTableCell cell : row.getTableCells()) {
					for (XWPFParagraph paragraph : cell.getParagraphs()) {
						replaceDomainCodeInParagraph(paragraph, replaceParamMap);
					}
				}
			}
		}
		document.write(outputStream);
		bytes = outputStream.toByteArray();
		outputStream.close();
		return bytes;
	}

	public static void replaceDomainCodeInParagraph(XWPFParagraph paragraph, Map<String, Object> replaceParamMap) {
		List<XWPFRun> runs = paragraph.getRuns();
		for (XWPFRun run : runs) {
			String instr = run.text().trim();
			if (instr.startsWith("«") && instr.endsWith("»")) {
				String key = instr.trim().substring("«".length(), instr.lastIndexOf("»")).trim();
				String value = Optional.ofNullable(replaceParamMap.get(key)).map(Object::toString).orElse("");
				run.setText(value, 0);
			}

		}
	}

}
