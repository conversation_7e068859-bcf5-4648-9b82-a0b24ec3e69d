package io.naccoll.boilerplate.core.lock;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 防止重复提交注解 用于标注需要防止重复提交的方法
 *
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface DefendDuplicate {

	/**
	 * 防重的key，不填写时默认获取方法的第一个参数
	 * @return 防重的唯一标识key
	 */
	String key() default "";

	/**
	 * 防重的类型前缀，不指定时获取class+method名称作为prefix
	 * @return 类型前缀，用于区分不同业务场景的防重
	 */
	String prefix() default "";

	/**
	 * 防重静默周期 单位毫秒，默认3000ms（3秒）内不允许重复提交
	 * @return 静默周期毫秒数
	 */
	long silentPeriod() default 3000L;

}
