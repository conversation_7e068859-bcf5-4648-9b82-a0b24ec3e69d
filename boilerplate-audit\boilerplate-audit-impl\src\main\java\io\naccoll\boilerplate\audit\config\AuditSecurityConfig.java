package io.naccoll.boilerplate.audit.config;

import io.naccoll.boilerplate.audit.constant.AuditApiConstant;
import io.naccoll.boilerplate.core.security.customizer.ApiSecurityConfigCustomizer;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.AuthorizeHttpRequestsConfigurer;

/**
 * 审计模块安全配置类
 *
 * 该类用于配置审计模块的安全规则，确保审计相关API的安全访问
 *
 * <AUTHOR>
 */
@Configuration
public class AuditSecurityConfig implements ApiSecurityConfigCustomizer {

	/**
	 * 配置审计相关API的安全规则
	 *
	 * 该方法配置了审计模块所有API的访问权限，要求用户必须认证后才能访问
	 * @param registry 安全规则注册器
	 */
	@Override
	public void customize(
			AuthorizeHttpRequestsConfigurer<HttpSecurity>.AuthorizationManagerRequestMatcherRegistry registry) {
		// @formatter:off
        registry
                .requestMatchers(AuditApiConstant.AUDIT_PREFIX + "/**").fullyAuthenticated();
        // @formatter:on
	}

}
