package io.naccoll.boilerplate.annex.service;

import io.naccoll.boilerplate.annex.dto.OssAnnexCreateCommand;
import io.naccoll.boilerplate.annex.dto.OssAnnexUpdateCommand;
import io.naccoll.boilerplate.annex.model.OssAnnexRefPo;
import jakarta.validation.Valid;
import org.springframework.web.multipart.MultipartFile;

/**
 * 通用附件服务
 *
 * <AUTHOR>
 */
public interface OssAnnexService {

	/**
	 * 创建通用附件
	 * @param command 创建参数
	 * @param file 上传文件
	 * @return 通用附件对象
	 */
	OssAnnexRefPo create(@Valid OssAnnexCreateCommand command, MultipartFile file);

	/**
	 * 更新通用附件
	 * @param command 更新参数
	 * @param file 上传文件
	 * @return 通用附件对象
	 */
	OssAnnexRefPo update(@Valid OssAnnexUpdateCommand command, MultipartFile file);

	/**
	 * 删除通用附件
	 * @param id 通用附件ID
	 */
	void deleteById(Long id);

}
