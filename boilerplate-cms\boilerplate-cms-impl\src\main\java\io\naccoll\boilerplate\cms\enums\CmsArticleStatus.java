package io.naccoll.boilerplate.cms.enums;

import io.naccoll.boilerplate.core.enums.DisplayEnum;

/**
 * 文章状态枚举类.
 *
 * <AUTHOR>
 */
public enum CmsArticleStatus implements DisplayEnum {

	/**
	 * 禁用状态.
	 */
	DISABLE(0, "禁用"),
	/**
	 * 启用状态.
	 */
	ENABLE(1, "启用");

	private final Integer id;

	private final String name;

	/**
	 * 构造函数.
	 * @param id 状态ID
	 * @param name 状态名称
	 */
	CmsArticleStatus(Integer id, String name) {
		this.id = id;
		this.name = name;
	}

	@Override
	public Integer getId() {
		return id;
	}

	@Override
	public String getName() {
		return name;
	}

}
