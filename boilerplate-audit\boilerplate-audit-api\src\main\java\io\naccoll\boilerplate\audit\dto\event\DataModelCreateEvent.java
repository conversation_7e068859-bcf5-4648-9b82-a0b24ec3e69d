package io.naccoll.boilerplate.audit.dto.event;

import io.naccoll.boilerplate.audit.dto.AuditDataModelCreateCommand;
import org.springframework.context.PayloadApplicationEvent;
import org.springframework.core.ResolvableType;

/**
 * 数据模型创建事件类，继承自PayloadApplicationEvent，用于处理数据模型创建相关的事件。 该事件携带了创建数据模型的命令信息。
 *
 * <AUTHOR>
 */
public class DataModelCreateEvent extends PayloadApplicationEvent<AuditDataModelCreateCommand> {

	/**
	 * 数据模型创建事件的构造方法。
	 * @param source 事件源，通常为触发事件的对象
	 * @param payload 包含创建数据模型命令信息的Payload
	 */
	public DataModelCreateEvent(Object source, AuditDataModelCreateCommand payload) {
		super(source, payload);
	}

	/**
	 * 获取事件的可解析类型。
	 * @return 事件的可解析类型，用于Spring事件机制的类型匹配
	 */
	@Override
	public ResolvableType getResolvableType() {
		return ResolvableType.forRawClass(DataModelCreateEvent.class);
	}

}
