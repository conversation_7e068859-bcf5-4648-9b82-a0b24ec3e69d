package io.naccoll.boilerplate.audit.service;

import io.naccoll.boilerplate.audit.dto.AuditStateMachineLogCreateCommand;
import io.naccoll.boilerplate.audit.dto.StateMachineFireEventCommand;
import io.naccoll.boilerplate.core.enums.DisplayEnum;
import io.naccoll.boilerplate.core.enums.EnumHelper;
import io.naccoll.boilerplate.core.exception.ResourceNotFoundException;
import io.naccoll.boilerplate.core.ip.Ip2RegionService;
import io.naccoll.boilerplate.core.lock.UseLock;
import io.naccoll.boilerplate.core.security.authtication.entity.UserDetailsImpl;
import io.naccoll.boilerplate.core.security.authtication.session.SessionHelper;
import io.naccoll.boilerplate.core.statemachine.Transition;
import io.naccoll.boilerplate.core.statemachine.exporter.StateMachinePlantUMLExporter;
import io.naccoll.boilerplate.core.utils.JsonUtil;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.io.BufferedWriter;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 状态机委托服务类，用于管理多个状态机服务实例，并提供统一的状态机操作接口。 该类主要负责状态机事件触发、状态机图像生成、事件类型获取等功能。
 *
 * <AUTHOR>
 */
@Service
@Validated
public class StateMachineDelegateService {

	/**
	 * 状态机服务映射，键为状态机ID，值为对应的状态机服务实例
	 */
	private final Map<String, BaseStateMachineService<?>> stateMachineServices;

	/**
	 * 会话帮助类，用于获取当前用户信息
	 */
	@Resource
	private SessionHelper sessionHelper;

	/**
	 * 审计状态机日志服务，用于记录状态机操作日志
	 */
	@Resource
	private AuditStateMachineLogService auditStateMachineLogService;

	/**
	 * IP地址解析服务，用于获取客户端地址信息
	 */
	@Resource
	private Ip2RegionService ip2RegionService;

	/**
	 * 初始化状态机委托服务
	 * @param stateMachineServices 状态机服务列表
	 */
	public StateMachineDelegateService(List<BaseStateMachineService<?>> stateMachineServices) {
		this.stateMachineServices = stateMachineServices.stream()
			.collect(Collectors.toMap(i -> i.getStateMachine().getMachineId(), i -> i, (a, b) -> b));
	}

	/**
	 * 获取所有状态机ID列表
	 * @return 状态机ID列表
	 */
	public List<String> getAll() {
		return new ArrayList<>(this.stateMachineServices.keySet()).stream().sorted().toList();
	}

	/**
	 * 触发状态机事件
	 * @param command 状态机事件命令
	 */
	@Transactional(rollbackFor = Exception.class)
	@UseLock(prefix = "state-machine-fire-event", key = "#command.machineId+'-'+#command.targetId")
	public void fireEvent(@Valid StateMachineFireEventCommand command) {
		BaseStateMachineService<Object> stateMachineService = getStateMachineServiceById(command.getMachineId());
		DisplayEnum event = EnumHelper.fromId(stateMachineService.getEventType(), command.getEvent());
		Long targetId = command.getTargetId();
		try {
			Object beforeContext = stateMachineService.createContext(targetId);
			String beforeContextJson = JsonUtil.toString(beforeContext);
			DisplayEnum sourceState = stateMachineService.getSourceState();
			DisplayEnum afterState = stateMachineService.fireEvent(event, beforeContext, command.getExtraParams());
			Object afterContext = stateMachineService.createContext(targetId);
			String afterContextJson = JsonUtil.toString(afterContext);
			stateMachineService.removeContext();
			UserDetailsImpl userDetails = sessionHelper.getCurrentUser();
			AuditStateMachineLogCreateCommand logCommand = new AuditStateMachineLogCreateCommand();
			logCommand.setStateMachine(command.getMachineId());
			logCommand.setTargetId(targetId);
			logCommand.setClientIp(userDetails.getIp());
			logCommand.setClientAddress(ip2RegionService.getCityInfo(userDetails.getIp()));
			logCommand.setUserId(userDetails.getId());
			logCommand.setUsername(userDetails.getUsername());
			logCommand.setName(userDetails.getName());
			logCommand.setRealm(userDetails.getRealm().name());
			logCommand.setEvent(command.getEvent());
			logCommand.setBeforeState(sourceState.getId());
			logCommand.setBeforeStateName(sourceState.getName());
			logCommand.setAfterState(afterState.getId());
			logCommand.setAfterStateName(afterState.getName());
			logCommand.setEventName(event.getName());
			logCommand.setBeforeContext(beforeContextJson);
			logCommand.setAfterContext(afterContextJson);
			logCommand.setExtraParams(JsonUtil.toString(command.getExtraParams()));
			auditStateMachineLogService.create(logCommand);
		}
		finally {
			stateMachineService.removeContext();
		}
	}

	/**
	 * 生成状态机图像
	 * @param stateMachineId 状态机ID
	 * @return 状态机图像的PlantUML描述字符串
	 * @throws IOException IO异常
	 */
	public String getImage(String stateMachineId) throws IOException {
		try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
			OutputStreamWriter f = new OutputStreamWriter(outputStream, StandardCharsets.UTF_8);
			StateMachinePlantUMLExporter.export(getStateMachineServiceById(stateMachineId).getStateMachine(),
					stateMachineId, new BufferedWriter(f));
			f.close();
			return outputStream.toString(StandardCharsets.UTF_8);
		}
	}

	/**
	 * 获取状态机事件类型
	 * @param stateMachineId 状态机ID
	 * @return 事件类型枚举类
	 */
	public Class<? extends DisplayEnum> getEventType(String stateMachineId) {
		var stateMachine = getStateMachineServiceById(stateMachineId).getStateMachine();
		return stateMachine.getStates()
			.stream()
			.flatMap(i -> i.getAllTransitions().stream())
			.findFirst()
			.map(Transition::getEvent)
			.map(DisplayEnum::getClass)
			.orElseThrow();
	}

	/**
	 * 根据ID获取状态机服务
	 * @param <T> 泛型类型参数
	 * @param stateMachineId 状态机ID
	 * @return 对应的状态机服务实例
	 * @throws ResourceNotFoundException 状态机服务未找到时抛出
	 */
	@SuppressWarnings("unchecked")
	public <T> BaseStateMachineService<T> getStateMachineServiceById(String stateMachineId) {
		BaseStateMachineService<T> service = (BaseStateMachineService<T>) stateMachineServices.get(stateMachineId);
		return Optional.ofNullable(service).orElseThrow(() -> new ResourceNotFoundException("找不到对应的状态机"));
	}

}
