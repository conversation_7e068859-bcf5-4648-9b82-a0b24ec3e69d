package io.naccoll.boilerplate.audit.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.naccoll.boilerplate.audit.model.AuditOperationLogPo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Objects;

/**
 * 审计操作日志简化DTO类，用于表示审计操作日志的简化信息。 继承自 {@link AuditOperationLogPo}，提供操作日志的基本信息。
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AuditOperationLogSimpleDto extends AuditOperationLogPo {

	/**
	 * 修改前的数据对象，表示操作前的数据状态。
	 */
	@Schema(description = "修改前的数据对象")
	@JsonIgnore
	private String beforeData;

	/**
	 * 修改后的数据对象，表示操作后的新数据状态。
	 */
	@Schema(description = "修改后的数据对象")
	@JsonIgnore
	private String afterData;

	/**
	 * DIFF数据，表示修改前后的数据差异信息。
	 */
	@Schema(description = "DIFF数据")
	@JsonIgnore
	private String diffData;

	/**
	 * 判断是否存在详细数据变化。 通过检查 {@link #diffData} 是否不为空且不等于 "[]" 来判断是否有详细数据变化。
	 * @return 是否存在详细数据变化
	 */
	@JsonProperty(access = JsonProperty.Access.READ_ONLY)
	public boolean getHasDetail() {
		return diffData != null && !Objects.equals(diffData, "[]");
	}

}
