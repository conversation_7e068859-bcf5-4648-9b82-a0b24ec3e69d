package io.naccoll.boilerplate.audit.dto;

import io.naccoll.boilerplate.core.enums.IdableEnum;
import lombok.Data;
import java.util.Map;

/**
 * 状态机上下文命令类，用于在状态机中传递上下文信息
 *
 * @param <T> 业务实体类型
 * <AUTHOR>
 */
@Data
public class StateMachineContextCommand<T> {

	/**
	 * 源状态，表示当前状态机所处的初始状态
	 */
	private IdableEnum sourceState;

	/**
	 * 事件类型，表示触发状态转移的事件
	 */
	private IdableEnum event;

	/**
	 * 业务实体，表示与状态机相关的业务数据
	 */
	private T entity;

	/**
	 * 额外参数，用于传递状态机处理过程中需要的其他信息
	 */
	private Map<String, Object> params;

	/**
	 * 状态机上下文命令构造方法
	 * @param sourceState 源状态
	 * @param event 事件类型
	 * @param entity 业务实体
	 * @param params 额外参数
	 */
	public StateMachineContextCommand(IdableEnum sourceState, IdableEnum event, T entity, Map<String, Object> params) {
		this.sourceState = sourceState;
		this.event = event;
		this.entity = entity;
		this.params = params;
	}

}
