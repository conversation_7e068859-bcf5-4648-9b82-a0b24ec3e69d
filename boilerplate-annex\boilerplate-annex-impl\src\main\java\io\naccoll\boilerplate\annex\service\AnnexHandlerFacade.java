package io.naccoll.boilerplate.annex.service;

import io.naccoll.boilerplate.annex.AnnexHandler;
import io.naccoll.boilerplate.annex.config.AnnexProperties;
import io.naccoll.boilerplate.annex.model.OssAnnexRefPo;
import jakarta.annotation.Resource;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 附件处理器门面类，负责管理所有附件处理器并提供统一接口
 *
 * <AUTHOR>
 */
@Service
@EnableConfigurationProperties(AnnexProperties.class)
public class AnnexHandlerFacade {

	/**
	 * 附件处理器映射，存储所有附件处理器实例
	 */
	private final Map<String, AnnexHandler<?>> annexHandlerMap;

	@Resource
	private AnnexProperties annexProperties;

	/**
	 * 构造函数，初始化附件处理器映射
	 * @param annexHandlers 附件处理器列表
	 */
	public AnnexHandlerFacade(List<AnnexHandler<?>> annexHandlers) {
		this.annexHandlerMap = annexHandlers.stream()
			.collect(Collectors.toMap(AnnexHandler::getHandlerType, i -> i, (a, b) -> b));
	}

	/**
	 * 检查目标是否存在
	 * @param targetType 目标类型
	 * @param targetId 目标ID
	 * @return 是否存在
	 */
	public boolean existTarget(String targetType, String targetId) {
		AnnexHandler<?> handler = annexHandlerMap.get(targetType);
		if (handler == null) {
			if (!annexProperties.isCheckTargetExist()) {
				return true;
			}
			return annexProperties.getWhiteTargets().contains(targetType);
		}
		return handler.findTarget(targetId) != null;
	}

	/**
	 * 单个目标权限检查
	 * @param targetType 目标类型
	 * @param targetId 目标ID
	 * @return 是否有权限
	 */
	public boolean checkPermission(String targetType, String targetId) {
		return checkPermission(targetType, Collections.singletonList(targetId));
	}

	/**
	 * 多个目标权限检查
	 * @param targetType 目标类型
	 * @param targetId 目标ID集合
	 * @return 是否有权限
	 */
	public boolean checkPermission(String targetType, Collection<String> targetId) {
		AnnexHandler<?> handler = annexHandlerMap.get(targetType);
		if (handler == null) {
			if (!annexProperties.isCheckTargetExist()) {
				return true;
			}
			return annexProperties.getWhiteTargets().contains(targetType);
		}
		return handler.checkPermission(targetId);
	}

	/**
	 * 更新前处理
	 * @param targetType 目标类型
	 * @param targetId 目标ID
	 * @param file 上传文件
	 */
	public void beforeUpdate(String targetType, String targetId, MultipartFile file) {
		Optional.ofNullable(annexHandlerMap.get(targetType)).ifPresent(i -> i.beforeUpdate(targetType, targetId, file));
	}

	/**
	 * 上传后处理
	 * @param targetType 目标类型
	 * @param targetId 目标ID
	 * @param file 上传文件
	 */
	public void afterUpload(String targetType, String targetId, MultipartFile file) {
		Optional.ofNullable(annexHandlerMap.get(targetType)).ifPresent(i -> i.afterUpload(targetType, targetId, file));
	}

	/**
	 * 保存前处理
	 * @param annex 附件引用对象
	 */
	public void beforeSave(OssAnnexRefPo annex) {
		Optional.ofNullable(annexHandlerMap.get(annex.getTargetType())).ifPresent(i -> i.beforeSave(annex));
	}

	/**
	 * 保存后处理
	 * @param annex 附件引用对象
	 */
	public void afterSave(OssAnnexRefPo annex) {
		Optional.ofNullable(annexHandlerMap.get(annex.getTargetType())).ifPresent(i -> i.afterSave(annex));
	}

}
