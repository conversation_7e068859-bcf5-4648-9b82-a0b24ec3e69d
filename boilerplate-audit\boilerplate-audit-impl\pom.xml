<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>boilerplate-audit</artifactId>
        <groupId>io.naccoll</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>boilerplate-audit-impl</artifactId>

    <dependencies>
        <dependency>
            <groupId>io.naccoll</groupId>
            <artifactId>boilerplate-core-common</artifactId>
        </dependency>
        <dependency>
            <groupId>io.naccoll</groupId>
            <artifactId>boilerplate-core-web</artifactId>
        </dependency>
        <dependency>
            <groupId>io.naccoll</groupId>
            <artifactId>boilerplate-core-persistence</artifactId>
        </dependency>
        <dependency>
            <groupId>io.naccoll</groupId>
            <artifactId>boilerplate-core-security</artifactId>
        </dependency>
        <dependency>
            <groupId>io.naccoll</groupId>
            <artifactId>boilerplate-sys-api</artifactId>
        </dependency>
        <dependency>
            <groupId>io.naccoll</groupId>
            <artifactId>boilerplate-audit-api</artifactId>
        </dependency>


        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>
