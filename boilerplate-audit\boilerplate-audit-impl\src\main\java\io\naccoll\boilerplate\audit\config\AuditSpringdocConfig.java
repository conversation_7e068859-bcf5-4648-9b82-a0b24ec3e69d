package io.naccoll.boilerplate.audit.config;

import io.naccoll.boilerplate.core.openapi.springdoc.AbstractWebMvcSpringdocConfig;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 审计模块OpenAPI文档配置类
 *
 * 该类用于配置审计模块的OpenAPI文档，定义了两个API组：平台审计API和用户审计API
 *
 * <AUTHOR>
 */
@Configuration
public class AuditSpringdocConfig extends AbstractWebMvcSpringdocConfig {

	/**
	 * 创建平台审计API文档组
	 *
	 * 该方法创建了一个名为"审计-平台"的API组，扫描指定包下的API接口，并添加JWT认证头自定义器
	 * @return GroupedOpenApi 平台审计API文档组
	 */
	@Bean
	public GroupedOpenApi auditPlatformApiDoc() {
		return GroupedOpenApi.builder()
			.group("审计-平台")
			.packagesToScan("io.naccoll.boilerplate.audit.interfaces.api.platform")
			.addOpenApiCustomizer(jwtHeaderOpenApiCustomiser())
			.build();
	}

	/**
	 * 创建用户审计API文档组
	 *
	 * 该方法创建了一个名为"审计-用户"的API组，扫描指定包下的API接口，并添加JWT认证头自定义器
	 * @return GroupedOpenApi 用户审计API文档组
	 */
	@Bean
	public GroupedOpenApi auditUserApiDoc() {
		return GroupedOpenApi.builder()
			.group("审计-用户")
			.packagesToScan("io.naccoll.boilerplate.audit.interfaces.api.user")
			.addOpenApiCustomizer(jwtHeaderOpenApiCustomiser())
			.build();
	}

}
