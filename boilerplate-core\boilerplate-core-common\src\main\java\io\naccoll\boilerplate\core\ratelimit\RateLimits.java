package io.naccoll.boilerplate.core.ratelimit;

import org.springframework.aot.hint.annotation.Reflective;

import java.lang.annotation.*;

/**
 * 限流注解，用于限制方法或类的调用频率 支持配置多个限流规则
 *
 * <AUTHOR>
 */
@Target({ ElementType.TYPE, ElementType.METHOD })
@Retention(RetentionPolicy.RUNTIME)
@Inherited
@Documented
@Reflective
public @interface RateLimits {

	/**
	 * 限流规则数组 配置具体的限流规则
	 */
	RateLimit[] ratelimits() default {};

}
