package io.naccoll.boilerplate.audit.model;

import io.naccoll.boilerplate.core.persistence.model.JpaAuditable;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

/**
 * 状态机日志
 *
 * <AUTHOR>
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_audit_state_machine_log")
public class AuditStateMachineLogPo extends JpaAuditable implements Serializable {

	@Serial
	private static final long serialVersionUID = 1773994149280940032L;

	/**
	 * 状态机日志的唯一标识符
	 */
	@Id
	@Schema(description = "id")
	private Long id;

	/**
	 * 使用的状态机标识符
	 */
	@Schema(description = "使用的状态机")
	private String stateMachine;

	/**
	 * 目标实体的唯一标识符
	 */
	@Schema(description = "目标id")
	private Long targetId;

	/**
	 * 客户端的IP地址
	 */
	@Schema(description = "客户端ip")
	private String clientIp;

	/**
	 * 客户端的地理位置
	 */
	@Schema(description = "客户端地址")
	private String clientAddress;

	/**
	 * 用户的唯一标识符
	 */
	@Schema(description = "用户id")
	private Long userId;

	/**
	 * 用户的登录名
	 */
	@Schema(description = "用户名")
	private String username;

	/**
	 * 用户的真实姓名
	 */
	@Schema(description = "真实姓名")
	private String name;

	/**
	 * 用户所属的域
	 */
	@Schema(description = "用户域")
	private String realm;

	/**
	 * 触发的状态机事件代码
	 */
	@Schema(description = "触发事件")
	private Integer event;

	/**
	 * 触发事件的名称
	 */
	@Schema(description = "事件名称")
	private String eventName;

	/**
	 * 事件触发前的状态代码
	 */
	@Schema(description = "之前的状态")
	private Integer beforeState;

	/**
	 * 事件触发后的状态代码
	 */
	@Schema(description = "之后的状态")
	private Integer afterState;

	/**
	 * 事件触发前的状态上下文信息
	 */
	@Schema(description = "修改前的上下文")
	private String beforeContext;

	/**
	 * 事件触发后的状态上下文信息
	 */
	@Schema(description = "修改后的上下文")
	private String afterContext;

	/**
	 * 事件触发前的状态名称
	 */
	@Schema(description = "之前的状态名称")
	private String beforeStateName;

	/**
	 * 事件触发后的状态名称
	 */
	@Schema(description = "之后的状态名称")
	private String afterStateName;

	/**
	 * 与事件相关的额外参数
	 */
	@Schema(description = "额外的参数")
	private String extraParams;

}
