package io.naccoll.boilerplate.sys.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

/**
 * 系统变量类型创建命令
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class SysVariableTypeCreateCommand {

	/**
	 * 变量类型名
	 */
	@Schema(description = "变量类型名")
	@NotNull(message = "变量类型名不能为空")
	private String name;

	/**
	 * 变量类型编码
	 */
	@Schema(description = "变量类型编码")
	@NotNull(message = "变量类型编码不能为空")
	private String code;

	/**
	 * 变量类型描述
	 */
	@Schema(description = "变量类型描述")
	private String describe;

	/**
	 * 变量类型上级id
	 */
	@Schema(description = "变量类型上级id")
	@NotNull(message = "变量类型上级id不能为空，无上级则设置成0")
	private Long parentId = 0L;

	/**
	 * 状态
	 */
	@Schema(description = "状态")
	@NotNull(message = "状态不能为空")
	private Integer status = 1;

	/**
	 * 排序
	 */
	@Schema(description = "排序")
	@NotNull(message = "排序不能为空")
	private Integer sort = 99;

}
