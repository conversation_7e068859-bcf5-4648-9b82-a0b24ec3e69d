package io.naccoll.boilerplate.cms.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.naccoll.boilerplate.core.persistence.model.AbstractEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

import java.io.Serial;
import java.io.Serializable;
import java.util.Optional;

/**
 * 栏目
 *
 * <AUTHOR>
 */
@SQLDelete(sql = "update t_cms_column set is_deleted = true where id = ?")
@SQLRestriction(value = "is_deleted = false")
@Schema(description = "栏目")
@Table(name = "t_cms_column")
@Entity
@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
public class CmsColumnPo extends AbstractEntity<Long> implements Serializable {

	@Serial
	private static final long serialVersionUID = 235123634652347L;

	/**
	 * 栏目ID
	 */
	@Id
	private Long id;

	/**
	 * 栏目名称
	 */
	@Schema(description = "栏目名称")
	private String name;

	/**
	 * 栏目状态
	 */
	@Schema(description = "栏目状态")
	private Integer status;

	/**
	 * 上级栏目Id
	 */
	@Schema(description = "上级栏目Id")
	private Long parentId;

	/**
	 * 栏目类型, 简单应用中无需区分栏目类型
	 */
	@Schema(description = "栏目类型, 简单应用中无需区分栏目类型 ")
	private Integer type;

	/**
	 * 栏目描述
	 */
	@Schema(description = "栏目描述")
	private String description;

	/**
	 * 栏目logo
	 */
	@Schema(description = "栏目logo")
	private String logo;

	/**
	 * Is root boolean.
	 * @return the boolean
	 */
	@JsonProperty(access = JsonProperty.Access.READ_ONLY)
	public boolean isRoot() {
		return Optional.ofNullable(parentId).map(i -> i <= 0).orElse(true);
	}

	/**
	 * Is leaf boolean.
	 * @return the boolean
	 */
	@JsonProperty(access = JsonProperty.Access.READ_ONLY)
	public boolean isLeaf() {
		return !isRoot();
	}

}
