dependencies {
    api project(":boilerplate-oss:boilerplate-oss-all")

    api project(":boilerplate-core:boilerplate-core-common")
    api project(":boilerplate-core:boilerplate-core-web")
    api project(":boilerplate-core:boilerplate-core-persistence")
    api project(":boilerplate-core:boilerplate-core-security")
    api project(":boilerplate-annex:boilerplate-annex-api")

    testImplementation("org.springframework.boot:spring-boot-starter-test")
}


bootJar {
    enabled = false
}
jar {
    enabled = true
}
