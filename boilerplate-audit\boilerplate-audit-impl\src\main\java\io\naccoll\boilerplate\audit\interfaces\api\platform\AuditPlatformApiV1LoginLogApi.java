package io.naccoll.boilerplate.audit.interfaces.api.platform;

import io.naccoll.boilerplate.audit.constant.AuditApiConstant;
import io.naccoll.boilerplate.audit.dto.AuditLoginLogQueryCondition;
import io.naccoll.boilerplate.audit.model.AuditLoginLogPo;
import io.naccoll.boilerplate.audit.service.AuditLoginLogQueryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 登录日志API控制器 提供登录日志的分页查询接口
 *
 * <AUTHOR>
 */
@RequestMapping(AuditApiConstant.PlatformApiV1.LOGIN)
@RestController
@Tag(name = "登录日志")
public class AuditPlatformApiV1LoginLogApi {

	@Resource
	private AuditLoginLogQueryService auditLoginLogQueryService;

	/**
	 * 分页查询登录日志
	 * @param pageable 分页参数
	 * @param condition 查询条件
	 * @return 登录日志分页结果
	 */
	@PreAuthorize("hasPermission(0L,'GLOBAL','audit/login-log:read')")
	@Operation(summary = "分页查询登录日志")
	@GetMapping("/page")
	public Page<AuditLoginLogPo> page(Pageable pageable, AuditLoginLogQueryCondition condition) {
		return auditLoginLogQueryService.page(pageable, condition);
	}

}
