package io.naccoll.boilerplate.core.security.enums;

/**
 * 安全数据类型枚举
 * <p>
 * 定义需要加密的数据类型，用于标识不同类型的敏感数据
 * </p>
 *
 * <p>
 * 该枚举类用于标识系统中需要进行加密处理的不同数据类型， 包括用户个人信息和系统敏感配置
 * </p>
 *
 * <AUTHOR>
 */
public enum SecurityDataType {

	/**
	 * 手机号码
	 * <p>
	 * 用于标识需要加密处理的用户手机号码
	 * </p>
	 */
	PHONE,

	/**
	 * 身份证号码
	 * <p>
	 * 用于标识需要加密处理的用户身份证号码
	 * </p>
	 */
	ID_CARD,

	/**
	 * 支付配置信息
	 * <p>
	 * 用于标识需要加密处理的支付相关配置信息
	 * </p>
	 */
	PAYMENT_CONFIG,

	/**
	 * 访问密钥
	 * <p>
	 * 用于标识需要加密处理的系统访问密钥
	 * </p>
	 */
	ACCESS_SECRET,

}
