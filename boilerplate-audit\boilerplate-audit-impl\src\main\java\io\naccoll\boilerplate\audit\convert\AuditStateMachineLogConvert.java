package io.naccoll.boilerplate.audit.convert;

import io.naccoll.boilerplate.audit.dto.AuditStateMachineLogDto;
import io.naccoll.boilerplate.audit.dto.AuditStateMachineSimpleLogDto;
import io.naccoll.boilerplate.audit.model.AuditStateMachineLogPo;
import io.naccoll.boilerplate.core.utils.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 状态机日志转换器 将状态机日志的PO对象转换为DTO对象
 *
 * <AUTHOR>
 */
@Component
public class AuditStateMachineLogConvert {

	/**
	 * 将单个状态机日志PO对象转换为DTO对象
	 * @param po 状态机日志PO对象
	 * @return 状态机日志DTO对象
	 */
	public AuditStateMachineLogDto convertAuditStateMachineLogDto(AuditStateMachineLogPo po) {
		return convertAuditStateMachineLogDtoList(List.of(po)).getFirst();
	}

	/**
	 * 将状态机日志PO对象列表转换为DTO对象列表
	 * @param list 状态机日志PO对象列表
	 * @return 状态机日志DTO对象列表
	 */
	public List<AuditStateMachineLogDto> convertAuditStateMachineLogDtoList(List<AuditStateMachineLogPo> list) {
		return list.stream().map(po -> {
			AuditStateMachineLogDto dto = new AuditStateMachineLogDto();
			BeanUtils.copyProperties(po, dto);
			return dto;
		}).toList();
	}

	/**
	 * 将状态机日志PO对象列表转换为简单DTO对象列表
	 * @param list 状态机日志PO对象列表
	 * @return 状态机日志简单DTO对象列表
	 */
	public List<AuditStateMachineSimpleLogDto> convertAuditStateMachineSimpleLogDtoList(
			List<AuditStateMachineLogPo> list) {
		return list.stream().map(po -> {
			AuditStateMachineSimpleLogDto dto = new AuditStateMachineSimpleLogDto();
			BeanUtils.copyProperties(po, dto);
			return dto;
		}).toList();
	}

	/**
	 * 将状态机日志PO对象分页结果转换为DTO对象分页结果
	 * @param page 状态机日志PO对象分页
	 * @return 状态机日志DTO对象分页
	 */
	public Page<AuditStateMachineLogDto> convertAuditStateMachineLogDtoPage(Page<AuditStateMachineLogPo> page) {
		List<AuditStateMachineLogDto> auditStateMachineLogList = convertAuditStateMachineLogDtoList(page.getContent());
		return new PageImpl<>(auditStateMachineLogList, page.getPageable(), page.getTotalElements());
	}

}
