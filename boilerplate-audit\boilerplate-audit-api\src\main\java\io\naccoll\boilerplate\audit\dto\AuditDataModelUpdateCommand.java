package io.naccoll.boilerplate.audit.dto;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 数据审计模型更新命令对象
 * <p>
 * 该类用于封装更新数据审计模型所需的信息，继承自 {@link AuditDataModelCreateCommand}，并增加了唯一标识字段 {@code id}
 * 以实现对特定数据审计模型的精准更新操作。
 * </p>
 *
 * <AUTHOR>
 * @see AuditDataModelCreateCommand
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AuditDataModelUpdateCommand extends AuditDataModelCreateCommand {

	/**
	 * 数据审计模型的唯一标识
	 * <p>
	 * 该字段用于标识需要更新的具体数据审计模型，必须提供且不能为空
	 * </p>
	 */
	@NotNull
	private Long id;

}
