package io.naccoll.boilerplate.core.exception;

import java.io.Serial;

/**
 * 唯一键冲突异常，表示违反数据库唯一性约束，通常由插入或更新操作引起。 该异常表示尝试插入或更新的数据与数据库中已存在的记录冲突。
 *
 * 建议检查输入数据或业务逻辑以确保数据唯一性。
 *
 * <AUTHOR>
 */
public class DuplicateKeyException extends BusinessException {

	@Serial
	private static final long serialVersionUID = 2348912758318709L;

	/**
	 * 构造唯一键冲突异常，用于表示数据插入或更新时的唯一性冲突。
	 * @param code 错误码，用于标识具体的错误类型
	 * @param parameters 与错误相关的参数，用于提供上下文信息
	 */
	public DuplicateKeyException(String code, Object... parameters) {
		super(code, BusinessError.DATA_DUPLICATE, parameters);
	}

	/**
	 * 构造带原始异常的唯一键冲突异常，用于包装底层异常。
	 * @param code 错误码，用于标识具体的错误类型
	 * @param throwable 原始异常，提供更详细的错误上下文
	 * @param parameters 与错误相关的参数，用于提供上下文信息
	 */
	public DuplicateKeyException(String code, Throwable throwable, Object... parameters) {
		super(code, BusinessError.DATA_DUPLICATE, throwable, parameters);
	}

}
