package io.naccoll.boilerplate.core.statemachine;

import io.naccoll.boilerplate.core.statemachine.impl.StateMachineException;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * StateMachineFactory
 *
 * <AUTHOR>
 * @date 2020 -02-08 10:21 PM
 */
public class StateMachineFactory {

	/**
	 * The State machine map. key machineId
	 */
	static final Map<String, StateMachine<?, ?, ?>> STATE_MACHINE_MAP = new ConcurrentHashMap<>();

	private StateMachineFactory() {
	}

	/**
	 * Register.
	 * @param <S> the type parameter
	 * @param <E> the type parameter
	 * @param <C> the type parameter
	 * @param stateMachine the state machine
	 */
	public static <S, E, C> void register(StateMachine<S, E, C> stateMachine) {
		String machineId = stateMachine.getMachineId();
		if (STATE_MACHINE_MAP.get(machineId) != null) {
			throw new StateMachineException(
					"The state machine with id [" + machineId + "] is already built, no need target build again");
		}
		STATE_MACHINE_MAP.put(stateMachine.getMachineId(), stateMachine);
	}

	/**
	 * Get state machine.
	 * @param <S> the type parameter
	 * @param <E> the type parameter
	 * @param <C> the type parameter
	 * @param machineId the machine id
	 * @return the state machine
	 */
	@SuppressWarnings("unchecked")
	public static <S, E, C> StateMachine<S, E, C> get(String machineId) {
		StateMachine<?, ?, ?> stateMachine = STATE_MACHINE_MAP.get(machineId);
		if (stateMachine == null) {
			throw new StateMachineException(
					"There is no stateMachine instance for " + machineId + ", please build it first");
		}
		return (StateMachine<S, E, C>) stateMachine;
	}

}
