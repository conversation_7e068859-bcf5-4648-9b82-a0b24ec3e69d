package io.naccoll.boilerplate.audit.service;

import cn.hutool.core.util.ClassUtil;
import cn.hutool.core.util.ReflectUtil;
import io.naccoll.boilerplate.audit.dto.StateMachineContextCommand;
import io.naccoll.boilerplate.audit.dto.StateMachineTransitionDto;
import io.naccoll.boilerplate.core.dto.Option;
import io.naccoll.boilerplate.core.enums.DisplayEnum;
import io.naccoll.boilerplate.core.enums.EnumHelper;
import io.naccoll.boilerplate.core.exception.ResourceNotFoundException;
import io.naccoll.boilerplate.core.springdoc.ModelUtils;
import io.naccoll.boilerplate.core.statemachine.Action;
import io.naccoll.boilerplate.core.statemachine.State;
import io.naccoll.boilerplate.core.statemachine.StateMachine;
import io.naccoll.boilerplate.core.statemachine.Transition;
import io.naccoll.boilerplate.sys.model.SysVariablePo;
import io.naccoll.boilerplate.sys.service.SysVariableQueryService;
import io.swagger.v3.core.converter.ResolvedSchema;
import jakarta.annotation.Resource;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 状态机服务基类，提供状态机的通用操作和管理功能。
 *
 * @param <T> 业务上下文类型
 * <AUTHOR>
 */
public abstract class BaseStateMachineService<T> {

	/**
	 * 线程本地存储的业务上下文
	 */
	@SuppressWarnings("AlibabaThreadLocalShouldRemove")
	protected final ThreadLocal<T> contextLocal = new ThreadLocal<>();

	/**
	 * 系统变量查询服务，用于获取系统变量配置
	 */
	@Resource
	private SysVariableQueryService sysVariableQueryService;

	/**
	 * 获取事件类型
	 * @return 事件类型枚举类
	 */
	public Class<? extends DisplayEnum> getEventType() {
		return getStateMachine().getStates()
			.stream()
			.flatMap(i -> i.getAllTransitions().stream())
			.findFirst()
			.map(Transition::getEvent)
			.map(DisplayEnum::getClass)
			.orElseThrow();
	}

	/**
	 * 获取状态类型
	 * @return 状态类型枚举类
	 */
	public Class<? extends DisplayEnum> getStateType() {
		return getStateMachine().getStates()
			.stream()
			.findFirst()
			.map(State::getId)
			.map(DisplayEnum::getClass)
			.orElseThrow();
	}

	/**
	 * 获取状态选项列表
	 * @return 状态选项列表
	 */
	public List<Option> getStateOptions() {
		return getStateMachine().getStates()
			.stream()
			.map(State::getId)
			.distinct()
			.map(i -> Option.create(i.getId().toString(), i.getName()))
			.toList();
	}

	/**
	 * 获取事件选项列表
	 * @return 事件选项列表
	 */
	public List<Option> getEventOptions() {
		return getStateMachine().getStates()
			.stream()
			.flatMap(i -> i.getAllTransitions().stream())
			.map(Transition::getEvent)
			.distinct()
			.map(i -> Option.create(i.getId().toString(), i.getName()))
			.toList();
	}

	/**
	 * 获取指定源状态的所有状态转移
	 * @param sourceState 源状态ID
	 * @return 状态转移列表
	 */
	public List<StateMachineTransitionDto> getTransitions(Integer sourceState) {
		var stateMachine = getStateMachine();
		Map<String, SysVariablePo> variables = sysVariableQueryService.listEnableByTypeCode(stateMachine.getMachineId())
			.stream()
			.collect(Collectors.toMap(SysVariablePo::getCode, i -> i, (a, b) -> b));
		return stateMachine.getTransitions()
			.stream()
			.filter(i -> sourceState == null || (i.getSource().getId().getId().equals(sourceState)))
			.map(transition -> buildTransitionResult(transition, variables))
			.toList();
	}

	/**
	 * 构建状态转移结果
	 * @param transition 状态转移
	 * @param variableMap 系统变量映射
	 * @return 状态转移DTO
	 */
	protected StateMachineTransitionDto buildTransitionResult(
			Transition<DisplayEnum, DisplayEnum, StateMachineContextCommand<T>> transition,
			Map<String, SysVariablePo> variableMap) {
		DisplayEnum event = transition.getEvent();
		DisplayEnum fromState = transition.getSource().getId();
		DisplayEnum targetState = transition.getTarget().getId();
		String key = String.format("%d-%d-%d", fromState.getId(), event.getId(), targetState.getId());
		List<ResolvedSchema> schemas = Optional.ofNullable(transition.getActions())
			.orElse(new ArrayList<>())
			.stream()
			.map(Action::getParameterType)
			.filter(i -> i != null && !ClassUtil.isSimpleValueType(i) && !Void.class.equals(i))
			.map(ModelUtils::resolveSchema)
			.toList();
		StateMachineTransitionDto dto = new StateMachineTransitionDto();
		dto.setEventCode(event.getId());
		dto.setEventName(event.getName());
		dto.setFromStateCode(fromState.getId());
		dto.setFromStateName(fromState.getName());
		dto.setTargetStateCode(targetState.getId());
		dto.setTargetStateName(targetState.getName());
		dto.setMetadata(Optional.ofNullable(variableMap.get(key)).map(SysVariablePo::getMetadata).orElse(null));
		dto.setParameterTypes(schemas);
		return dto;
	}

	/**
	 * 触发状态机事件
	 * @param event 事件
	 * @param entity 业务实体
	 * @param params 事件参数
	 * @return 事件触发后的目标状态
	 */
	public DisplayEnum fireEvent(DisplayEnum event, T entity, Map<String, Object> params) {
		DisplayEnum sourceState = getSourceState();
		StateMachineContextCommand<T> context = new StateMachineContextCommand<>(sourceState, event, entity, params);
		beforeHook(context);
		DisplayEnum afterState = getStateMachine().fireEvent(sourceState, event, context);
		afterHook(afterState, context);
		return afterState;
	}

	/**
	 * 状态转换前的钩子方法，用于在状态转换前执行自定义逻辑
	 * @param context 状态机上下文
	 */
	protected void beforeHook(StateMachineContextCommand<T> context) {

	}

	/**
	 * 状态转换后的钩子方法，用于在状态转换后执行自定义逻辑
	 * @param afterState 转换后的状态
	 * @param context 状态机上下文
	 */
	protected abstract void afterHook(DisplayEnum afterState, StateMachineContextCommand<T> context);

	/**
	 * 创建业务上下文
	 * @param id 业务实体ID
	 * @return 业务上下文
	 */
	public T createContext(Long id) {
		T context = loadContextEntity(id);
		if (context == null) {
			throw new ResourceNotFoundException("未找到对应的记录");
		}
		this.contextLocal.set(context);
		return context;
	}

	/**
	 * 移除业务上下文
	 */
	public void removeContext() {
		this.contextLocal.remove();
	}

	/**
	 * 加载业务上下文实体
	 * @param id 业务实体ID
	 * @return 业务上下文实体
	 */
	protected abstract T loadContextEntity(Long id);

	/**
	 * 获取当前源状态
	 * @return 当前源状态
	 */
	public DisplayEnum getSourceState() {
		T object = contextLocal.get();
		return EnumHelper.fromId(getStateType(),
				Integer.parseInt(ReflectUtil.getFieldValue(object, getStateField()).toString()));
	}

	/**
	 * 获取状态字段名称
	 * @return 状态字段名称
	 */
	protected abstract String getStateField();

	/**
	 * 获取状态机实例
	 * @param <S> 状态类型
	 * @param <E> 事件类型
	 * @return 状态机实例
	 */
	protected abstract <S extends DisplayEnum, E extends DisplayEnum> StateMachine<S, E, StateMachineContextCommand<T>> getStateMachine();

}
