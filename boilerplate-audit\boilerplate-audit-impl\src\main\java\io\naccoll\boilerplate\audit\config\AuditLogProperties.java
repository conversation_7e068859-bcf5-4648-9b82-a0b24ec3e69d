package io.naccoll.boilerplate.audit.config;

import io.naccoll.boilerplate.core.audit.enums.AuditStorageType;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Arrays;
import java.util.List;

/**
 * 审计日志配置属性类
 *
 * 该类用于配置审计日志的相关属性，包括操作审计、登录审计和HTTP审计的配置
 *
 * <AUTHOR>
 */
@ConfigurationProperties(prefix = "custom.security.audit")
@Data
public class AuditLogProperties {

	/**
	 * 操作审计配置
	 */
	private Operation operation = new Operation();

	/**
	 * 登录审计配置
	 */
	private Login login = new Login();

	/**
	 * HTTP审计配置
	 */
	private AuditLogProperties.Http http = new Http();

	/**
	 * 操作审计配置类
	 *
	 * 该类包含操作审计的相关配置项
	 *
	 * <AUTHOR>
	 */
	@Data
	public static class Operation {

		/**
		 * 是否启用操作审计，默认启用
		 */
		private boolean enabled = true;

		/**
		 * 是否启用数据审计，默认启用
		 */
		private boolean dataAudit = true;

		/**
		 * 支持的审计存储类型，默认支持所有类型
		 */
		private List<AuditStorageType> type = Arrays.asList(AuditStorageType.values());

	}

	/**
	 * 登录审计配置类
	 *
	 * 该类包含登录审计的相关配置项
	 *
	 * <AUTHOR>
	 */
	@Data
	public static class Login {

		/**
		 * 是否启用登录审计，默认启用
		 */
		private boolean enabled = true;

		/**
		 * 支持的审计存储类型，默认支持所有类型
		 */
		private List<AuditStorageType> type = Arrays.asList(AuditStorageType.values());

	}

	/**
	 * HTTP审计配置类
	 *
	 * 该类包含HTTP审计的相关配置项
	 *
	 * <AUTHOR>
	 */
	@Data
	public static class Http {

		/**
		 * 是否启用HTTP审计，默认启用
		 */
		private boolean enabled = true;

		/**
		 * 支持的审计存储类型，默认支持所有类型
		 */
		private List<AuditStorageType> type = Arrays.asList(AuditStorageType.values());

	}

}
