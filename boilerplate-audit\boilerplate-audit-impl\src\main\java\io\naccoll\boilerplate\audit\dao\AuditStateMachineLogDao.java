package io.naccoll.boilerplate.audit.dao;

import io.naccoll.boilerplate.audit.dto.AuditStateMachineLogQueryCondition;
import io.naccoll.boilerplate.audit.model.AuditStateMachineLogPo;
import io.naccoll.boilerplate.core.persistence.dao.BaseDao;
import io.naccoll.boilerplate.core.persistence.jpa.specification.Specifications;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Collection;
import java.util.List;

/**
 * 状态机日志数据库访问层
 *
 * <AUTHOR>
 */
public interface AuditStateMachineLogDao extends BaseDao<AuditStateMachineLogPo, Long> {

	/**
	 * 查询AuditStateMachineLog列表
	 * @param condition AuditStateMachineLog查询条件
	 * @return AuditStateMachineLog列表 list
	 */
	default List<AuditStateMachineLogPo> findAll(AuditStateMachineLogQueryCondition condition) {
		Specification<AuditStateMachineLogPo> spec = buildSpecification(condition);
		return findAll(spec);
	}

	/**
	 * 查询AuditStateMachineLog分页
	 * @param condition AuditStateMachineLog查询条件
	 * @param pageable the pageable
	 * @return AuditStateMachineLog分页 page
	 */
	default Page<AuditStateMachineLogPo> page(AuditStateMachineLogQueryCondition condition, Pageable pageable) {
		Specification<AuditStateMachineLogPo> spec = buildSpecification(condition);
		return findAll(spec, pageable);
	}

	/**
	 * Build specification specification.
	 * @param condition the condition
	 * @return the specification
	 */
	default Specification<AuditStateMachineLogPo> buildSpecification(AuditStateMachineLogQueryCondition condition) {
		return Specifications.builder(AuditStateMachineLogPo.class)
			.eq(AuditStateMachineLogPo::getTargetId, condition.getTargetId())
			.eq(AuditStateMachineLogPo::getStateMachine, condition.getStateMachine());
	}

	/**
	 * 根据状态机和目标ID集合查询最后一条状态机日志
	 * @param stateMachine 状态机名称
	 * @param targetIds 目标ID集合
	 * @return 最后一条状态机日志列表
	 */
	@Query("""
			SELECT t
			FROM AuditStateMachineLogPo t
			WHERE t.targetId IN (:targetIds)
			AND t.stateMachine=:stateMachine
			AND NOT EXISTS (
			    SELECT 1
			    FROM AuditStateMachineLogPo t2
			    WHERE t2.targetId = t.targetId
			    AND t.stateMachine=:stateMachine
			    AND t2.id > t.id
			)
			""")
	List<AuditStateMachineLogPo> findLastByStateMachineAndTargetIds(@Param("stateMachine") String stateMachine,
			@Param("targetIds") Collection<Long> targetIds);

}
