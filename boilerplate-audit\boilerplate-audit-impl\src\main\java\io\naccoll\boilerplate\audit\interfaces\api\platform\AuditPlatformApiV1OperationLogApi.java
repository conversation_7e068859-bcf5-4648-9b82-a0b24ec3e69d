package io.naccoll.boilerplate.audit.interfaces.api.platform;

import io.naccoll.boilerplate.audit.constant.AuditApiConstant;
import io.naccoll.boilerplate.audit.convert.AuditOperationLogConvert;
import io.naccoll.boilerplate.audit.dto.AuditOperationLogDto;
import io.naccoll.boilerplate.audit.dto.AuditOperationLogQueryCondition;
import io.naccoll.boilerplate.audit.dto.AuditOperationLogSimpleDto;
import io.naccoll.boilerplate.audit.service.AuditOperationLogQueryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 操作日志API控制器 提供操作日志的分页查询和单条查询接口
 *
 * <AUTHOR>
 */
@RequestMapping(AuditApiConstant.PlatformApiV1.OPERATION)
@RestController
@Tag(name = "操作日志")
public class AuditPlatformApiV1OperationLogApi {

	@Resource
	private AuditOperationLogQueryService auditLogQueryService;

	@Resource
	private AuditOperationLogConvert auditOperationLogConvert;

	/**
	 * 分页查询操作日志
	 * @param pageable 分页参数
	 * @param condition 查询条件
	 * @return 操作日志分页结果
	 */
	@PreAuthorize("hasPermission(0L,'GLOBAL','audit/operation-log:read')")
	@Operation(summary = "分页查询操作日志")
	@GetMapping("/page")
	public Page<AuditOperationLogSimpleDto> page(Pageable pageable, AuditOperationLogQueryCondition condition) {
		return auditOperationLogConvert
			.convertAuditOperationLogDtoPage(auditLogQueryService.queryPage(pageable, condition));
	}

	/**
	 * 查询单条操作日志
	 * @param id 操作日志ID
	 * @return 操作日志DTO对象
	 */
	@PreAuthorize("hasPermission(0L,'GLOBAL','audit/operation-log:read')")
	@Operation(summary = "查询操作日志")
	@GetMapping("/{id}")
	public AuditOperationLogDto page(@PathVariable Long id) {
		return auditOperationLogConvert.convertAuditOperationLogDto(auditLogQueryService.findByIdNotNull(id));
	}

}
