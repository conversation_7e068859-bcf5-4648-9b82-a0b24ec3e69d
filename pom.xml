<?xml version="1.0" encoding="UTF-8" ?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>
    <modules>
        <module>boilerplate-audit</module>
        <module>boilerplate-server</module>
        <module>boilerplate-oss</module>
        <module>boilerplate-annex</module>
        <module>boilerplate-messenger</module>
        <module>boilerplate-ocr</module>
        <module>boilerplate-sys</module>
        <module>boilerplate-cms</module>
        <module>boilerplate-payment</module>
        <module>boilerplate-core</module>
        <module>boilerplate-monitor</module>
        <module>boilerplate-generator</module>
        <module>boilerplate-customer</module>
        <module>boilerplate-thirdparty</module>
        <module>boilerplate-workflow</module>

    </modules>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.5.3</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>io.naccoll</groupId>
    <artifactId>boilerplate</artifactId>
    <version>${revision}</version>
    <name>boilerplate</name>
    <description>Boilerplate project for Spring Boot</description>

    <properties>
        <revision>0.0.1-SNAPSHOT</revision>

        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <file.encoding>UTF-8</file.encoding>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <java.version>21</java.version>

        <alipay-java.version>2.14.7</alipay-java.version>
        <aliyun.oss.version>3.18.1</aliyun.oss.version>
        <aliyun.sms.version>3.1.1</aliyun.sms.version>
        <bcpkix.version>1.80</bcpkix.version>
        <bcprov.version>1.80</bcprov.version>
        <camunda.version>7.22.0</camunda.version>
        <commons-io.version>2.18.0</commons-io.version>
        <datasource.decorator.starter.version>1.10.0</datasource.decorator.starter.version>
        <fastjson.version>1.2.83</fastjson.version>
        <guava.version>33.4.0-jre</guava.version>
        <httpclient.version>4.5.14</httpclient.version>
        <hutool.version>5.8.36</hutool.version>
        <hypersistenceutils.version>3.9.2</hypersistenceutils.version>
        <ip2region.version>2.7.0</ip2region.version>
        <knife4j.version>4.5.0</knife4j.version>
        <kryo.version>5.6.2</kryo.version>
        <minio.client.version>8.5.17</minio.client.version>
        <mybatis.starter.version>3.0.4</mybatis.starter.version>
        <okhttp.version>4.12.0</okhttp.version>
        <okio.version>3.10.2</okio.version>
        <openfeign.version>13.5</openfeign.version>
        <pagehelper.version>2.1.0</pagehelper.version>
        <pdfbox.version>2.0.33</pdfbox.version>
        <parsson.version>1.1.7</parsson.version>
        <poi.version>5.4.0</poi.version>
        <spring.boot.admin.version>3.4.5</spring.boot.admin.version>
        <springdoc.version>2.8.6</springdoc.version>
        <tencent.oss.version>*********</tencent.oss.version>
        <tencent.sdk.version>3.1.1228</tencent.sdk.version>
        <weixin-java.version>4.7.0</weixin-java.version>
    </properties>

    <pluginRepositories>
        <pluginRepository>
            <id>alimaven</id>
            <name>aliyun maven</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </pluginRepository>
        <pluginRepository>
            <id>spring-plugins-release</id>
            <url>https://maven.aliyun.com/repository/spring-plugin</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </pluginRepository>
        <pluginRepository>
            <id>spring-milestones</id>
            <url>https://repo.spring.io/milestone</url>
        </pluginRepository>
        <pluginRepository>
            <id>maven mirror</id>
            <url>https://repo1.maven.org/maven2</url>
        </pluginRepository>
    </pluginRepositories>

    <repositories>
        <repository>
            <id>alimaven</id>
            <name>aliyun maven</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
        <repository>
            <id>alimaven spring</id>
            <name>aliyun maven spring</name>
            <url>https://maven.aliyun.com/repository/spring</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
        <repository>
            <id>spring-milestones</id>
            <url>https://repo.spring.io/milestone</url>
        </repository>
        <repository>
            <id>maven mirror</id>
            <url>https://repo1.maven.org/maven2</url>
        </repository>
    </repositories>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.squareup.okio</groupId>
                <artifactId>okio-bom</artifactId>
                <version>${okio.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.flowable/flowable-bom -->
            <dependency>
                <groupId>org.camunda.bpm</groupId>
                <artifactId>camunda-only-bom</artifactId>
                <version>${camunda.version}</version>
                <type>pom</type>
                <scope>import</scope>
                <exclusions>
                    <exclusion>
                        <groupId>org.mybatis</groupId>
                        <artifactId>mybatis</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk18on</artifactId>
                <version>${bcprov.version}</version>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcpkix-jdk18on</artifactId>
                <version>${bcpkix.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tencentcloudapi</groupId>
                <artifactId>tencentcloud-sdk-java-common</artifactId>
                <version>${tencent.sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>io.github.openfeign</groupId>
                <artifactId>feign-bom</artifactId>
                <version>${openfeign.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>${httpclient.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-core-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-core-integrate</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-core-persistence</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-core-web</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-core-redis</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-core-security</artifactId>
                <version>${project.version}</version>
            </dependency>


            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-audit</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-audit-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-audit-impl</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-monitor</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-monitor-all</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-codecentric-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-codecentric-admin</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-oss</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-oss-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-oss-minio</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-oss-all</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-oss-aliyun</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-oss-tencent</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-oss-localfile</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-oss-local</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-annex-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-annex-impl</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-messenger</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-ocr</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-sys</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-sys-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-thirdparty-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-sys-wx-miniapp</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-sys-wx-mp</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-sys-impl</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-cms</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-cms-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-cms-impl</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-payment</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-payment-all</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-payment-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-payment-alipay</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-payment-wechat</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-generator</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-customer</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-customer-all</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-customer-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-customer-wx-mp</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-customer-wx-miniapp</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-workflow</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-workflow-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-workflow-impl</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-wx-mp</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-wx-miniapp</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.naccoll</groupId>
                <artifactId>boilerplate-server</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <!--Lombok-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.graalvm.buildtools</groupId>
                <artifactId>native-maven-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>io.spring.javaformat</groupId>
                <artifactId>spring-javaformat-maven-plugin</artifactId>
                <version>0.0.43</version>
                <executions>
                    <execution>
                        <phase>validate</phase>
                        <inherited>true</inherited>
                        <goals>
                            <goal>validate</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.rudikershaw.gitbuildhook</groupId>
                <artifactId>git-build-hook-maven-plugin</artifactId>
                <version>3.5.0</version>
                <configuration>
                    <gitConfig>
                        <core.hooksPath>.mvn/git-hooks</core.hooksPath>
                        <custom.configuration>true</custom.configuration>
                    </gitConfig>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>initialize</goal>
                            <goal>configure</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.owasp</groupId>
                <artifactId>dependency-check-maven</artifactId>
                <version>9.0.9</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <inherited>true</inherited>
                <configuration>
                    <excludes>
                        <exclude>**/*ManualTest.java</exclude>
                    </excludes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <version>2.18.0</version>
            </plugin>
        </plugins>
    </build>
</project>
