package io.naccoll.boilerplate.audit.service.impl;

import io.naccoll.boilerplate.audit.dao.AuditStateMachineLogDao;
import io.naccoll.boilerplate.audit.dto.AuditStateMachineLogQueryCondition;
import io.naccoll.boilerplate.audit.model.AuditStateMachineLogPo;
import io.naccoll.boilerplate.audit.service.AuditStateMachineLogQueryService;
import io.naccoll.boilerplate.core.exception.ResourceNotFoundException;
import io.naccoll.boilerplate.core.persistence.dao.DaoUtils;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 状态机日志查询服务实现
 *
 * <AUTHOR>
 */
@Service
public class AuditStateMachineLogQueryServiceImpl implements AuditStateMachineLogQueryService {

	@Resource
	private AuditStateMachineLogDao auditStateMachineLogDao;

	@Override
	public Page<AuditStateMachineLogPo> page(AuditStateMachineLogQueryCondition condition, Pageable pageable) {
		return auditStateMachineLogDao.page(condition, pageable);
	}

	@Override
	public List<AuditStateMachineLogPo> findAll(AuditStateMachineLogQueryCondition condition) {
		return auditStateMachineLogDao.findAll(condition);
	}

	@Override
	public AuditStateMachineLogPo findById(Long id) {
		return auditStateMachineLogDao.findById(id).orElse(null);
	}

	@Override
	public AuditStateMachineLogPo findByIdNotNull(Long id) {
		return auditStateMachineLogDao.findById(id).orElseThrow(() -> new ResourceNotFoundException("状态机日志不存在"));
	}

	@Override
	public List<AuditStateMachineLogPo> findByIds(Collection<Long> ids) {
		return DaoUtils.findByPrimaryKeyIn(auditStateMachineLogDao, ids);
	}

	@Override
	public Map<Long, AuditStateMachineLogPo> findMapByIds(Collection<Long> ids) {
		return DaoUtils.findMapByPrimaryKeyIn(auditStateMachineLogDao, ids);
	}

	@Override
	public Map<Long, AuditStateMachineLogPo> findLastByStateMachineAndTargetId(String stateMachine,
			Collection<Long> targetIds) {
		if (CollectionUtils.isEmpty(targetIds)) {
			return new HashMap<>(10);
		}
		return auditStateMachineLogDao.findLastByStateMachineAndTargetIds(stateMachine, targetIds)
			.stream()
			.collect(Collectors.toMap(AuditStateMachineLogPo::getTargetId, i -> i, (a, b) -> b));
	}

}
