package io.naccoll.boilerplate.audit.dao;

import jakarta.annotation.Resource;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;

import java.util.Arrays;

/**
 * The type Audit state machine log dao test.
 *
 * <AUTHOR>
 */
@DataJpaTest
class AuditStateMachineLogDaoTest {

	@Resource
	private AuditStateMachineLogDao auditStateMachineLogDao;

	/**
	 * Find last by state machine and target ids.
	 */
	@Test
	void findLastByStateMachineAndTargetIds() {
		Assertions
			.assertThat(auditStateMachineLogDao.findLastByStateMachineAndTargetIds("test", Arrays.asList(1L, 2L, 3L)))
			.isEmpty();
	}

}
