package io.naccoll.boilerplate.core.persistence.mybatis;

import io.naccoll.boilerplate.core.exception.ClientException;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.StatementVisitorAdapter;
import net.sf.jsqlparser.statement.Statements;
import net.sf.jsqlparser.statement.select.Select;
import net.sf.jsqlparser.util.deparser.StatementDeParser;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * MyBatis SQL解析工具类，用于解析和验证MyBatis SQL模板
 *
 * <AUTHOR>
 */
@Slf4j
public class MybatisSqlParser {

	/**
	 * 私有构造方法，防止实例化
	 */
	private MybatisSqlParser() {
	}

	/**
	 * 验证SQL模板是否合法
	 * @param sqlTemplate SQL模板
	 * @throws ClientException 如果SQL模板非法，抛出客户端异常
	 */
	public static void validateSql(String sqlTemplate) {
		sqlTemplate = sqlTemplate.trim();
		String standardSqlTemplate = renderMybatisTemplateToSql(sqlTemplate);
		try {
			Statements statement = CCJSqlParserUtil.parseStatements(standardSqlTemplate);
			StatementVisitorAdapter deparser = new StatementVisitorAdapter();

			statement.accept(deparser);
		}
		catch (JSQLParserException e) {
			throw new ClientException("错误：" + e.getMessage() + "\n非法的SQL模板: " + standardSqlTemplate);
		}
	}

	/**
	 * 检查SQL模板是否为SELECT语句
	 * @param sqlTemplate SQL模板
	 * @return 如果是SELECT语句返回true，否则返回false
	 */
	public static boolean validateSelectSql(String sqlTemplate) {
		sqlTemplate = sqlTemplate.trim();
		String standardSqlTemplate = renderMybatisTemplateToSql(sqlTemplate);
		try {
			Statement statement = CCJSqlParserUtil.parse(standardSqlTemplate);
			StatementDeParser deparser = new StatementDeParser(new StringBuilder());
			statement.accept(deparser);
			return statement instanceof Select;
		}
		catch (JSQLParserException e) {
			return false;
		}
	}

	/**
	 * 将MyBatis模板转换为标准SQL语句
	 * @param mybatisTemplate MyBatis SQL模板
	 * @return 转换后的标准SQL语句
	 */
	public static String renderMybatisTemplateToSql(String mybatisTemplate) {
		mybatisTemplate = mybatisTemplate.replaceAll("--.*", "");

		// 使用正则表达式匹配<if>标签和参数，并将参数名存储到paramList中
		String ifPattern = "<if\\s+test=\"([^\"]*+)\">((?:(?!</if>).)*+)</if>";
		Pattern ifRegex = Pattern.compile(ifPattern, Pattern.DOTALL);
		List<String> paramList = new ArrayList<>();
		Matcher ifMatcher = ifRegex.matcher(mybatisTemplate);
		while (ifMatcher.find()) {
			String paramName = ifMatcher.group(1).trim();
			paramList.add(paramName);
			mybatisTemplate = mybatisTemplate.replaceAll("<if\\s+test=\"(.*?)\">", " ").replaceAll("</\\s*if>", "");
		}

		String foreachPattern = "<foreach\\s++([^>]++)>((?>[^<]++)++)</foreach>";
		Pattern foreachRegex = Pattern.compile(foreachPattern, Pattern.DOTALL);
		// 使用正则表达式匹配<foreach>标签并生成对应的SQL语句片段
		Matcher foreachMatcher = foreachRegex.matcher(mybatisTemplate);
		while (foreachMatcher.find()) {

			String elementAttributes = foreachMatcher.group(1);
			String elementBody = foreachMatcher.group(2).trim();

			// 使用更灵活的正则表达式来提取属性值
			String separator = extractAttributeValue(elementAttributes, "separator");
			String open = extractAttributeValue(elementAttributes, "open");
			String close = extractAttributeValue(elementAttributes, "close");

			// 获取集合参数的值
			// 在这里可以处理集合参数的情况，但是如果不依赖外部参数传递，那么需要构造一个集合来处理
			// 这里仅作示例，你可以根据实际情况修改
			String innerSqlTemplate = elementBody;

			// 在这里可以构造一个示例集合参数
			List<String> exampleCollection = Arrays.asList("Item1", "Item2");

			StringBuilder foreachSql = new StringBuilder();

			foreachSql.append(StringUtils.hasLength(open) ? open : "");
			for (int i = 0; i < exampleCollection.size(); i++) {
				String renderedInnerSql = renderMybatisTemplateToSql(innerSqlTemplate);
				foreachSql.append(renderedInnerSql);

				if (i < exampleCollection.size() - 1) {
					foreachSql.append(separator);
				}
			}
			foreachSql.append(StringUtils.hasLength(close) ? close : "");
			// 替换<foreach>标签为生成的SQL语句片段
			mybatisTemplate = mybatisTemplate.replace(foreachMatcher.group(), foreachSql.toString());
		}

		// 使用正则表达式替换#{paramName}占位符为?
		String paramPattern = "#\\{(.*?)\\}";
		Pattern paramRegex = Pattern.compile(paramPattern);
		Matcher paramMatcher = paramRegex.matcher(mybatisTemplate);
		int paramIndex = 1;
		while (paramMatcher.find()) {
			String replacement = "?";
			// 根据参数名的位置替换为对应的?
			if (paramIndex <= paramList.size()) {
				replacement = "?";
			}
			mybatisTemplate = mybatisTemplate.replaceFirst(paramPattern, replacement);
			paramIndex++;
		}

		String cdataPattern = "<!\\[CDATA\\[([^\\]]*?)\\]\\]>";
		Pattern cdataRegex = Pattern.compile(cdataPattern, Pattern.DOTALL);
		Matcher cdataMatcher = cdataRegex.matcher(mybatisTemplate);
		while (cdataMatcher.find()) {
			String cdataContent = cdataMatcher.group(1).trim();
			mybatisTemplate = mybatisTemplate.replaceAll(cdataPattern, cdataContent);
		}

		// 替换<where>和</where>标签，同时删除所有多余的空格和换行符
		mybatisTemplate = mybatisTemplate.replaceAll("<where\\s*>", "WHERE").replaceAll("</\\s*where\\s*>", "");
		mybatisTemplate = mybatisTemplate.trim();

		return mybatisTemplate;
	}

	/**
	 * 从元素属性中提取指定属性的值
	 * @param elementAttributes 元素属性字符串
	 * @param attributeName 要提取的属性名
	 * @return 属性值，如果未找到则返回null
	 */
	private static String extractAttributeValue(String elementAttributes, String attributeName) {
		// 使用正则表达式提取属性值
		String regex = attributeName + "\\s*=\\s*['\"](.*?)['\"]";
		Pattern pattern = Pattern.compile(regex);
		Matcher matcher = pattern.matcher(elementAttributes);
		if (matcher.find()) {
			return matcher.group(1);
		}
		return null;
	}

}
