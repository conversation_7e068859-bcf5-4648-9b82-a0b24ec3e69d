buildscript {
    ext {
        springBootVersion = '3.5.3'
        springDependencyVersion = '1.1.6'

        set('okhttp.version', '4.12.0')

        alipayJavaVersion = '2.14.7'
        aliyunOssVersion = '3.18.1'
        aliyunSmsVersion = '3.1.1'
        bcpkixVersion = '1.80'
        bcprovVersion = '1.80'
        camundaVersion = '7.22.0'
        commonIoVersion = '2.18.0'
        datasourceDecoratorStarterVersion = '1.10.0'
        fastjsonVersion = '1.2.83'
        guavaVersion = '33.4.0-jre'
        httpClientVersion = "4.5.14"
        hutoolVersion = '5.8.36'
        hypersistenceutilsVersion = '3.9.2'
        ip2RegionVersion = '2.7.0'
        knife4jVersion = '4.5.0'
        kryoVersion = '5.6.2'
        minioClientVersion = '8.5.17'
        mybatisStarterVersion = '3.0.4'
        okioVersion = '3.10.2'
        openfeignVersion = '13.5'
        pagehelperVersion = '2.1.0'
        parssonVersion = "1.1.7"
        pdfboxVersion = '2.0.33'
        poiVersion = "5.4.0"
        springBootAdminVersion = '3.4.5'
        springdocVersion = '2.8.6'
        tencentOssVersion = '*********'
        tencentSdkVersion = '3.1.1228'
        weixinJavaVersion = '4.7.0'

    }
    repositories {
        maven {
            url = 'https://maven.aliyun.com/repository/public'
        }
        maven {
            url = 'https://maven.aliyun.com/repository/spring'
        }
        maven {
            url = 'https://maven.aliyun.com/repository/spring-plugin'
        }
        maven {
            url = 'https://maven.aliyun.com/repository/gradle-plugin'
        }
        maven {
            url = 'https://repo.spring.io/milestone'
        }
        gradlePluginPortal()
        mavenCentral()
    }
    dependencies {
        classpath("org.springframework.boot:spring-boot-gradle-plugin:${springBootVersion}")
        classpath "io.spring.gradle:dependency-management-plugin:${springDependencyVersion}"
        classpath("io.spring.javaformat:spring-javaformat-gradle-plugin:0.0.43")
        classpath("org.owasp:dependency-check-gradle:9.0.9")
        classpath("org.graalvm.buildtools.native:org.graalvm.buildtools.native.gradle.plugin:0.10.4")
    }
}

allprojects {
    apply plugin: 'org.springframework.boot'
    apply plugin: 'io.spring.dependency-management'
    apply plugin: 'java'
    apply plugin: 'io.spring.javaformat'
    apply plugin: 'org.owasp.dependencycheck'

    group = 'io.naccoll'
    version = '0.0.1-SNAPSHOT'
    java {
        toolchain {
            languageVersion = JavaLanguageVersion.of(21)
        }
    }
    configurations {
        compileOnly {
            extendsFrom annotationProcessor
        }
    }

    repositories {
        mavenLocal()
        maven {
            url = 'https://maven.aliyun.com/repository/public'
        }
        maven {
            url = 'https://maven.aliyun.com/repository/spring'
        }
        maven {
            url = 'https://maven.aliyun.com/repository/spring-plugin'
        }
        maven {
            url = 'https://maven.aliyun.com/repository/gradle-plugin'
        }
        maven {
            url = 'https://repo.spring.io/milestone'
        }
        gradlePluginPortal()
        mavenCentral()
    }
    dependencies {
        // import BOM
        implementation platform(group: 'org.springframework.boot', name: 'spring-boot-dependencies', version: "${springBootVersion}")
        implementation platform(group: 'io.github.openfeign', name: 'feign-bom', version: "${openfeignVersion}")
        implementation platform(group: 'com.squareup.okio', name: 'okio-bom', version: "${okioVersion}")
        implementation platform(group: 'org.camunda.bpm', name: 'camunda-only-bom', version: "${camundaVersion}")

        compileOnly('org.projectlombok:lombok')
        annotationProcessor('org.projectlombok:lombok')

        testCompileOnly('org.projectlombok:lombok')
        testAnnotationProcessor('org.projectlombok:lombok')

        compileOnly("org.springframework.boot:spring-boot-configuration-processor")
        annotationProcessor("org.springframework.boot:spring-boot-configuration-processor")
    }
    dependencyManagement {
        dependencies {
            dependency("cn.hutool:hutool-all:${hutoolVersion}")
            dependency("com.tencentcloudapi:tencentcloud-sdk-java-common:${tencentSdkVersion}")
            dependency("com.alibaba:fastjson:${fastjsonVersion}")
            dependency("org.apache.poi:poi-ooxml:${poiVersion}")
            dependency("org.bouncycastle:bcprov-jdk18on:${bcprovVersion}")
            dependency("org.bouncycastle:bcpkix-jdk18on:${bcpkixVersion}")
            dependency("com.google.guava:guava:${guavaVersion}")
            dependency("org.apache.httpcomponents:httpclient:${httpClientVersion}")
            dependency("commons-io:commons-io:${commonIoVersion}")
        }
    }
    tasks.withType(JavaCompile) {
        options.encoding = "UTF-8"

    }
    tasks.withType(Jar) {
        duplicatesStrategy = DuplicatesStrategy.EXCLUDE
    }


}

subprojects {

    dependencies {
        testRuntimeOnly('org.junit.platform:junit-platform-launcher')
    }
    if (it.name != 'boilerplate-server') {
        apply plugin: 'java-library'
    }
    tasks.named('test') {
        useJUnitPlatform()
    }
    test {
        exclude '**/*ManualTest.class'
    }
}

afterEvaluate {
    providers.exec {
        if (System.getProperty('os.name').toLowerCase(Locale.ROOT).contains('windows')) {
            commandLine 'cmd', '/c', 'git config core.hooksPath gradle/git-hooks'
        } else {
            commandLine 'bash', '-c', 'git config core.hooksPath gradle/git-hooks'
        }
    }.result.get()
}

bootJar {
    enabled = false
}

jar {
    enabled = false
}
