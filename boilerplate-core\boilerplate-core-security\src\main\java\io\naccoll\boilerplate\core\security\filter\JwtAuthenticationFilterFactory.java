package io.naccoll.boilerplate.core.security.filter;

import io.naccoll.boilerplate.core.security.properties.SecurityProperties;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.config.AutowireCapableBeanFactory;
import org.springframework.security.web.servlet.util.matcher.PathPatternRequestMatcher;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * JWT认证过滤器工厂类，用于创建和配置JWT认证过滤器实例
 *
 * <AUTHOR>
 */
@Component
public class JwtAuthenticationFilterFactory extends AbstractCustomFilterFactory<JwtAuthenticationFilter.Config> {

	@Resource
	private AutowireCapableBeanFactory beanFactory;

	@Resource
	private SecurityProperties securityProperties;

	protected JwtAuthenticationFilterFactory() {
		super(JwtAuthenticationFilter.Config.class);
	}

	@Override
	public AbstractCustomFilter apply(JwtAuthenticationFilter.Config stringObjectMap) {
		List<PathPatternRequestMatcher> matchers = securityProperties.getIgnoreTokenUrls()
			.stream()
			.map(pattern -> PathPatternRequestMatcher.withDefaults().matcher(pattern.getMethod(), pattern.getPath()))
			.collect(Collectors.toList());
		JwtAuthenticationFilter jwtAuthenticationFilter = new JwtAuthenticationFilter();
		jwtAuthenticationFilter.setExcludePatterns(matchers);
		beanFactory.autowireBean(jwtAuthenticationFilter);
		return jwtAuthenticationFilter;
	}

}
