package io.naccoll.boilerplate.audit.interfaces.eventlisten;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import io.naccoll.boilerplate.audit.config.AuditLogProperties;
import io.naccoll.boilerplate.core.audit.enums.AuditStorageType;
import io.naccoll.boilerplate.core.audit.operate.AuditOperationLogCommand;
import io.naccoll.boilerplate.core.audit.operate.OperateLogEvent;
import io.naccoll.boilerplate.core.ip.Ip2RegionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 审计操作事件SLF4J处理器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@EnableConfigurationProperties(AuditLogProperties.class)
public class AuditOperateEventSlf4jHandler {

	/**
	 * IP地址解析服务
	 */
	@Resource
	private Ip2RegionService ip2RegionService;

	/**
	 * 审计日志配置属性
	 */
	@Resource
	private AuditLogProperties auditLogProperties;

	/**
	 * 处理操作事件
	 * @param event 操作事件
	 */
	@EventListener
	@Async
	public void onOperate(OperateLogEvent event) {
		AuditLogProperties.Operation operation = auditLogProperties.getOperation();
		if (operation.isEnabled() && operation.getType().contains(AuditStorageType.SLF4J)) {
			AuditOperationLogCommand command = event.getPayload();
			String realm = command.getRealm();
			Long userId = command.getUserId();
			String username = command.getUsername();
			String description = command.getDescription();
			String type = command.getType();
			String clientIp = command.getClientIp();
			String createdDate = DateUtil.format(command.getCreatedDate(), DatePattern.ISO8601_PATTERN);
			String clientAddress = command.getClientAddress();
			String targetId = command.getTargetId();
			log.info(
					"REALM=[{}], UserId=[{}], UserName=[{}], Description=[{}],TargetId=[{}] Type=[{}], ClientIp=[{}], OperateDate=[{}], ClientAddress=[{}]",
					realm, userId, username, description, targetId, type, clientIp, createdDate, clientAddress);
		}
	}

}
