package io.naccoll.boilerplate.audit.interfaces.api.user;

import io.naccoll.boilerplate.audit.constant.AuditApiConstant;
import io.naccoll.boilerplate.audit.convert.AuditOperationLogConvert;
import io.naccoll.boilerplate.audit.dto.AuditOperationLogDto;
import io.naccoll.boilerplate.audit.dto.AuditOperationLogQueryCondition;
import io.naccoll.boilerplate.audit.dto.AuditOperationLogSimpleDto;
import io.naccoll.boilerplate.audit.model.AuditOperationLogPo;
import io.naccoll.boilerplate.audit.service.AuditOperationLogQueryService;
import io.naccoll.boilerplate.core.exception.ResourceNotFoundException;
import io.naccoll.boilerplate.core.security.authtication.entity.UserDetailsImpl;
import io.naccoll.boilerplate.core.security.authtication.session.SessionHelper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * 操作日志管理
 *
 * <AUTHOR>
 */
@RequestMapping(AuditApiConstant.UserApiV1.OPERATION)
@RestController
@Tag(name = "操作日志")
public class AuditUserApiV1OperationLogApi {

	/**
	 * 会话帮助工具
	 */
	@Resource
	private SessionHelper sessionHelper;

	/**
	 * 操作日志查询服务
	 */
	@Resource
	private AuditOperationLogQueryService auditLogQueryService;

	/**
	 * 操作日志转换器
	 */
	@Resource
	private AuditOperationLogConvert auditOperationLogConvert;

	/**
	 * 分页查询操作日志
	 * @param pageable 分页参数
	 * @return 操作日志分页结果
	 */
	@GetMapping("/page")
	public Page<AuditOperationLogSimpleDto> page(Pageable pageable) {
		AuditOperationLogQueryCondition condition = new AuditOperationLogQueryCondition();
		UserDetailsImpl userDetailsImpl = sessionHelper.getCurrentUser();
		condition.setUserId(sessionHelper.getUserId());
		condition.setRealm(userDetailsImpl.getRealm().toString());
		return auditOperationLogConvert
			.convertAuditOperationLogDtoPage(auditLogQueryService.queryPage(pageable, condition));
	}

	/**
	 * 根据ID查询操作日志详情
	 * @param id 操作日志ID
	 * @return 操作日志详情
	 */
	@Operation(summary = "查询操作日志")
	@GetMapping("/{id}")
	public AuditOperationLogDto page(@PathVariable Long id) {
		AuditOperationLogPo operationLog = auditLogQueryService.findByIdNotNull(id);
		if (!Objects.equals(operationLog.getUserId(), sessionHelper.getUserId())) {
			throw new ResourceNotFoundException("审计日志不存在");
		}
		return auditOperationLogConvert.convertAuditOperationLogDto(operationLog);
	}

}
