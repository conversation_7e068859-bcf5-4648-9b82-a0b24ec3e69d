package io.naccoll.boilerplate.audit.interfaces.eventlisten;

import io.naccoll.boilerplate.audit.config.AuditLogProperties;
import io.naccoll.boilerplate.core.audit.enums.AuditStorageType;
import io.naccoll.boilerplate.core.audit.feign.HttpRequestAuditCommand;
import io.naccoll.boilerplate.core.audit.feign.HttpRequestAuditEvent;
import io.naccoll.boilerplate.core.constant.AuditRequestConstant;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Objects;

/**
 * 审计HTTP请求事件SLF4J处理器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@EnableConfigurationProperties(AuditLogProperties.class)
public class AuditHttpRequestEventSlf4jHandler implements ApplicationListener<HttpRequestAuditEvent> {

	/**
	 * 审计日志配置属性
	 */
	@Resource
	private AuditLogProperties auditLogProperties;

	/**
	 * 处理HTTP请求审计事件
	 * @param event HTTP请求审计事件
	 */
	@Override
	@Async
	public void onApplicationEvent(HttpRequestAuditEvent event) {
		AuditLogProperties.Http http = auditLogProperties.getHttp();
		if (http.isEnabled() && http.getType().contains(AuditStorageType.SLF4J)) {
			HttpRequestAuditCommand httpRequestAudit = event.getPayload();
			String source = event.getSource().toString();
			if (Objects.equals(source, AuditRequestConstant.REQUEST)) {
				logRequest(httpRequestAudit);
			}

			if (Objects.equals(source, AuditRequestConstant.RESPONSE)) {
				logResponse(httpRequestAudit);
			}

			if (Objects.equals(source, AuditRequestConstant.IOEXCEPTION)) {
				logException(httpRequestAudit);
			}
		}
	}

	/**
	 * 记录HTTP请求日志
	 * @param httpRequest HTTP请求审计命令
	 */
	private void logRequest(HttpRequestAuditCommand httpRequest) {
		StringBuilder sb = new StringBuilder();
		sb.append("http request audit: ").append(httpRequest.getRequestId()).append("\n");
		sb.append(String.format("---> %s %s %s", httpRequest.getMethod(), httpRequest.getUrl(),
				httpRequest.getProtocolVersion()))
			.append("\n");
		for (String field : httpRequest.getRequestHeader().keySet()) {
			sb.append(String.format("%s: %s", field, httpRequest.getRequestHeader().get(field))).append("\n");
		}
		if (StringUtils.hasText(httpRequest.getRequestBody())) {
			sb.append("\n").append(httpRequest.getRequestBody());
		}
		sb.append(String.format("---> END HTTP (%s-byte body)", httpRequest.getRequestBodyLength()));
		log.info(httpRequest.getConfigKey() + ": " + sb);
	}

	/**
	 * 记录HTTP响应日志
	 * @param httpRequest HTTP请求审计命令
	 */
	protected void logResponse(HttpRequestAuditCommand httpRequest) {
		StringBuilder sb = new StringBuilder();
		sb.append("http response audit: ").append(httpRequest.getRequestId()).append("\n");
		sb.append(String.format("<--- %s %s%s (%sms)", httpRequest.getProtocolVersion(),
				httpRequest.getResponseStatus(), httpRequest.getResponseReason(), httpRequest.getElapsedTime()))
			.append("\n");
		for (String field : httpRequest.getResponseHeader().keySet()) {
			for (String value : httpRequest.getResponseHeader().get(field)) {
				sb.append(String.format("%s: %s", field, value)).append("\n");
			}
		}
		if (StringUtils.hasText(httpRequest.getResponseBody())) {
			sb.append("\n").append(httpRequest.getResponseBody()).append("\n");
		}
		sb.append(String.format("<--- END HTTP (%s-byte body)", httpRequest.getResponseBodyLength())).append("\n");
		log.info(httpRequest.getConfigKey() + ": " + sb);
	}

	/**
	 * 记录HTTP异常日志
	 * @param httpRequest HTTP请求审计命令
	 */
	protected void logException(HttpRequestAuditCommand httpRequest) {
		StringBuilder sb = new StringBuilder();
		sb.append(String.format("<--- ERROR %s (%sms)", httpRequest.getError(), httpRequest.getElapsedTime()))
			.append("\n");
		if (StringUtils.hasText(httpRequest.getErrorDetail())) {
			sb.append(httpRequest.getErrorDetail()).append("\n");
		}
		log.error(httpRequest.getConfigKey(), sb);
	}

}
