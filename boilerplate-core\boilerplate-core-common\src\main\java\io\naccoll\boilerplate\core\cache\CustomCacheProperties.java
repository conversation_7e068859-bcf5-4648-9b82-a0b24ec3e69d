package io.naccoll.boilerplate.core.cache;

import lombok.Data;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.convert.DurationUnit;
import org.springframework.util.ObjectUtils;

import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 自定义缓存配置属性类，用于管理缓存相关的配置信息。 该类通过@ConfigurationProperties注解，将配置文件中的custom.cache属性映射到类的字段中。
 *
 * <AUTHOR>
 */
@Data
@ConfigurationProperties("custom.cache")
public class CustomCacheProperties implements InitializingBean {

	/**
	 * 应用名称，用于生成缓存前缀，默认值为"demo"。 该值会被注入到prefix字段中，作为缓存前缀的一部分。
	 */
	@Value("${spring.application.name:demo}")
	private String applicationName;

	/**
	 * 默认锁过期时间，单位为毫秒，默认值为30秒。 该配置目前仅适用于Redis锁的有效期设置。
	 */
	private long defaultLockExpireTime = 30_000L;

	/**
	 * 缓存前缀字符串，用于标识不同应用或模块的缓存。 该前缀可以通过配置文件进行自定义设置。
	 */
	private String prefix = "";

	/**
	 * 是否启用统一前缀标识。 当设置为true时，prefix字段中的值会被附加到缓存键中。
	 */
	private boolean usePrefix = true;

	/**
	 * 是否缓存空值开关。 当设置为true时，允许缓存null值；false时，空值不会被缓存。
	 */
	private boolean cacheNullValues = true;

	/**
	 * 缓存默认过期时间，单位为毫秒，默认值为1小时。 该配置通过{@link DurationUnit}注解指定时间单位。
	 */
	@DurationUnit(ChronoUnit.MILLIS)
	private Duration cacheTtl = Duration.ofHours(1L);

	/**
	 * Spring Cache缓存前缀，默认值为"cache:"。 该前缀会被附加到缓存键中，用于标识缓存类型。
	 */
	private String cachePrefix = "cache:";

	/**
	 * 是否启用缓存前缀开关。 当设置为true时，cachePrefix字段中的值会被附加到缓存键中。
	 */
	private boolean useCachePrefix = true;

	private Map<String, CacheItem> items = new LinkedHashMap<>();

	public String getPrefix() {
		String result = "";
		if (usePrefix) {
			result += prefix;
		}
		return result;
	}

	public String getCompletePrefix() {
		String result = "";
		if (usePrefix) {
			result += prefix;
		}
		if (useCachePrefix) {
			result += cachePrefix;
		}
		return result;
	}

	@Override
	public void afterPropertiesSet() throws Exception {
		if (ObjectUtils.isEmpty(prefix)) {
			prefix = applicationName + ":";
		}
		if (useCachePrefix && ObjectUtils.isEmpty(cachePrefix)) {
			throw new IllegalArgumentException(
					"CustomCacheProperties cachePrefix must be not blank when useCachePrefix is true");
		}
		for (Map.Entry<String, CacheItem> stringCacheItemEntry : items.entrySet()) {
			CacheItem item = stringCacheItemEntry.getValue();
			String name = stringCacheItemEntry.getKey();
			if (item.isUsePrefix() && ObjectUtils.isEmpty(item.getPrefix())) {
				throw new IllegalArgumentException(String
					.format("CustomCacheProperties.item[%s] prefix must be not blank when usePrefix is true", name));
			}
		}
	}

	/**
	 * 缓存项配置类，用于定义每个缓存项的详细配置信息。 该类包含缓存前缀、过期时间、前缀使用策略等配置项。
	 *
	 * <AUTHOR>
	 */
	@Data
	public static class CacheItem {

		/**
		 * 缓存前缀字符串，用于标识特定缓存项的唯一性。 该前缀会与根前缀和父前缀组合使用，生成完整的缓存键前缀。
		 */
		private String prefix;

		/**
		 * 缓存默认过期时间，单位为毫秒。 该配置通过{@link DurationUnit}注解指定时间单位。
		 */
		@DurationUnit(ChronoUnit.MILLIS)
		private Duration cacheTtl;

		/**
		 * 是否启用当前缓存项的前缀标识。 当设置为true时，prefix字段中的值会被附加到缓存键中。
		 */
		private boolean usePrefix = false;

		/**
		 * 是否启用根前缀标识。 当设置为true时，根前缀（来自applicationName）会被附加到缓存键中。
		 */
		private boolean useRootPrefix = true;

		/**
		 * 是否启用父前缀标识。 当设置为true时，父前缀（来自cachePrefix）会被附加到缓存键中。
		 */
		private boolean useParentPrefix = true;

		/**
		 * 是否缓存空值开关。 当设置为true时，允许缓存null值；false时，空值不会被缓存。
		 */
		private boolean cacheNullValues = true;

		/**
		 * Gets complete prefix.
		 * @param properties the properties
		 * @return the complete prefix
		 */
		public String getCompletePrefix(CustomCacheProperties properties) {
			String result = "";
			if (useRootPrefix) {
				result += properties.getPrefix();
			}
			if (useParentPrefix) {
				result += properties.cachePrefix;
			}
			if (usePrefix) {
				result += prefix;
			}
			return result;
		}

	}

}
