package io.naccoll.boilerplate.audit.dao;

import io.naccoll.boilerplate.audit.dto.AuditLoginLogQueryCondition;
import io.naccoll.boilerplate.audit.model.AuditLoginLogPo;
import io.naccoll.boilerplate.core.persistence.dao.BaseDao;
import io.naccoll.boilerplate.core.persistence.jpa.specification.Specifications;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.util.StringUtils;

/**
 * 审计登录日志数据访问接口.
 *
 * <AUTHOR>
 */
public interface AuditLoginLogDao extends BaseDao<AuditLoginLogPo, Long> {

	/**
	 * 分页查询审计登录日志.
	 * @param pageable 分页参数
	 * @param condition 查询条件
	 * @return 分页查询结果
	 */
	default Page<AuditLoginLogPo> page(Pageable pageable, AuditLoginLogQueryCondition condition) {
		Specifications<AuditLoginLogPo> spec = Specifications.builder();
		spec.eq(condition.getUserId() != null, AuditLoginLogPo::getUserId, condition.getUserId())
			.eq(StringUtils.hasText(condition.getUsername()), AuditLoginLogPo::getUsername, condition.getUsername())
			.eq(StringUtils.hasText(condition.getRealm()), AuditLoginLogPo::getRealm, condition.getRealm());
		return findAll(spec, pageable);
	}

}
