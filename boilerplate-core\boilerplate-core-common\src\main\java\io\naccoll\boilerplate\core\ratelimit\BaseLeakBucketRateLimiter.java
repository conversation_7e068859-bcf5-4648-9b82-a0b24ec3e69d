package io.naccoll.boilerplate.core.ratelimit;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 漏桶限流器基类，实现了基于漏桶算法的限流功能 继承自BaseRateLimiter，提供漏桶限流器的核心实现
 *
 * <AUTHOR>
 */
public abstract class BaseLeakBucketRateLimiter extends BaseRateLimiter {

	/**
	 * 限流器初始化状态标记
	 */
	private final AtomicBoolean initialized = new AtomicBoolean(false);

	/**
	 * 漏桶限流器构造方法
	 * @param initialized 初始化状态
	 * @param defaultConfig 默认配置参数
	 */
	protected BaseLeakBucketRateLimiter(boolean initialized, RateLimiterProperties defaultConfig) {
		super(defaultConfig);
		this.initialized.compareAndSet(false, initialized);
	}

	@Override
	@SuppressWarnings("unchecked")
	public Response isAllowed(String id, RateLimiterProperties properties) {
		if (!this.initialized.get()) {
			throw new IllegalStateException("LeakBucketRateLimiter is not initialized");
		}
		RateLimiterProperties routeConfig = getRouteConfig(properties);

		// 每秒流失速率
		int leakRate = properties.getRate();
		// 漏桶容量
		int capacity = properties.getCapacity();
		// 每个请求消耗的水量
		int requestedTokens = 1;

		Response response = check(id, leakRate, capacity, requestedTokens, routeConfig);
		if (response != null) {
			return response;
		}
		return new Response(true, getHeaders(routeConfig, -1L));
	}

	/**
	 * 检查是否通过漏桶限流
	 * @param id 请求标识
	 * @param leakRate 每秒流失速率
	 * @param capacity 漏桶容量
	 * @param requestedTokens 请求需要的水量
	 * @param routeConfig 路由配置
	 * @return 限流检查结果
	 */
	protected abstract RateLimiter.Response check(String id, int leakRate, int capacity, int requestedTokens,
			RateLimiterProperties routeConfig);

	@Override
	public RateLimiterMode getMode() {
		return RateLimiterMode.LEAK_BUCKET;
	}

}