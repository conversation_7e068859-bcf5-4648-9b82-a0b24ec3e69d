package io.naccoll.boilerplate.audit.service;

import io.naccoll.boilerplate.audit.dto.AuditDataModelQueryCondition;
import io.naccoll.boilerplate.audit.model.AuditDataModelPo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 数据审计模型查询服务 提供数据审计模型相关的查询功能，包括分页查询、条件查询、ID查询等
 *
 * <AUTHOR>
 */
public interface AuditDataModelQueryService {

	/**
	 * 根据条件进行分页查询
	 * @param condition 查询条件
	 * @param pageable 分页参数
	 * @return 符合条件的分页数据
	 */
	Page<AuditDataModelPo> page(AuditDataModelQueryCondition condition, Pageable pageable);

	/**
	 * 根据条件查询所有数据
	 * @param condition 查询条件
	 * @return 符合条件的所有数据列表
	 */
	List<AuditDataModelPo> findAll(AuditDataModelQueryCondition condition);

	/**
	 * 根据ID查询单条数据
	 * @param id 数据ID
	 * @return 查询到的数据对象，如果不存在则返回null
	 */
	AuditDataModelPo findById(Long id);

	/**
	 * 根据模型签名查询数据
	 * @param modelSign 模型签名
	 * @return 查询到的数据对象
	 */
	AuditDataModelPo findByModelSign(String modelSign);

	/**
	 * 根据ID查询单条数据，若不存在则抛出异常
	 * @param id 数据ID
	 * @return 查询到的数据对象
	 * @throws IllegalArgumentException 如果未找到数据
	 */
	AuditDataModelPo findByIdNotNull(Long id);

	/**
	 * 根据ID集合查询数据列表
	 * @param ids 数据ID集合
	 * @return 数据ID与数据对象的映射列表
	 */
	List<AuditDataModelPo> findByIds(Collection<Long> ids);

	/**
	 * 根据ID集合查询数据映射
	 * @param ids 数据ID集合
	 * @return 数据ID与数据对象的映射关系
	 */
	Map<Long, AuditDataModelPo> findMapByIds(Collection<Long> ids);

}
