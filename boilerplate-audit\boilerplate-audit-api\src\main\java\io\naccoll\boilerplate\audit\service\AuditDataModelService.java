package io.naccoll.boilerplate.audit.service;

import io.naccoll.boilerplate.audit.dto.AuditDataModelCreateCommand;
import io.naccoll.boilerplate.audit.dto.AuditDataModelUpdateCommand;
import io.naccoll.boilerplate.audit.model.AuditDataModelPo;
import jakarta.validation.Valid;

/**
 * 数据审计模型服务接口，定义了数据审计模型的增删改操作
 *
 * 该接口提供了创建、更新和删除数据审计模型的核心功能
 *
 * <AUTHOR>
 */
public interface AuditDataModelService {

	/**
	 * 创建新的数据审计模型
	 *
	 * 该方法用于根据提供的创建命令创建新的数据审计模型记录
	 * @param command 包含创建数据审计模型所需参数的命令对象
	 * @return 创建成功的数据审计模型对象，包含系统生成的唯一标识和其他元数据
	 */
	AuditDataModelPo create(@Valid AuditDataModelCreateCommand command);

	/**
	 * 更新现有数据审计模型
	 *
	 * 该方法用于根据提供的更新命令更新现有数据审计模型记录
	 * @param command 包含更新数据审计模型所需参数的命令对象
	 * @return 更新后的数据审计模型对象，包含最新的状态和元数据
	 */
	AuditDataModelPo update(@Valid AuditDataModelUpdateCommand command);

	/**
	 * 根据ID删除数据审计模型
	 *
	 * 该方法用于根据提供的数据审计模型ID删除对应的记录
	 * @param id 数据审计模型的唯一标识
	 */
	void deleteById(Long id);

}
