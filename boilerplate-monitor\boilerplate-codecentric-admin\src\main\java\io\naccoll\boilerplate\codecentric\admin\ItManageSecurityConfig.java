package io.naccoll.boilerplate.codecentric.admin;

import de.codecentric.boot.admin.server.config.AdminServerProperties;
import io.naccoll.boilerplate.core.security.config.ApiSecurityFilterChainConfig;
import jakarta.annotation.Resource;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.security.SecurityProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.HeadersConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.provisioning.InMemoryUserDetailsManager;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.SavedRequestAwareAuthenticationSuccessHandler;
import org.springframework.security.web.csrf.CookieCsrfTokenRepository;
import org.springframework.security.web.servlet.util.matcher.PathPatternRequestMatcher;

import java.util.Collections;
import java.util.UUID;

/**
 * 管理后台安全配置类.
 *
 * <AUTHOR>
 */
@Configuration
@ConditionalOnProperty(value = "spring.boot.admin.server.enabled", matchIfMissing = true)
@AutoConfigureBefore(ApiSecurityFilterChainConfig.class)
public class ItManageSecurityConfig {

	@Resource
	private AdminServerProperties adminServerProperties;

	@Resource
	private SecurityProperties securityProperties;

	/**
	 * 管理后台安全过滤器链.
	 * @param http the http
	 * @return the security filter chain
	 * @throws Exception the exception
	 */
	@Bean
	public SecurityFilterChain itManageSecurityFilterChain(HttpSecurity http) throws Exception {
		InMemoryUserDetailsManager inMemoryUserDetailsManager = new InMemoryUserDetailsManager();
		User user = new User(securityProperties.getUser().getName(),
				"{noop}" + securityProperties.getUser().getPassword(),
				Collections.singletonList(new SimpleGrantedAuthority("ADMIN")));
		inMemoryUserDetailsManager.createUser(user);

		SavedRequestAwareAuthenticationSuccessHandler successHandler = new SavedRequestAwareAuthenticationSuccessHandler();
		successHandler.setTargetUrlParameter("redirectTo");
		successHandler.setDefaultTargetUrl(this.adminServerProperties.path("/"));
		// @formatter:off
		http
			.securityMatcher(this.adminServerProperties.path("/**"))
				.headers(headersConfigurer->headersConfigurer.frameOptions((HeadersConfigurer.FrameOptionsConfig::disable)))
			.authorizeHttpRequests(authorizeRequests -> authorizeRequests
				.requestMatchers(this.adminServerProperties.path("/assets/**")).permitAll()
				.requestMatchers(this.adminServerProperties.path("/actuator/info")).permitAll()
				.requestMatchers(this.adminServerProperties.path("/actuator/health")).permitAll()
				.requestMatchers(this.adminServerProperties.path("/login")).permitAll().anyRequest()
				.authenticated())
			.formLogin(formLogin -> formLogin.loginPage(this.adminServerProperties.path("/login"))
				.successHandler(successHandler))
			.logout(logout -> logout.logoutUrl(this.adminServerProperties.path("/logout")))
			.httpBasic(Customizer.withDefaults())
			.csrf(csrf -> csrf.csrfTokenRepository(CookieCsrfTokenRepository.withHttpOnlyFalse())
				.ignoringRequestMatchers(
					PathPatternRequestMatcher.withDefaults().matcher(HttpMethod.POST,this.adminServerProperties.path("/instances")),
					PathPatternRequestMatcher.withDefaults().matcher(HttpMethod.DELETE,this.adminServerProperties.path("/instances/*")),
					PathPatternRequestMatcher.withDefaults().matcher(this.adminServerProperties.path("/instances/*/actuator/**"))))
			.userDetailsService(inMemoryUserDetailsManager)
			.sessionManagement(configurer->configurer.sessionCreationPolicy(SessionCreationPolicy.IF_REQUIRED))
			.rememberMe(rememberMe -> {
				rememberMe.key(UUID.randomUUID().toString()).tokenValiditySeconds(1209600);
				rememberMe.userDetailsService(inMemoryUserDetailsManager);
			});
		// @formatter:on
		return http.build();
	}

}
