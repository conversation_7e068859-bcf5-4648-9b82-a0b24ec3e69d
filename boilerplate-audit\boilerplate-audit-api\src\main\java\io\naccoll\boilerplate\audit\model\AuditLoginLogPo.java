package io.naccoll.boilerplate.audit.model;

import io.naccoll.boilerplate.core.persistence.model.BasePersistableEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 登录日志实体类
 *
 * 该类用于记录用户登录相关信息，包括用户域、用户ID、用户名、登录时间、登录IP和登录地址。
 *
 * <AUTHOR>
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_audit_login_log")
public class AuditLoginLogPo extends BasePersistableEntity<Long> implements Serializable {

	@Serial
	private static final long serialVersionUID = 264373475342L;

	@Id
	@Schema()
	private Long id;

	/**
	 * 用户所属的域，用于区分不同用户来源或系统
	 */
	@Schema(description = "用户域")
	private String realm;

	/**
	 * 用户Id
	 */
	@Schema(description = "用户Id")
	private Long userId;

	/**
	 * 用户名
	 */
	@Schema(description = "用户名")
	private String username;

	/**
	 * 登录时间
	 */
	@Schema(description = "登录时间")
	private Date loginDate;

	/**
	 * 用户登录时所使用的IP地址
	 */
	@Schema(description = "登录IP")
	private String loginIp;

	/**
	 * 用户登录时的地理位置信息
	 */
	@Schema(description = "登录地址")
	private String loginAddress;

}
