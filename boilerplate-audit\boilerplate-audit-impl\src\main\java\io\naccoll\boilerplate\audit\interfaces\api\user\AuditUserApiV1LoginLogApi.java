package io.naccoll.boilerplate.audit.interfaces.api.user;

import io.naccoll.boilerplate.audit.constant.AuditApiConstant;
import io.naccoll.boilerplate.audit.dto.AuditLoginLogQueryCondition;
import io.naccoll.boilerplate.audit.model.AuditLoginLogPo;
import io.naccoll.boilerplate.audit.service.AuditLoginLogQueryService;
import io.naccoll.boilerplate.core.security.authtication.entity.UserDetailsImpl;
import io.naccoll.boilerplate.core.security.authtication.session.SessionHelper;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 登录日志管理
 *
 * <AUTHOR>
 */
@RequestMapping(AuditApiConstant.UserApiV1.LOGIN)
@RestController
@Tag(name = "登录日志")
public class AuditUserApiV1LoginLogApi {

	/**
	 * 会话帮助工具
	 */
	@Resource
	private SessionHelper sessionHelper;

	/**
	 * 登录日志查询服务
	 */
	@Resource
	private AuditLoginLogQueryService auditLoginLogQueryService;

	/**
	 * 分页查询登录日志
	 * @param pageable 分页参数
	 * @return 登录日志分页结果
	 */
	@GetMapping("/page")
	public Page<AuditLoginLogPo> page(Pageable pageable) {
		AuditLoginLogQueryCondition condition = new AuditLoginLogQueryCondition();
		UserDetailsImpl userDetailsImpl = sessionHelper.getCurrentUser();
		condition.setUserId(userDetailsImpl.getId());
		condition.setRealm(userDetailsImpl.getRealm().toString());
		return auditLoginLogQueryService.page(pageable, condition);
	}

}
