package io.naccoll.boilerplate.core.statemachine.builder;

/**
 * FailCallback
 *
 * @param <S> the type parameter
 * @param <E> the type parameter
 * @param <C> the type parameter
 * <AUTHOR>
 * @date 2022 /9/15 12:02 PM
 */
@FunctionalInterface
public interface FailCallback<S, E, C> {

	/**
	 * Callback function target execute if failed target trigger an Event
	 * @param sourceState the source state
	 * @param event the event
	 * @param context the context
	 */
	void onFail(S sourceState, E event, C context);

}
