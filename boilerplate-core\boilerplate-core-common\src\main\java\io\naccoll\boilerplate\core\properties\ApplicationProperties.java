package io.naccoll.boilerplate.core.properties;

import cn.hutool.core.util.RandomUtil;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 应用程序配置属性类，用于配置应用程序的基本属性，包括机器节点ID和主机地址。
 *
 * <AUTHOR>
 */
@ConfigurationProperties("custom.application")
@Getter
@Setter
public class ApplicationProperties implements InitializingBean {

	/**
	 * 机器节点的最大数量，固定为32个实例。
	 */
	public static final int LIMIT_WORKER = 32;

	/**
	 * 应用程序的主机地址，默认为本地开发地址。
	 */
	private String host = "http://127.0.0.1:31242";

	/**
	 * 机器的节点编号，范围为[0, 31]，由RandomUtil随机生成。
	 * <p>
	 * 该值受限于IdServiceLocalImpl的实现，最大支持32个实例。
	 */
	private int machineId = RandomUtil.randomInt(LIMIT_WORKER);

	@Override
	public void afterPropertiesSet() throws Exception {
		if (machineId >= LIMIT_WORKER) {
			throw new IllegalArgumentException("the machineId should be [0,31]");
		}
	}

}
