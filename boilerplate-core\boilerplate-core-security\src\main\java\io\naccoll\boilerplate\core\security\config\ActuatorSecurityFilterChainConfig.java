package io.naccoll.boilerplate.core.security.config;

import jakarta.annotation.Resource;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.security.SecurityProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.provisioning.InMemoryUserDetailsManager;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.csrf.CookieCsrfTokenRepository;

import java.util.Collections;

/**
 * actuator安全过滤器连，默认使用basic认证
 *
 * <AUTHOR>
 */
@AutoConfiguration
@AutoConfigureBefore(ApiSecurityFilterChainConfig.class)
public class ActuatorSecurityFilterChainConfig {

	/**
	 * 安全配置属性，用于获取安全相关的配置信息
	 */
	@Resource
	private SecurityProperties securityProperties;

	/**
	 * 配置actuator安全过滤器链
	 * <p>
	 * 1. 匹配所有/actuator/**路径 2. 禁用匿名访问 3. 所有请求都需要身份认证 4. 使用HTTP Basic认证 5.
	 * 配置CSRF保护，忽略actuator路径 6. 使用内存用户DetailsService
	 * @param http HttpSecurity配置对象
	 * @return 安全过滤器链
	 * @throws Exception 配置过程中可能抛出的异常
	 */
	@Bean
	public SecurityFilterChain actuatorSecurityFilterChain(HttpSecurity http) throws Exception {
		InMemoryUserDetailsManager inMemoryUserDetailsManager = new InMemoryUserDetailsManager();
		User user = new User(securityProperties.getUser().getName(),
				"{noop}" + securityProperties.getUser().getPassword(),
				Collections.singletonList(new SimpleGrantedAuthority("ENDPOINT_ADMIN")));
		inMemoryUserDetailsManager.createUser(user);

		// @formatter:off
		http.securityMatcher("/actuator/**")
			.anonymous(AbstractHttpConfigurer::disable)
			.authorizeHttpRequests(registry -> registry.requestMatchers("/**").fullyAuthenticated())
			.httpBasic(Customizer.withDefaults())
			.csrf(csrf -> {
				csrf.csrfTokenRepository(CookieCsrfTokenRepository.withHttpOnlyFalse());
				csrf.ignoringRequestMatchers("/actuator/**");
			})
			.userDetailsService(inMemoryUserDetailsManager);
		// @formatter:on
		return http.build();
	}

}
