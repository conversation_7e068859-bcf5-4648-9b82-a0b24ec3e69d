package io.naccoll.boilerplate.annex.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.ArrayList;
import java.util.List;

/**
 * 附件模块配置属性类
 *
 * <AUTHOR>
 */
@ConfigurationProperties(prefix = "annex")
@Getter
@Setter
public class AnnexProperties {

	/**
	 * 是否检查目标文件是否存在
	 */
	private boolean checkTargetExist = true;

	/**
	 * 白名单目标列表，附件操作时忽略检查
	 */
	private List<String> whiteTargets = new ArrayList<>();

}
