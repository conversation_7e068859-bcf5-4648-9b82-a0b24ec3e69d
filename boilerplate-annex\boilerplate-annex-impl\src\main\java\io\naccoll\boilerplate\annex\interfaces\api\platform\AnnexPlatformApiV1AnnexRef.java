package io.naccoll.boilerplate.annex.interfaces.api.platform;

import io.naccoll.boilerplate.annex.convert.OssAnnexRefConvert;
import io.naccoll.boilerplate.annex.dto.OssAnnexRefCreateCommand;
import io.naccoll.boilerplate.annex.dto.OssAnnexRefDto;
import io.naccoll.boilerplate.annex.dto.OssAnnexRefQueryCondition;
import io.naccoll.boilerplate.annex.model.OssAnnexRefPo;
import io.naccoll.boilerplate.annex.service.OssAnnexRefQueryService;
import io.naccoll.boilerplate.annex.service.OssAnnexRefService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;

/**
 * 通用附件引用管理
 *
 * <AUTHOR>
 */
@RestController
@Tag(name = "通用附件引用管理")
@RequestMapping("/annex/platform/api/v1/annex-ref")
public class AnnexPlatformApiV1AnnexRef {

	@Resource
	private OssAnnexRefService ossAnnexRefService;

	@Resource
	private OssAnnexRefQueryService ossAnnexRefQueryService;

	@Resource
	private OssAnnexRefConvert ossAnnexRefConvert;

	/**
	 * 分页查询通用附件引用
	 */
	@GetMapping("/page")
	@Operation(summary = "分页查询通用附件引用")
	@PreAuthorize("hasPermission(0L,'GLOBAL','oss/annex-ref:read')")
	public ResponseEntity<Page<OssAnnexRefDto>> page(OssAnnexRefQueryCondition condition, Pageable pageable) {
		Page<OssAnnexRefPo> ossAnnexRefPage = ossAnnexRefQueryService.page(condition, pageable);
		Page<OssAnnexRefDto> ossAnnexRefDtoPage = ossAnnexRefConvert.convertOssAnnexRefDtoPage(ossAnnexRefPage);
		return new ResponseEntity<>(ossAnnexRefDtoPage, HttpStatus.OK);
	}

	/**
	 * 查询通用附件引用
	 */
	@GetMapping("/{id}")
	@Operation(summary = "查询通用附件引用")
	@PreAuthorize("hasPermission(0L,'GLOBAL','oss/annex-ref:read')")
	public ResponseEntity<OssAnnexRefDto> queryOne(@PathVariable Long id) {
		OssAnnexRefPo ossAnnexRefPo = ossAnnexRefQueryService.findByIdNotNull(id);
		OssAnnexRefDto ossAnnexRefDto = ossAnnexRefConvert.convertOssAnnexRefDto(ossAnnexRefPo);
		return new ResponseEntity<>(ossAnnexRefDto, HttpStatus.OK);
	}

	/**
	 * 新增通用附件引用
	 */
	@PostMapping
	@Operation(summary = "保存通用附件引用")
	@PreAuthorize("hasPermission(0L,'GLOBAL','oss/annex-ref:add')")
	public ResponseEntity<OssAnnexRefDto> createOssAnnexRef(@RequestBody OssAnnexRefCreateCommand command) {
		OssAnnexRefPo ossAnnexRefPo = ossAnnexRefService.save(command);
		OssAnnexRefDto ossAnnexRefDto = ossAnnexRefConvert.convertOssAnnexRefDto(ossAnnexRefPo);
		return new ResponseEntity<>(ossAnnexRefDto, HttpStatus.CREATED);
	}

	/**
	 * 批量新增通用附件引用
	 */
	@PostMapping("/batch")
	@Operation(summary = "批量保存通用附件引用")
	@PreAuthorize("hasPermission(0L,'GLOBAL','oss/annex-ref:add')")
	@Transactional(rollbackFor = Exception.class)
	public ResponseEntity<Void> batchCreateOssAnnexRef(@RequestBody List<OssAnnexRefCreateCommand> commands) {
		for (OssAnnexRefCreateCommand command : commands) {
			ossAnnexRefService.save(command);
		}
		return new ResponseEntity<>(HttpStatus.CREATED);
	}

	/**
	 * 删除通用附件引用
	 */
	@DeleteMapping("/{id}")
	@Operation(summary = "删除通用附件引用")
	@PreAuthorize("hasPermission(0L,'GLOBAL','oss/annex-ref:del')")
	public ResponseEntity<Void> deleteOssAnnexRef(@PathVariable Long id) {
		ossAnnexRefService.deleteById(id);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	/**
	 * 批量删除通用附件引用
	 */
	@DeleteMapping("/batch")
	@Operation(summary = "批量删除通用附件引用")
	@PreAuthorize("hasPermission(0L,'GLOBAL','oss/annex-ref:del')")
	@Transactional(rollbackFor = Exception.class)
	public ResponseEntity<Void> batchDeleteOssAnnexRef(@RequestParam Collection<Long> ids) {
		for (Long id : ids) {
			ossAnnexRefService.deleteById(id);
		}
		return new ResponseEntity<>(HttpStatus.OK);
	}

}
