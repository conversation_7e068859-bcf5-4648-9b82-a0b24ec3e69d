package io.naccoll.boilerplate.audit.convert;

import io.naccoll.boilerplate.audit.dto.AuditOperationLogDto;
import io.naccoll.boilerplate.audit.dto.AuditOperationLogSimpleDto;
import io.naccoll.boilerplate.audit.model.AuditOperationLogPo;
import io.naccoll.boilerplate.core.utils.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 操作日志转换器 将操作日志的PO对象转换为DTO对象
 *
 * <AUTHOR>
 */
@Component
public class AuditOperationLogConvert {

	/**
	 * 将单个操作日志PO对象转换为DTO对象
	 * @param po 操作日志PO对象
	 * @return 操作日志DTO对象
	 */
	public AuditOperationLogDto convertAuditOperationLogDto(AuditOperationLogPo po) {
		return convertAuditOperationLogDtoList(List.of(po)).getFirst();
	}

	/**
	 * 将操作日志PO对象列表转换为DTO对象列表
	 * @param list 操作日志PO对象列表
	 * @return 操作日志DTO对象列表
	 */
	public List<AuditOperationLogDto> convertAuditOperationLogDtoList(List<AuditOperationLogPo> list) {
		return list.stream().map(po -> {
			AuditOperationLogDto dto = new AuditOperationLogDto();
			BeanUtils.copyProperties(po, dto);
			return dto;
		}).toList();
	}

	/**
	 * 将操作日志PO对象列表转换为简单DTO对象列表
	 * @param list 操作日志PO对象列表
	 * @return 操作日志简单DTO对象列表
	 */
	public List<AuditOperationLogSimpleDto> convertAuditOperationLogSimpleDtoList(List<AuditOperationLogPo> list) {
		return list.stream().map(po -> {
			AuditOperationLogSimpleDto dto = new AuditOperationLogSimpleDto();
			BeanUtils.copyProperties(po, dto);
			return dto;
		}).toList();
	}

	/**
	 * 将操作日志PO对象分页结果转换为DTO对象分页结果
	 * @param page 操作日志PO对象分页
	 * @return 操作日志DTO对象分页
	 */
	public Page<AuditOperationLogSimpleDto> convertAuditOperationLogDtoPage(Page<AuditOperationLogPo> page) {
		List<AuditOperationLogSimpleDto> auditOperationLogList = convertAuditOperationLogSimpleDtoList(
				page.getContent());
		return new PageImpl<>(auditOperationLogList, page.getPageable(), page.getTotalElements());
	}

}
