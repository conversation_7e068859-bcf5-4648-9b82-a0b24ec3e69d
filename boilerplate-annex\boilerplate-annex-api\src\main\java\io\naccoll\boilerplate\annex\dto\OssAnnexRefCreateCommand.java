package io.naccoll.boilerplate.annex.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 通用附件引用创建对象
 *
 * 该类用于创建通用附件引用，包含目标信息、附件分类、组织信息等字段。
 *
 * <AUTHOR>
 */
@Data
public class OssAnnexRefCreateCommand {

	/**
	 * 目标Id
	 */
	@Schema(description = "目标Id")
	@NotBlank
	private String targetId;

	/**
	 * 目标类型
	 */
	@Schema(description = "目标类型")
	@NotBlank
	private String targetType;

	/**
	 * 附件分类
	 */
	@Schema(description = "附件分类")
	@NotBlank
	private String annexGroup;

	/**
	 * 备注
	 */
	@Schema(description = "备注")
	private String remark;

	/**
	 * 组织id
	 */
	@Schema(description = "组织id")
	private Long organizationId;

	/**
	 * 部门id
	 */
	@Schema(description = "部门id")
	private Long departId;

	/**
	 * 附件id
	 */
	@Schema(description = "附件id")
	@NotNull
	private Long annexId;

	/**
	 * 附件名称
	 */
	@Schema(description = "附件名称")
	private String annexName;

	/**
	 * 扩展字段1
	 */
	@Schema(description = "扩展字段1")
	private String d1;

	/**
	 * 扩展字段2
	 */
	@Schema(description = "扩展字段2")
	private String d2;

	/**
	 * 扩展字段3
	 */
	@Schema(description = "扩展字段3")
	private String d3;

	/**
	 * 扩展字段4
	 */
	@Schema(description = "扩展字段4")
	private String d4;

	/**
	 * 扩展字段5
	 */
	@Schema(description = "扩展字段5")
	private String d5;

	/**
	 * 扩展字段6
	 */
	@Schema(description = "扩展字段6")
	private String d6;

	/**
	 * 扩展字段7
	 */
	@Schema(description = "扩展字段7")
	private String d7;

	/**
	 * 扩展字段8
	 */
	@Schema(description = "扩展字段8")
	private String d8;

	/**
	 * 扩展字段9
	 */
	@Schema(description = "扩展字段9")
	private String d9;

}
