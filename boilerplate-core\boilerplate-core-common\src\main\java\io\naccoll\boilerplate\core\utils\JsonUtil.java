package io.naccoll.boilerplate.core.utils;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.util.StdDateFormat;
import jakarta.json.*;
import lombok.SneakyThrows;

import java.io.*;
import java.net.URL;
import java.util.Arrays;
import java.util.Locale;
import java.util.Optional;
import java.util.TimeZone;

/**
 * JSON工具类，提供JSON与Java对象之间的转换功能
 *
 * <AUTHOR>
 */
public class JsonUtil {

	/** Jackson ObjectMapper实例 */
	private static final ObjectMapper objectMapper;

	private JsonUtil() {
	}

	static {
		objectMapper = createObjectMapper();
	}

	/**
	 * 将对象转换为JSON字符串
	 * @param obj 要转换的对象
	 * @return JSON字符串，如果输入为null则返回null
	 * @throws RuntimeException 如果转换失败
	 */
	@SneakyThrows
	public static String toString(Object obj) {
		if (obj == null) {
			return null;
		}
		if (obj instanceof CharSequence charSequence) {
			return charSequence.toString();
		}
		return objectMapper.writeValueAsString(obj);
	}

	/**
	 * 将对象转换为指定类型的JavaBean
	 * @param obj
	 * 输入对象(支持String/byte[]/InputStream/Reader/URL/File/DataInput/JsonParser等类型)
	 * @param clazz 目标类类型
	 * @return 转换后的JavaBean对象
	 * @param <T> 目标类型
	 * @throws RuntimeException 如果转换失败
	 */
	@SneakyThrows
	public static <T> T toBean(Object obj, Class<T> clazz) {
		if (obj instanceof String o) {
			return objectMapper.readValue(o, clazz);
		}
		if (obj instanceof byte[]o) {
			return objectMapper.readValue(o, clazz);
		}
		if (obj instanceof InputStream o) {
			return objectMapper.readValue(o, clazz);
		}
		if (obj instanceof Reader o) {
			return objectMapper.readValue(o, clazz);
		}
		if (obj instanceof URL o) {
			return objectMapper.readValue(o, clazz);
		}
		if (obj instanceof File o) {
			return objectMapper.readValue(o, clazz);
		}
		if (obj instanceof DataInput o) {
			return objectMapper.readValue(o, clazz);
		}
		if (obj instanceof JsonParser o) {
			return objectMapper.readValue(o, clazz);
		}
		// 不符合输入类型时尝试先序列化为字符串再反序列化为指定类型
		return objectMapper.readValue(toString(obj), clazz);
	}

	/**
	 * 将对象转换为指定泛型类型的JavaBean
	 * @param obj 输入对象(支持String/byte[]/InputStream/Reader/URL/File/JsonParser等类型)
	 * @param typeReference 目标类型引用
	 * @return 转换后的JavaBean对象
	 * @param <T> 目标类型
	 * @throws RuntimeException 如果转换失败
	 */
	@SneakyThrows
	public static <T> T toBean(Object obj, TypeReference<T> typeReference) {
		if (obj instanceof String o) {
			return objectMapper.readValue(o, typeReference);
		}
		if (obj instanceof byte[]o) {
			return objectMapper.readValue(o, typeReference);
		}
		if (obj instanceof InputStream o) {
			return objectMapper.readValue(o, typeReference);
		}
		if (obj instanceof Reader o) {
			return objectMapper.readValue(o, typeReference);
		}
		if (obj instanceof URL o) {
			return objectMapper.readValue(o, typeReference);
		}
		if (obj instanceof File o) {
			return objectMapper.readValue(o, typeReference);
		}
		if (obj instanceof JsonParser o) {
			return objectMapper.readValue(o, typeReference);
		}
		// 不符合输入类型时尝试先序列化为字符串再反序列化为指定类型
		return objectMapper.readValue(toString(obj), typeReference);
	}

	/**
	 * 将对象转换为指定JavaType类型的JavaBean
	 * @param obj
	 * 输入对象(支持String/byte[]/InputStream/Reader/URL/File/DataInput/JsonParser等类型)
	 * @param javaType 目标JavaType
	 * @return 转换后的JavaBean对象
	 * @param <T> 目标类型
	 * @throws RuntimeException 如果转换失败
	 */
	@SneakyThrows
	public static <T> T toBean(Object obj, JavaType javaType) {
		if (obj instanceof String o) {
			return objectMapper.readValue(o, javaType);
		}
		if (obj instanceof byte[]o) {
			return objectMapper.readValue(o, javaType);
		}
		if (obj instanceof InputStream o) {
			return objectMapper.readValue(o, javaType);
		}
		if (obj instanceof Reader o) {
			return objectMapper.readValue(o, javaType);
		}
		if (obj instanceof URL o) {
			return objectMapper.readValue(o, javaType);
		}
		if (obj instanceof File o) {
			return objectMapper.readValue(o, javaType);
		}
		if (obj instanceof DataInput o) {
			return objectMapper.readValue(o, javaType);
		}
		if (obj instanceof JsonParser o) {
			return objectMapper.readValue(o, javaType);
		}
		// 不符合输入类型时尝试先序列化为字符串再反序列化为指定类型
		return objectMapper.readValue(toString(obj), javaType);
	}

	/**
	 * 将对象转换为JSONObject(不推荐使用)
	 * <p>
	 * ⚠️尽量使用toBean将数据转为pojo
	 * </p>
	 * @param json 输入对象
	 * @return JSONObject对象
	 */
	@Deprecated
	public static JSONObject toJsonObject(Object json) {
		return JSONUtil.parseObj(json);
	}

	/**
	 * 将对象转换为JSONArray(不推荐使用)
	 * <p>
	 * ⚠️尽量使用toBean将数据转为pojo
	 * </p>
	 * @param json 输入对象
	 * @return JSONArray对象
	 */
	@Deprecated
	public static JSONArray toJsonArray(Object json) {
		return JSONUtil.parseArray(json);
	}

	/**
	 * 创建RFC6902标准的JSON差异补丁
	 * <p>
	 * 入参需要是代表数组或者对象的json字符串
	 * </p>
	 * @param source 源JSON字符串
	 * @param dest 目标JSON字符串
	 * @return 差异补丁的JSON字符串
	 */
	public static String createDiff(String source, String dest) {
		String defaultValue = "[]";
		JsonStructure beforeData = Optional.ofNullable(source).map(JsonUtil::fromString).orElse(null);
		JsonStructure afterData = Optional.ofNullable(dest).map(JsonUtil::fromString).orElse(null);
		if (beforeData == null && afterData == null) {
			return defaultValue;
		}
		JsonValue.ValueType type = beforeData == null ? afterData.getValueType() : beforeData.getValueType();
		if (!Arrays.asList(JsonValue.ValueType.ARRAY, JsonValue.ValueType.OBJECT).contains(type)) {
			return defaultValue;
		}
		beforeData = defensive(beforeData, type);
		afterData = defensive(afterData, type);
		JsonPatch jsonPatch = Json.createDiff(beforeData, afterData);
		return jsonPatch.toJsonArray().toString();
	}

	private static JsonStructure fromString(String source) {
		try (StringReader reader = new StringReader(source)) {
			try (JsonReader jsonReader = Json.createReader(reader)) {
				return jsonReader.read();
			}
		}
		catch (Exception e) {
			return null;
		}
	}

	private static JsonStructure defensive(JsonStructure jsonStructure, JsonValue.ValueType type) {
		if (jsonStructure == null) {
			return createEmpty(type);
		}
		return jsonStructure;
	}

	private static JsonStructure createEmpty(JsonValue.ValueType type) {
		return switch (type) {
			case ARRAY -> Json.createArrayBuilder().build();
			case OBJECT -> Json.createObjectBuilder().build();
			default -> null;
		};
	}

	/**
	 * 创建默认配置的ObjectMapper实例
	 * @return 新创建的ObjectMapper实例
	 */
	public static ObjectMapper createObjectMapper() {
		return createObjectMapper(null);
	}

	/**
	 * 创建或配置ObjectMapper实例
	 * @param mapper 要配置的ObjectMapper实例，如果为null则创建新实例
	 * @return 配置后的ObjectMapper实例
	 */
	public static ObjectMapper createObjectMapper(ObjectMapper mapper) {
		if (mapper == null) {
			mapper = new ObjectMapper();
		}
		var dateFormat = new StdDateFormat().withTimeZone(TimeZone.getDefault()).withLocale(Locale.getDefault());
		mapper.setDateFormat(dateFormat);
		mapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
		mapper.setTimeZone(TimeZone.getTimeZone("GMT+8"));
		mapper.setDefaultPropertyInclusion(JsonInclude.Include.NON_NULL);
		mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
		mapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
		return mapper;
	}

}
