package io.naccoll.boilerplate.core.statemachine.builder;

import io.naccoll.boilerplate.core.statemachine.Guard;

/**
 * On
 *
 * @param <S> the type parameter
 * @param <E> the type parameter
 * @param <C> the type parameter
 * <AUTHOR>
 * @date 2020 -02-07 6:14 PM
 */
public interface On<S, E, C> extends When<S, E, C> {

	/**
	 * Add condition for the transition
	 * @param guard transition condition
	 * @return When clause builder
	 */
	When<S, E, C> guard(Guard<C> guard);

}
