package io.naccoll.boilerplate.annex.model;

import io.naccoll.boilerplate.core.persistence.model.AbstractEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

import java.io.Serial;
import java.io.Serializable;

/**
 * 通用附件引用
 *
 * <AUTHOR>
 */
@SQLDelete(sql = "update t_oss_annex_ref set is_deleted = true where id = ?")
@SQLRestriction(value = "is_deleted = false")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_oss_annex_ref")
public class OssAnnexRefPo extends AbstractEntity<Long> implements Serializable {

	@Serial
	private static final long serialVersionUID = 1896087458594152448L;

	/**
	 * 主键ID
	 */
	@Id
	@Schema(description = "id")
	private Long id;

	/**
	 * 关联的目标ID
	 */
	@Schema(description = "目标Id")
	private String targetId;

	/**
	 * 关联的目标类型
	 */
	@Schema(description = "目标类型")
	private String targetType;

	/**
	 * 附件所属分类
	 */
	@Schema(description = "附件分类")
	private String annexGroup;

	/**
	 * 附件名称
	 */
	@Schema(description = "附件名称")
	private String annexName;

	/**
	 * 附件备注信息
	 */
	@Schema(description = "备注")
	private String remark;

	/**
	 * 所属组织ID
	 */
	@Schema(description = "组织id")
	private Long organizationId;

	/**
	 * 所属部门ID
	 */
	@Schema(description = "部门id")
	private Long departId;

	/**
	 * 附件唯一标识ID
	 */
	@Schema(description = "附件id")
	private Long annexId;

	/**
	 * 扩展字段1
	 */
	@Schema(description = "扩展字段1")
	private String d1;

	/**
	 * 扩展字段2
	 */
	@Schema(description = "扩展字段2")
	private String d2;

	/**
	 * 扩展字段3
	 */
	@Schema(description = "扩展字段3")
	private String d3;

	/**
	 * 扩展字段4
	 */
	@Schema(description = "扩展字段4")
	private String d4;

	/**
	 * 扩展字段5
	 */
	@Schema(description = "扩展字段5")
	private String d5;

	/**
	 * 扩展字段6
	 */
	@Schema(description = "扩展字段6")
	private String d6;

	/**
	 * 扩展字段7
	 */
	@Schema(description = "扩展字段7")
	private String d7;

	/**
	 * 扩展字段8
	 */
	@Schema(description = "扩展字段8")
	private String d8;

	/**
	 * 扩展字段9
	 */
	@Schema(description = "扩展字段9")
	private String d9;

	/**
	 * 附件访问URL（非持久化字段）
	 */
	@Transient
	@Schema(description = "附件路径")
	private String annexUrl;

	/**
	 * 附件MIME类型（非持久化字段）
	 */
	@Transient
	@Schema(description = "附件类型")
	private String contentType;

	/**
	 * 附件文件大小（非持久化字段）
	 */
	@Transient
	@Schema(description = "文件大小")
	private Long size;

	/**
	 * 附件文件扩展名（非持久化字段）
	 */
	@Transient
	@Schema(description = "附件拓展名")
	private String extName;

	/**
	 * 附件详细信息备注（非持久化字段）
	 */
	@Transient
	@Schema(description = "附件备注")
	private String annexRemark;

}
