package io.naccoll.boilerplate.core.security.annotations;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 安全操作注解
 * <p>
 * 用于标记需要进行安全操作的方法，确保方法调用时进行权限验证
 * </p>
 * <p>
 * 该注解适用于方法级别，运行时有效
 * </p>
 *
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface SecurityOperate {

}
