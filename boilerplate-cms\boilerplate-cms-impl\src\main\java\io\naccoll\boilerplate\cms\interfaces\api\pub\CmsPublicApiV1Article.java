package io.naccoll.boilerplate.cms.interfaces.api.pub;

import io.naccoll.boilerplate.cms.constant.CmsApiConstant;
import io.naccoll.boilerplate.cms.convert.CmsConvert;
import io.naccoll.boilerplate.cms.dto.CmsArticleDto;
import io.naccoll.boilerplate.cms.dto.CmsArticleQueryCondition;
import io.naccoll.boilerplate.cms.enums.CmsArticleStatus;
import io.naccoll.boilerplate.cms.model.CmsArticlePo;
import io.naccoll.boilerplate.cms.service.CmsArticleQueryService;
import io.naccoll.boilerplate.core.exception.ResourceNotFoundException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * 公共文章API接口
 *
 * <AUTHOR>
 */
@Tag(name = "文章API")
@RestController
@RequestMapping(CmsApiConstant.PublicApiV1.ARTICLE)
public class CmsPublicApiV1Article {

	@Resource
	private CmsArticleQueryService cmsArticleQueryService;

	@Resource
	private CmsConvert cmsConvert;

	/**
	 * 根据文章ID获取单篇文章信息
	 * @param articleId 文章ID
	 * @return 文章DTO
	 */
	@Operation(summary = "查询单篇文章")
	@GetMapping("/{articleId}")
	public CmsArticleDto getOneArticle(@PathVariable Long articleId) {
		CmsArticlePo cmsArticle = cmsArticleQueryService.findByIdNotNull(articleId);
		if (!Objects.equals(CmsArticleStatus.ENABLE.getId(), cmsArticle.getStatus())) {
			throw new ResourceNotFoundException("文章不存在");
		}
		return cmsConvert.convertCmsArticleDto(cmsArticleQueryService.findByIdNotNull(articleId));
	}

	/**
	 * 分页查询文章列表
	 * @param pageable 分页参数
	 * @param condition 查询条件
	 * @return 文章DTO分页结果
	 */
	@Operation(summary = "分页查询文章")
	@GetMapping("/page")
	public Page<CmsArticleDto> page(Pageable pageable, CmsArticleQueryCondition condition) {
		condition.setStatus(CmsArticleStatus.ENABLE.getId());
		return cmsConvert.convertCmsArticleDtoPage(cmsArticleQueryService.page(pageable, condition));
	}

}
