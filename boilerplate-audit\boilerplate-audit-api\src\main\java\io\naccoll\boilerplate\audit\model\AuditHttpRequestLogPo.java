package io.naccoll.boilerplate.audit.model;

import io.naccoll.boilerplate.core.persistence.model.BasePersistableEntity;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 审计HTTP请求日志实体类
 *
 * 该类用于记录HTTP请求的详细信息，包括请求的基本信息、请求和响应的详细内容、 处理时间以及错误信息等。
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "t_audit_http_request_log")
@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
public class AuditHttpRequestLogPo extends BasePersistableEntity<Long> {

	/**
	 * 主键ID
	 */
	@Id
	private Long id;

	/**
	 * HTTP协议版本，例如HTTP/1.1或HTTP/2.0
	 */
	private String protocolVersion;

	/**
	 * 请求方法，例如GET、POST、PUT、DELETE等
	 */
	private String method;

	/**
	 * 请求URL地址
	 */
	private String url;

	/**
	 * 日志级别，用于标识请求的处理优先级或严重程度
	 */
	private String logLevel;

	/**
	 * 请求类的全限定名，用于标识请求的来源或类型
	 */
	private String requestClass;

	/**
	 * 请求日期时间
	 */
	private Date requestDate;

	/**
	 * 请求头信息，包含请求的元数据
	 */
	private String requestHeader;

	/**
	 * 请求正文内容
	 */
	private String requestBody;

	/**
	 * 请求正文长度，单位为字节
	 */
	private int requestBodyLength;

	/**
	 * 响应原因短语，例如"OK"或"Not Found"
	 */
	private String responseReason;

	/**
	 * 响应状态码，例如200、404、500等
	 */
	private String responseStatus;

	/**
	 * 请求处理的总时间，单位为毫秒
	 */
	private long elapsedTime;

	/**
	 * 响应日期时间
	 */
	private Date responseDate;

	/**
	 * 响应头信息，包含响应的元数据
	 */
	private String responseHeader;

	/**
	 * 响应正文内容
	 */
	private String responseBody;

	/**
	 * 响应正文长度，单位为字节
	 */
	private int responseBodyLength;

	/**
	 * 错误信息，记录请求处理过程中出现的错误
	 */
	private String error = "";

	/**
	 * 错误详细信息，包含错误的详细描述和堆栈跟踪
	 */
	private String errorDetail = "";

}
