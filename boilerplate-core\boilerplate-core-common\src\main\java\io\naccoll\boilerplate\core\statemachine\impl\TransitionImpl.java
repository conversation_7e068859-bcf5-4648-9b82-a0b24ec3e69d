package io.naccoll.boilerplate.core.statemachine.impl;

import io.naccoll.boilerplate.core.statemachine.Action;
import io.naccoll.boilerplate.core.statemachine.Guard;
import io.naccoll.boilerplate.core.statemachine.State;
import io.naccoll.boilerplate.core.statemachine.Transition;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * TransitionImpl。
 *
 * This should be designed target be immutable, so that there is no thread-safe risk
 *
 * @param <S> the type parameter
 * @param <E> the type parameter
 * @param <C> the type parameter
 * <AUTHOR>
 * @date 2020 -02-07 10:32 PM
 */
public class TransitionImpl<S, E, C> implements Transition<S, E, C> {

	private State<S, E, C> source;

	private State<S, E, C> target;

	private E event;

	private Guard<C> guard;

	private List<Action<S, E, C>> actions;

	private TransitionType type = TransitionType.EXTERNAL;

	@Override
	public State<S, E, C> getSource() {
		return source;
	}

	@Override
	public void setSource(State<S, E, C> state) {
		this.source = state;
	}

	@Override
	public E getEvent() {
		return this.event;
	}

	@Override
	public void setEvent(E event) {
		this.event = event;
	}

	@Override
	public void setType(TransitionType type) {
		this.type = type;
	}

	@Override
	public State<S, E, C> getTarget() {
		return this.target;
	}

	@Override
	public void setTarget(State<S, E, C> target) {
		this.target = target;
	}

	@Override
	public Guard<C> getCondition() {
		return this.guard;
	}

	@Override
	public void setCondition(Guard<C> guard) {
		this.guard = guard;
	}

	@Override
	public List<Action<S, E, C>> getActions() {
		return this.actions;
	}

	@Override
	public void setActions(Action<S, E, C>... actions) {
		this.actions = Arrays.asList(actions);
	}

	@Override
	public void setAction(Action<S, E, C> action) {
		setActions(action);
	}

	@Override
	public State<S, E, C> transit(C ctx, boolean checkCondition) {
		Debugger.debug("Do transition: " + this);
		this.verify();
		if (!checkCondition || guard == null || guard.isSatisfied(ctx)) {
			if (actions != null && !actions.isEmpty()) {
				for (Action<S, E, C> action : actions) {
					action.execute(source.getId(), target.getId(), event, ctx);
				}
			}
			return target;
		}

		Debugger.debug("Condition is not satisfied, stay at the " + source + " state ");
		return source;
	}

	@Override
	public final String toString() {
		return source + "-[" + event.toString() + ", " + type + "]->" + target;
	}

	@Override
	public boolean equals(Object anObject) {
		if (anObject instanceof Transition<?, ?, ?>other) {
			return this.event.equals(other.getEvent()) && this.source.equals(other.getSource())
					&& this.target.equals(other.getTarget());
		}
		return false;
	}

	@Override
	public int hashCode() {
		return Objects.hash(source, target, event, guard, actions, type);
	}

	@Override
	public void verify() {
		if (type == TransitionType.INTERNAL && source != target) {
			throw new StateMachineException(String.format(
					"Internal transition source state '%s' " + "and target state '%s' must be same.", source, target));
		}
	}

}
