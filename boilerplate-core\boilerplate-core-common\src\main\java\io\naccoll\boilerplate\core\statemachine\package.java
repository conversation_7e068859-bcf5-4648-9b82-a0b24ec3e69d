/**
 * @formatter:off
 * 提供一个无状态的状态机？
 *
 * 虽然多数项目的状态管理可以用ifelse或者switch状态写成，但命令式的写法很难以用图像表示，不利于与实际业务对应，所以需要一套程序来控制
 * spring statemachine是一个完整的状态机方案，它可以进行复杂的嵌套状态管理以及并行等高级特性。但它与我做的多数项目并不契合
 * 一个是因为它太过于复杂，围绕着状态以及状态的流转将所有行为囊括，但多数业务更多是扁平的crud
 * 另一方面是因为它是一个完整的状态机，在分布式下持有状态会导致更多问题
 * 所以实际业务中更需要的是一个状态机用来表示业务流转，同时它能接收原始状态与事件，为了推导出一个新的状态
 * 我基于spring-statemachine-chart-exporter、COLA以及18年写过的一个支付平台的FSM形成当前的一个版本，可生成plantUML
 * @formatter:on
 */
package io.naccoll.boilerplate.core.statemachine;
