package io.naccoll.boilerplate.core.audit;

/**
 * 审计上下文管理接口，用于获取审计所需的用户信息 实现类应提供具体的用户信息获取方式，例如从安全上下文、HTTP请求头或数据库中获取
 * 该接口主要为审计系统提供用户身份信息，确保每次操作都能关联到具体用户
 *
 * 实现时需考虑以下因素： 1. 线程安全：确保在多线程环境下正确获取用户信息 2. 性能优化：避免在高并发场景下造成性能瓶颈 3.
 * 使用场景：适用于Web应用、微服务等需要用户身份校验的场景
 *
 * <AUTHOR>
 */
public interface AuditContextManager {

	/**
	 * 获取当前审计用户信息 返回的用户对象应包含身份认证所需的基本信息
	 *
	 * 调用方可以根据需要将返回的用户对象转换为具体业务模型
	 * @param <T> 审计用户类型，需继承AuditUser接口，具体实现由调用方决定
	 * @return 当前审计用户实例，保证不返回null，调用方无需进行空判断
	 */
	<T extends AuditUser> T getAuditUser();

}
