package io.naccoll.boilerplate.annex.config;

import io.naccoll.boilerplate.core.security.customizer.ApiSecurityConfigCustomizer;
import io.naccoll.boilerplate.core.security.properties.RequestPath;
import io.naccoll.boilerplate.core.security.properties.SecurityProperties;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.AuthorizeHttpRequestsConfigurer;

import java.util.Arrays;

/**
 * 通用附件安全配置
 *
 * <AUTHOR>
 */
@Configuration
public class AnnexSecurityConfig implements InitializingBean, ApiSecurityConfigCustomizer {

	@Resource
	private SecurityProperties securityProperties;

	/**
	 * 安全配置规则定制 配置附件相关接口的安全规则
	 */
	@Override
	public void customize(
			AuthorizeHttpRequestsConfigurer<HttpSecurity>.AuthorizationManagerRequestMatcherRegistry registry) {
		// @formatter:off
        registry
            .requestMatchers(getIgnoreToken()).permitAll()
            .requestMatchers("/annex/**").fullyAuthenticated();
        // @formatter:on
	}

	/**
	 * 获取忽略Token验证的路径
	 */
	private String[] getIgnoreToken() {
		return new String[] {};
	}

	/**
	 * 初始化完成后执行 注册忽略Token验证的URL路径
	 */
	@Override
	public void afterPropertiesSet() {
		for (String path : getIgnoreToken()) {
			for (HttpMethod method : Arrays.asList(HttpMethod.GET, HttpMethod.POST, HttpMethod.PUT, HttpMethod.DELETE,
					HttpMethod.PATCH)) {
				securityProperties.getIgnoreTokenUrls().add(new RequestPath(path, method));
			}
		}
	}

}
