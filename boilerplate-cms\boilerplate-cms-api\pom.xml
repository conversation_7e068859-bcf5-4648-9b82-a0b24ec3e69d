<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>boilerplate-cms</artifactId>
        <groupId>io.naccoll</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>boilerplate-cms-api</artifactId>

    <dependencies>
        <dependency>
            <groupId>io.naccoll</groupId>
            <artifactId>boilerplate-core-common</artifactId>
        </dependency>
        <dependency>
            <groupId>io.naccoll</groupId>
            <artifactId>boilerplate-core-web</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>io.naccoll</groupId>
            <artifactId>boilerplate-core-persistence</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>io.naccoll</groupId>
            <artifactId>boilerplate-core-security</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>
</project>
