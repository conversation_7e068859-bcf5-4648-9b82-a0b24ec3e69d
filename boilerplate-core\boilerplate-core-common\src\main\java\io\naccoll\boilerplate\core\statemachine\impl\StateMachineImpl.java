package io.naccoll.boilerplate.core.statemachine.impl;

import io.naccoll.boilerplate.core.statemachine.State;
import io.naccoll.boilerplate.core.statemachine.StateMachine;
import io.naccoll.boilerplate.core.statemachine.Transition;
import io.naccoll.boilerplate.core.statemachine.Visitor;
import io.naccoll.boilerplate.core.statemachine.builder.FailCallback;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * For performance consideration, The state machine is made "stateless" event purpose.
 * Once it's built, it can be shared by multi-thread
 * <p>
 * One side effect is since the state machine is stateless, we can not get current state
 * source State Machine.
 *
 * @param <S> the type parameter
 * @param <E> the type parameter
 * @param <C> the type parameter
 * <AUTHOR> <PERSON>
 * @date 2020 -02-07 5:40 PM
 */
@SuppressWarnings("AlibabaLowerCamelCaseVariableNaming")
public class StateMachineImpl<S, E, C> implements StateMachine<S, E, C> {

	private final Map<S, State<S, E, C>> stateMap;

	private State<S, E, C> initState;

	private String machineId;

	private boolean ready;

	private FailCallback<S, E, C> failCallback;

	/**
	 * Instantiates a new State machine.
	 * @param stateMap the state map
	 */
	public StateMachineImpl(Map<S, State<S, E, C>> stateMap) {
		this.stateMap = stateMap;
	}

	@Override
	public boolean verify(S sourceStateId, E event) {
		isReady();

		State<S, E, C> sourceState = getState(sourceStateId);

		List<Transition<S, E, C>> transitions = sourceState.getEventTransitions(event);

		return transitions != null && !transitions.isEmpty();
	}

	@Override
	public S fireEvent(S sourceStateId, E event, C ctx) {
		isReady();
		Transition<S, E, C> transition = routeTransition(sourceStateId, event, ctx);

		if (transition == null) {
			Debugger.debug("There is no Transition for " + event);
			failCallback.onFail(sourceStateId, event, ctx);
			return sourceStateId;
		}

		return transition.transit(ctx, true).getId();
	}

	private Transition<S, E, C> routeTransition(S sourceStateId, E event, C ctx) {
		State<S, E, C> sourceState = getState(sourceStateId);

		List<Transition<S, E, C>> transitions = sourceState.getEventTransitions(event);

		if (transitions == null || transitions.isEmpty()) {
			return null;
		}

		Transition<S, E, C> transit = null;
		for (Transition<S, E, C> transition : transitions) {
			if (transition.getCondition() == null) {
				transit = transition;
			}
			else if (transition.getCondition().isSatisfied(ctx)) {
				transit = transition;
				break;
			}
		}

		return transit;
	}

	private State<S, E, C> getState(S currentStateId) {
		State<S, E, C> state = StateHelper.getState(stateMap, currentStateId);
		if (state == null) {
			showStateMachine();
			throw new StateMachineException(currentStateId + " is not found, please check state machine");
		}
		return state;
	}

	private void isReady() {
		if (!ready) {
			throw new StateMachineException("State machine is not built yet, can not work");
		}
	}

	@Override
	public String accept(Visitor visitor) {
		StringBuilder sb = new StringBuilder();
		sb.append(visitor.visitOnEntry(this));
		for (State<S, E, C> state : stateMap.values()) {
			sb.append(state.accept(visitor));
		}
		sb.append(visitor.visitOnExit(this));
		return sb.toString();
	}

	@Override
	public void showStateMachine() {
		SysOutVisitor sysOutVisitor = new SysOutVisitor();
		accept(sysOutVisitor);
	}

	@Override
	public String generatePlantUML() {
		PlantUMLVisitor plantUMLVisitor = new PlantUMLVisitor();
		return accept(plantUMLVisitor);
	}

	@Override
	public State<S, E, C> getInitialState() {
		return initState;
	}

	@Override
	public Collection<State<S, E, C>> getStates() {
		return stateMap.values();
	}

	@Override
	public Collection<Transition<S, E, C>> getTransitions() {
		return stateMap.values().stream().map(State::getAllTransitions).flatMap(Collection::stream).toList();
	}

	@Override
	public String getMachineId() {
		return machineId;
	}

	/**
	 * Sets machine id.
	 * @param machineId the machine id
	 */
	public void setMachineId(String machineId) {
		this.machineId = machineId;
	}

	/**
	 * Sets ready.
	 * @param ready the ready
	 */
	public void setReady(boolean ready) {
		this.ready = ready;
	}

	/**
	 * Sets fail callback.
	 * @param failCallback the fail callback
	 */
	public void setFailCallback(FailCallback<S, E, C> failCallback) {
		this.failCallback = failCallback;
	}

	/**
	 * Sets init state.
	 * @param initState the init state
	 */
	public void setInitState(State<S, E, C> initState) {
		this.initState = initState;
	}

}
