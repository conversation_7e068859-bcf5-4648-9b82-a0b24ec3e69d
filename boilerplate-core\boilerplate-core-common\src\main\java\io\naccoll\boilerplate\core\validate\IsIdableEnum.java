package io.naccoll.boilerplate.core.validate;

import io.naccoll.boilerplate.core.enums.IdableEnum;
import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.*;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * 可标识枚举验证注解
 * <p>
 * 用于验证参数是否为指定枚举的有效标识值。该注解适用于需要进行枚举类型验证的场景， 确保输入的标识值在指定枚举类中存在。
 * </p>
 *
 * <AUTHOR>
 */
@Target({ METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER })
@Retention(RUNTIME)
@Documented
@Constraint(validatedBy = { IsIdableEnumValidator.class })
public @interface IsIdableEnum {

	/**
	 * 是否必须验证
	 * <p>
	 * 当返回true时，表示必须进行验证，不允许为空；返回false时，允许参数为空。
	 * </p>
	 * @return true表示必须验证，false表示允许为空
	 */
	boolean required() default false;

	/**
	 * 获取验证失败消息
	 * <p>
	 * 定义验证失败时的提示信息。默认使用"{error.enum.format}"作为提示消息， 开发者可以根据需要自定义消息内容。
	 * </p>
	 * @return 验证失败提示信息
	 */
	String message() default "{error.enum.format}";

	/**
	 * 获取验证组别
	 * <p>
	 * 定义该注解所属的验证组别，用于分组验证。默认情况下不指定任何组别。
	 * </p>
	 * @return 验证组别类数组
	 */
	Class<?>[] groups() default {};

	/**
	 * 获取负载信息
	 * <p>
	 * 定义与该注解相关的负载信息，用于扩展验证逻辑。默认情况下不指定任何负载。
	 * </p>
	 * @return 负载类数组
	 */
	Class<? extends Payload>[] payload() default {};

	/**
	 * 获取支持的枚举类
	 * <p>
	 * 指定需要进行验证的枚举类，该枚举类必须实现IdableEnum接口，确保 枚举实例具有标识值属性。
	 * </p>
	 * @return 实现IdableEnum接口的枚举类
	 */
	Class<? extends IdableEnum> support();

}
