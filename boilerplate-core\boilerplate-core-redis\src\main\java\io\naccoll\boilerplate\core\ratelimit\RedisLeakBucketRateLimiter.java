package io.naccoll.boilerplate.core.ratelimit;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.scripting.support.ResourceScriptSource;

import java.util.Arrays;
import java.util.List;

/**
 * 基于Redis的漏桶限流器实现，用于分布式系统中的限流控制
 *
 * <AUTHOR>
 */
@Slf4j
public class RedisLeakBucketRateLimiter extends BaseLeakBucketRateLimiter implements RateLimiter {

	private final StringRedisTemplate redisTemplate;

	private final RedisScript<List<Long>> script;

	public RedisLeakBucketRateLimiter(StringRedisTemplate redisTemplate, RateLimiterProperties defaultConfig) {
		super(true, defaultConfig);
		this.redisTemplate = redisTemplate;
		this.script = redisLeakBucketRateLimiterScript();
	}

	@Override
	protected Response check(String id, int leakRate, int capacity, int requestedTokens,
			RateLimiterProperties routeConfig) {
		try {
			List<String> keys = getKeys(id);
			// 替换 key 后缀
			String waterKey = keys.getFirst().replace(".tokens", ".water");
			String lastLeakTimeKey = keys.get(1).replace(".timestamp", ".last_leak_time");
			List<String> leakKeys = Arrays.asList(waterKey, lastLeakTimeKey);

			List<Long> results;
			try {
				results = this.redisTemplate.execute(this.script, leakKeys, String.valueOf(leakRate),
						String.valueOf(capacity), "", String.valueOf(requestedTokens));
			}
			catch (Exception e) {
				if (log.isDebugEnabled()) {
					log.debug("Error calling leak bucket rate limiter lua", e);
				}
				results = Arrays.asList(1L, -1L);
			}
			boolean allowed = results.getFirst() == 1L;
			Long waterLeft = results.get(1);

			Response response = new Response(allowed, getHeaders(routeConfig, waterLeft));

			if (log.isTraceEnabled()) {
				log.trace("response: " + response);
			}
			return response;
		}
		catch (Exception e) {
			log.error("Error determining if user allowed from redis (leak bucket)", e);
		}
		return null;
	}

	@SuppressWarnings("unchecked")
	public RedisScript redisLeakBucketRateLimiterScript() {
		DefaultRedisScript redisScript = new DefaultRedisScript<>();
		redisScript.setScriptSource(
				new ResourceScriptSource(new ClassPathResource("META-INF/scripts/leak_bucket_rate_limiter.lua")));
		redisScript.setResultType(List.class);
		return redisScript;
	}

}