# 项目概述

本项目是一个基于Spring Boot的企业级开发脚手架，集成了常用功能模块和最佳实践，帮助开发者快速构建企业级应用。

## 主要功能

- 系统管理模块
- 内容管理模块
- 支付模块（支持支付宝、微信支付）
- 对象存储模块（支持阿里云、腾讯云、MinIO、本地存储）
- 消息通知模块（邮件、短信）
- 应用监控
- 代码生成器
- 审计日志

## 环境要求

- Java 21
- PostgreSQL 17.4 (建议)
- Redis 7.4.3
- SpringBoot3.5.3

## 项目模块

├── boilerplate-annex
│   ├── boilerplate-annex-api
│   ├── boilerplate-annex-impl
├── boilerplate-audit
│   ├── boilerplate-audit-api
│   ├── boilerplate-audit-impl
├── boilerplate-cms
│   ├── boilerplate-cms-api
│   ├── boilerplate-cms-impl
├── boilerplate-core
│   ├── boilerplate-core-common
│   ├── boilerplate-core-integrate
│   ├── boilerplate-core-persistence
│   ├── boilerplate-core-redis
│   ├── boilerplate-core-security
│   ├── boilerplate-core-web
├── boilerplate-customer
│   ├── boilerplate-customer-all
│   ├── boilerplate-customer-api
│   ├── boilerplate-customer-wx-miniapp
│   ├── boilerplate-customer-wx-mp
├── boilerplate-generator
├── boilerplate-messenger
├── boilerplate-monitor
│   ├── boilerplate-codecentric-admin
│   ├── boilerplate-codecentric-client
│   ├── boilerplate-monitor-all
├── boilerplate-ocr
├── boilerplate-oss
│   ├── boilerplate-oss-aliyun
│   ├── boilerplate-oss-all
│   ├── boilerplate-oss-api
│   ├── boilerplate-oss-local
│   ├── boilerplate-oss-localfile
│   ├── boilerplate-oss-minio
│   ├── boilerplate-oss-tencent
├── boilerplate-payment
│   ├── boilerplate-payment-alipay
│   ├── boilerplate-payment-all
│   ├── boilerplate-payment-api
│   ├── boilerplate-payment-wechat
├── boilerplate-server
├── boilerplate-sys
│   ├── boilerplate-sys-api
│   ├── boilerplate-sys-impl
│   ├── boilerplate-sys-wx-miniapp
│   ├── boilerplate-sys-wx-mp
├── boilerplate-thirdparty
│   ├── boilerplate-thirdparty-api
│   ├── boilerplate-wx-miniapp
│   ├── boilerplate-wx-mp
├── boilerplate-workflow
│   ├── boilerplate-workflow-api
│   ├── boilerplate-workflow-impl

api模块一般存放实体，DTO跟Service接口层等可以给外部模块引用的部分
impl模块一般存放Controller，Service实现，Repository以及其他业务逻辑实现