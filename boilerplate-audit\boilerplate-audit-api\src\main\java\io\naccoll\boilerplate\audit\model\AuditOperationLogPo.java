package io.naccoll.boilerplate.audit.model;

import io.naccoll.boilerplate.core.persistence.model.BasePersistableEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 操作日志实体类
 *
 * <AUTHOR>
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_audit_operation_log")
public class AuditOperationLogPo extends BasePersistableEntity<Long> implements Serializable {

	@Serial
	private static final long serialVersionUID = 160037987052067213L;

	/**
	 * 日志ID
	 */
	@Id
	@Schema(description = "日志ID")
	private Long id;

	/**
	 * 操作的详细描述
	 */
	@Schema(description = "操作描述")
	private String description;

	/**
	 * 日志的类型，如操作类型
	 */
	@Schema(description = "日志类型")
	private String type;

	/**
	 * 客户端IP地址
	 */
	@Schema(description = "客户端IP")
	private String clientIp;

	/**
	 * 操作用户的ID
	 */
	@Schema(description = "用户ID")
	private Long userId;

	/**
	 * 操作用户的用户名
	 */
	@Schema(description = "用户名")
	private String username;

	/**
	 * 客户端的详细地址
	 */
	@Schema(description = "客户端地址")
	private String clientAddress;

	/**
	 * 用户所属的域
	 */
	@Schema(description = "用户域")
	private String realm;

	/**
	 * 操作前的数据对象
	 */
	@Schema(description = "修改前的数据对象")
	private String beforeData;

	/**
	 * 操作后的数据对象
	 */
	@Schema(description = "修改后的数据对象")
	private String afterData;

	/**
	 * 数据修改的差异信息
	 */
	@Schema(description = "DIFF数据")
	private String diffData;

	/**
	 * 操作发生的时间
	 */
	@Schema(description = "操作时间")
	private Date createdDate;

	/**
	 * 操作的目标ID
	 */
	@Schema(description = "目标Id")
	private String targetId;

	/**
	 * 操作用户的姓名
	 */
	@Schema(description = "用户姓名")
	private String name;

	/**
	 * 数据模型的唯一标识
	 */
	@Schema(description = "数据模型摘要")
	private String modelSign;

}
