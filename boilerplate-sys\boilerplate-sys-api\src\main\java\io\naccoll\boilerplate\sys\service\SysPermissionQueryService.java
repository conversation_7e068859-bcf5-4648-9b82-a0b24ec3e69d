package io.naccoll.boilerplate.sys.service;

import io.naccoll.boilerplate.sys.dto.SysPermissionQueryCondition;
import io.naccoll.boilerplate.sys.enums.PermissionLevel;
import io.naccoll.boilerplate.sys.model.SysPermissionPo;
import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 系统权限查询服务接口
 *
 * <AUTHOR>
 */
public interface SysPermissionQueryService {

	/**
	 * 查询所有权限列表
	 * @return 权限列表
	 */
	List<SysPermissionPo> findAll();

	/**
	 * 根据ID查询权限
	 * @param id 权限ID
	 * @return 权限信息
	 */
	SysPermissionPo findById(Long id);

	/**
	 * 根据ID集合批量查询权限
	 * @param ids 权限ID集合
	 * @return 权限列表
	 */
	List<SysPermissionPo> findByIds(Collection<Long> ids);

	/**
	 * 根据ID查询权限（确保ID不为空）
	 * @param id 权限ID
	 * @return 权限信息
	 */
	SysPermissionPo findByIdNotNull(Long id);

	/**
	 * 分页查询权限
	 * @param pageable 分页参数
	 * @param condition 查询条件
	 * @return 分页结果
	 */
	Page<SysPermissionPo> pageByCondition(Pageable pageable, @Valid SysPermissionQueryCondition condition);

	/**
	 * 根据角色ID查询权限
	 * @param roleId 角色ID
	 * @return 权限列表
	 */
	List<SysPermissionPo> findByRoleId(long roleId);

	/**
	 * 查询角色未分配的权限
	 * @param roleId 角色ID
	 * @return 权限列表
	 */
	List<SysPermissionPo> findAllByRoleNoAssign(long roleId);

	/**
	 * 根据用户ID查询权限
	 * @param userId 用户ID
	 * @param resourceType 资源类型
	 * @param resourceId 资源ID
	 * @return 权限列表
	 */
	List<SysPermissionPo> findByUserId(Long userId, PermissionLevel resourceType, Long resourceId);

	/**
	 * 通过标识查询唯一权限信息
	 * @param identity 权限标识
	 * @return 权限信息
	 */
	SysPermissionPo findByIdentity(String identity);

	/**
	 * 判断用户在特定层级是否有指定的权限
	 * @param userId 用户ID
	 * @param resourceType 资源类型
	 * @param resourceId 资源ID
	 * @param permit 权限标识
	 * @return 是否拥有权限
	 */
	boolean hasPermission(Long userId, PermissionLevel resourceType, Long resourceId, String permit);

	/**
	 * 判断用户在特定层级是否有任意指定的权限
	 * @param userId 用户ID
	 * @param resourceType 资源类型
	 * @param resourceId 资源ID
	 * @param permits 权限标识集合
	 * @return 是否拥有任意权限
	 */
	boolean hasAnyPermission(Long userId, PermissionLevel resourceType, Long resourceId, Collection<String> permits);

	List<SysPermissionPo> findByIdentities(Collection<String> oldCodes);

	default Map<String, SysPermissionPo> findMapByIdentities(Collection<String> oldCodes) {
		List<SysPermissionPo> permissions = findByIdentities(oldCodes);
		return permissions.stream()
			.collect(java.util.stream.Collectors.toMap(SysPermissionPo::getIdentity, p -> p, (p1, p2) -> p1));
	}

}
