package io.naccoll.boilerplate.core.ratelimit;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR> 令牌桶限流器基类，实现了基于令牌桶算法的限流功能 继承自BaseRateLimiter，提供令牌桶限流器的核心实现
 */
public abstract class BaseTokenBucketRateLimiter extends BaseRateLimiter {

	/**
	 * 限流器初始化状态标记
	 */
	private final AtomicBoolean initialized = new AtomicBoolean(false);

	/**
	 * 令牌桶限流器构造方法
	 * @param initialized 初始化状态
	 * @param defaultConfig 默认配置参数
	 */
	protected BaseTokenBucketRateLimiter(boolean initialized, RateLimiterProperties defaultConfig) {
		super(defaultConfig);
		this.initialized.compareAndSet(false, initialized);
	}

	@Override
	@SuppressWarnings("unchecked")
	public Response isAllowed(String id, RateLimiterProperties properties) {
		if (!this.initialized.get()) {
			throw new IllegalStateException("RedisRateLimiter is not initialized");
		}
		RateLimiterProperties routeConfig = getRouteConfig(properties);

		// 每秒允许的请求数
		int replenishRate = properties.getRate();

		// 允许的突发容量
		int burstCapacity = properties.getCapacity();

		// 每个请求消耗的令牌数
		int requestedTokens = 1;

		Response response = check(id, replenishRate, burstCapacity, requestedTokens, routeConfig);
		if (response != null) {
			return response;
		}
		return new Response(true, getHeaders(routeConfig, -1L));
	}

	/**
	 * 检查是否通过令牌桶限流
	 * @param id 请求标识
	 * @param replenishRate 每秒令牌生成速率
	 * @param burstCapacity 令牌桶容量
	 * @param requestedTokens 请求需要的令牌数
	 * @param routeConfig 路由配置
	 * @return 限流检查结果
	 */
	protected abstract RateLimiter.Response check(String id, int replenishRate, int burstCapacity, int requestedTokens,
			RateLimiterProperties routeConfig);

	@Override
	public RateLimiterMode getMode() {
		return RateLimiterMode.TOKEN_BUCKET;
	}

}
