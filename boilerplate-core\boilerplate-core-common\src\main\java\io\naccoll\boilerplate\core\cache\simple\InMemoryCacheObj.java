package io.naccoll.boilerplate.core.cache.simple;

import lombok.Getter;

import java.time.Duration;

/**
 * 内存缓存对象封装类
 * <p>
 * 用于存储缓存值及其过期时间信息
 * </p>
 *
 * <AUTHOR>
 */
class InMemoryCacheObj {

	/**
	 * 缓存值
	 */
	@Getter
	private final Object value;

	/**
	 * 缓存过期时间，表示为自纪元以来的毫秒数
	 */
	private final long expireTime;

	/**
	 * 构造内存缓存对象
	 * @param value 缓存值
	 * @param expireTime 缓存过期时间间隔
	 */
	public InMemoryCacheObj(Object value, Duration expireTime) {
		this.value = value;
		this.expireTime = System.currentTimeMillis() + expireTime.toMillis();
	}

	/**
	 * 获取剩余过期时间
	 * @return 剩余有效时间，可能为负值表示已过期
	 */
	public Duration getExpire() {
		return Duration.ofMillis(expireTime - System.currentTimeMillis());
	}

	/**
	 * 检查缓存是否已过期
	 * @return true表示已过期，false表示仍有效
	 */
	public boolean isExpired() {
		return System.currentTimeMillis() > expireTime;
	}

}
