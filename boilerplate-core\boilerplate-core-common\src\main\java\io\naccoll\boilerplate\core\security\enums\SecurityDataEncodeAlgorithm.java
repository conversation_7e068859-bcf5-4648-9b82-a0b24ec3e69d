package io.naccoll.boilerplate.core.security.enums;

/**
 * 安全数据编码算法枚举
 * <p>
 * 定义安全数据的加密算法类型，用于数据加密和解密操作
 * </p>
 *
 * <AUTHOR>
 */
public enum SecurityDataEncodeAlgorithm {

	/**
	 * 无加密
	 * <p>
	 * 用于不需要加密的场景，如测试环境或未启用加密功能的情况
	 * </p>
	 */
	NULL,

	/**
	 * SM4国密算法
	 * <p>
	 * 采用国家密码管理局认可的SM4分组密码算法，适用于需要符合国家密码标准的加密场景
	 * </p>
	 */
	SM4,

	/**
	 * AES加密算法
	 * <p>
	 * 使用高级加密标准（AES），一种广泛使用的对称加密算法，提供高安全性和性能
	 * </p>
	 */
	AES,

}
