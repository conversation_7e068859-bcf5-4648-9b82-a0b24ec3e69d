package io.naccoll.boilerplate.audit.constant;

/**
 * 审计API常量类，定义了与审计相关的API路径常量
 *
 * <AUTHOR>
 */
public class AuditApiConstant {

	/**
	 * 审计API的基础路径前缀
	 */
	public static final String AUDIT_PREFIX = "/audit";

	private AuditApiConstant() {
	}

	/**
	 * 平台相关的API路径常量
	 */
	public static class PlatformApiV1 {

		/**
		 * 平台API的基础路径
		 */
		public static final String PREFIX = AUDIT_PREFIX + "/platform/api/v1";

		/**
		 * 平台登录日志API路径
		 */
		public static final String LOGIN = PREFIX + "/login-log";

		/**
		 * 平台操作日志API路径
		 */
		public static final String OPERATION = PREFIX + "/operation-log";

		/**
		 * 平台状态机日志API路径
		 */
		public static final String STATE_MACHINE_LOG = PREFIX + "/state-machine-log";

		private PlatformApiV1() {
		}

	}

	/**
	 * 用户相关的API路径常量
	 */
	public static class UserApiV1 {

		/**
		 * 用户API的基础路径
		 */
		public static final String PREFIX = AUDIT_PREFIX + "/user/api/v1";

		/**
		 * 用户登录日志API路径
		 */
		public static final String LOGIN = PREFIX + "/login-log";

		/**
		 * 用户操作日志API路径
		 */
		public static final String OPERATION = PREFIX + "/operation-log";

		/**
		 * 用户状态机API路径
		 */
		public static final String STATE_MACHINE = PREFIX + "/state-machine";

		private UserApiV1() {
		}

	}

}
