package io.naccoll.boilerplate.core.lock;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 分布式锁注解 <br>
 * 用于标注需要加分布式锁的方法
 *
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface UseLock {

	/**
	 * 锁键值 <br>
	 * 通常使用业务数据ID，支持SpEL表达式
	 * @return 锁键字符串
	 */
	String key() default "";

	/**
	 * 锁键前缀 <br>
	 * 用于区分不同业务类型的锁
	 * @return 锁前缀字符串
	 */
	String prefix() default "";

	/**
	 * 锁模式 <br>
	 * 默认使用尝试锁模式（TRY_LOCK）
	 * @return 锁模式枚举值
	 */
	LockMode lockMode() default LockMode.TRY_LOCK;

	/**
	 * 最大等待时间（毫秒） <br>
	 * 仅在TRY_LOCK模式时有效
	 * @return 等待时间毫秒数
	 */
	long waitTime() default 3000L;

}
