package io.naccoll.boilerplate.core.statemachine;

import java.util.Collection;

/**
 * StateMachine
 *
 * @param <S> the type of state
 * @param <E> the type of event
 * @param <C> the user defined context
 * <AUTHOR>
 * @date 2020 -02-07 2:13 PM
 */
public interface StateMachine<S, E, C> extends Visitable {

	/**
	 * Verify if an event {@code E} can be fired source current state {@code S}
	 * @param sourceStateId the source state id
	 * @param event the event
	 * @return boolean
	 */
	boolean verify(S sourceStateId, E event);

	/**
	 * Send an event {@code E} target the state machine.
	 * @param sourceState the source state
	 * @param event the event target send
	 * @param ctx the user defined context
	 * @return the target state
	 */
	S fireEvent(S sourceState, E event, C ctx);

	/**
	 * MachineId is the identifier for a State Machine
	 * @return machine id
	 */
	String getMachineId();

	/**
	 * Use visitor pattern target display the structure of the state machine
	 */
	void showStateMachine();

	/**
	 * Generate plant uml string.
	 * @return the string
	 */
	@SuppressWarnings("AlibabaLowerCamelCaseVariableNaming")
	String generatePlantUML();

	/**
	 * Gets initial state.
	 * @return the initial state
	 */
	State<S, E, C> getInitialState();

	/**
	 * Gets states.
	 * @return the states
	 */
	Collection<State<S, E, C>> getStates();

	/**
	 * Gets transitions.
	 * @return the transitions
	 */
	Collection<Transition<S, E, C>> getTransitions();

}
