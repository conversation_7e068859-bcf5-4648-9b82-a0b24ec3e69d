package io.naccoll.boilerplate.core.cache.simple;

import com.esotericsoftware.kryo.Kryo;
import com.esotericsoftware.kryo.io.ByteBufferInput;
import com.esotericsoftware.kryo.io.ByteBufferOutput;
import io.naccoll.boilerplate.core.audit.PersistableObj;
import io.naccoll.boilerplate.core.utils.BeanUtils;
import org.springframework.scheduling.annotation.Scheduled;

import java.time.Duration;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 内存缓存写入器实现类
 * <p>
 * 负责内存缓存的存储管理、过期清理和序列化操作
 * </p>
 *
 * <AUTHOR>
 */
public class InMemoryCacheWriter {

	/**
	 * 内存缓存存储容器，使用ConcurrentHashMap保证线程安全
	 */
	private final ConcurrentHashMap<Object, InMemoryCacheObj> store;

	/**
	 * 存储是否序列化，序列化能避免写入内存缓存中的对象在外层被修改
	 */
	/**
	 * 是否启用序列化存储，true表示启用
	 */
	private final boolean serializable;

	private final ThreadLocal<Kryo> kryo = ThreadLocal.withInitial(() -> {
		Kryo kryoInit = new Kryo();
		kryoInit.setRegistrationRequired(false);
		return kryoInit;
	});

	/**
	 * 构造内存缓存写入器，默认启用序列化，初始容量1024
	 */
	public InMemoryCacheWriter() {
		this(true, 1024);
	}

	/**
	 * 初始化内存缓存写入器
	 * @param serializable 是否启用序列化存储
	 * @param initialCapacity 初始容量大小
	 */
	public InMemoryCacheWriter(boolean serializable, int initialCapacity) {
		this.serializable = serializable;
		this.store = new ConcurrentHashMap<>(initialCapacity);
	}

	/**
	 * 清理过期缓存 遍历存储容器，移除所有已过期的缓存对象
	 */
	public void clearCache() {
		for (Map.Entry<Object, InMemoryCacheObj> entry : store.entrySet()) {
			if (entry.getValue().isExpired()) {
				store.remove(entry.getKey(), entry.getValue());
			}
		}
	}

	/**
	 * 获取底层存储源
	 * @return 内存缓存存储对象
	 */
	ConcurrentHashMap<Object, InMemoryCacheObj> getSource() {
		return store;
	}

	/**
	 * 将对象存入内存缓存
	 * @param key 缓存键
	 * @param obj 存储对象
	 * @param ttl 时间生存期
	 */
	public void put(Object key, Object obj, Duration ttl) {
		store.put(key, new InMemoryCacheObj(toStoreValue(obj), ttl));
	}

	/**
	 * 如果缓存键不存在，则存入对象
	 * @param key 缓存键
	 * @param obj 存储对象
	 * @param ttl 时间生存期
	 * @return 是否成功存入
	 */
	public boolean putIfAbsent(Object key, Object obj, Duration ttl) {
		Object tmp = get(key);
		if (tmp != null) {
			return false;
		}
		store.putIfAbsent(key, new InMemoryCacheObj(toStoreValue(obj), ttl));
		return true;
	}

	/**
	 * 内部获取缓存对象
	 * @param key 缓存键
	 * @return 缓存对象，如果不存在或已过期则返回null
	 */
	Object getInner(Object key) {
		InMemoryCacheObj cacheObj = store.get(key);

		if (cacheObj != null && !cacheObj.isExpired()) {
			return cacheObj.getValue();
		}
		store.remove(key);
		return null;
	}

	/**
	 * 获取缓存对象
	 * @param key 缓存键
	 * @return 缓存对象，如果不存在或已过期则返回null
	 */
	public Object get(Object key) {
		Object res = getInner(key);
		if (res == null) {
			return null;
		}
		Object result = fromStoreValue(res);
		return result != null ? result : res;
	}

	/**
	 * 移除指定键的缓存
	 * @param key 缓存键
	 * @return 总是返回true
	 */
	public boolean remove(Object key) {
		store.remove(key);
		return true;
	}

	/**
	 * 更新缓存的过期时间
	 * @param key 缓存键
	 * @param ttl 新的生存期
	 * @return 是否成功更新
	 */
	public boolean expire(Object key, Duration ttl) {
		InMemoryCacheObj cacheObj = store.get(key);
		if (cacheObj == null) {
			return false;
		}
		store.put(key, new InMemoryCacheObj(cacheObj.getValue(), ttl));
		return true;
	}

	/**
	 * 获取缓存的过期时间
	 * @param key 缓存键
	 * @return 缓存的生存期，如果缓存不存在则返回null
	 */
	public Duration getExpire(Object key) {
		InMemoryCacheObj cacheObj = store.get(key);
		if (cacheObj == null) {
			return null;
		}
		return cacheObj.getExpire();
	}

	/**
	 * 增量递增缓存值
	 * @param key 缓存键
	 * @return 递增后的值，如果缓存不存在或无法转换则返回null
	 */
	public Long increment(Object key) {
		InMemoryCacheObj cacheObj = store.get(key);
		if (cacheObj == null || cacheObj.isExpired()) {
			return null;
		}
		try {
			Long num = Long.parseLong(cacheObj.getValue().toString()) + 1;
			store.put(key, new InMemoryCacheObj(num, cacheObj.getExpire()));
			return num;
		}
		catch (Exception e) {
			return null;
		}
	}

	/**
	 * 判断缓存是否为空
	 * @return 是否为空
	 */
	public boolean isEmpty() {
		return store.isEmpty();
	}

	/**
	 * 如果存在则移除缓存
	 * @param key 缓存键
	 * @return 是否存在并移除
	 */
	public boolean evictIfPresent(Object key) {
		return (store.remove(key) != null);
	}

	/**
	 * 如果缓存不存在，则计算并存入
	 * @param key 缓存键
	 * @param mappingFunction 计算函数
	 * @param ttl 时间生存期
	 * @return 缓存对象
	 */
	public InMemoryCacheObj computeIfAbsent(Object key, Function<? super Object, ?> mappingFunction, Duration ttl) {
		return store.computeIfAbsent(key, k -> new InMemoryCacheObj(mappingFunction.apply(k), ttl));
	}

	/**
	 * 获取匹配指定模式的缓存键集合
	 * @param pattern 匹配模式，支持*和?通配符
	 * @return 匹配的缓存键集合
	 */
	public Set<String> keys(String pattern) {
		pattern = pattern.replace("*", ".*");
		pattern = pattern.replace("?", ".");
		Pattern reg = Pattern.compile(pattern);
		return store.keySet()
			.stream()
			.filter(Objects::nonNull)
			.map(Object::toString)
			.filter(i -> reg.matcher(i).find())
			.collect(Collectors.toSet());
	}

	/**
	 * 将对象转换为存储格式
	 * @param obj 原始对象
	 * @return 存储格式对象
	 */
	public Object toStoreValue(Object obj) {
		if (serializable) {
			try (var output = new ByteBufferOutput(4096, 65535)) {
				kryo.get().writeClassAndObject(output, obj);
				return output.toBytes();
			}
			// return SerializeUtil.serialize(obj);
		}
		return BeanUtils.shallowCopy(obj);
	}

	/**
	 * 将存储格式对象转换为原始对象
	 * @param obj 存储格式对象
	 * @return 原始对象
	 */
	public Object fromStoreValue(Object obj) {
		Object result = obj;
		if (serializable) {
			try (var input = new ByteBufferInput((byte[]) obj)) {
				result = kryo.get().readClassAndObject(input);
			}
			// return SerializeUtil.deserialize((byte[]) obj);
		}
		else {
			result = BeanUtils.shallowCopy(obj);
		}
		if (result instanceof PersistableObj persistableObj) {
			persistableObj.setNew(false);
		}
		return result;
	}

	@Scheduled(cron = "* 0/1 * * * ?")
	public void cleanInMemoryCache() {
		clearCache();
	}

}
