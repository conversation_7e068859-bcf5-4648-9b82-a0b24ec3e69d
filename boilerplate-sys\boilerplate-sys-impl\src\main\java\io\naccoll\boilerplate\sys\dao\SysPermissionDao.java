package io.naccoll.boilerplate.sys.dao;

import io.naccoll.boilerplate.core.persistence.dao.BaseDao;
import io.naccoll.boilerplate.core.persistence.jpa.specification.Specifications;
import io.naccoll.boilerplate.sys.constant.SysCacheName;
import io.naccoll.boilerplate.sys.dto.SysPermissionQueryCondition;
import io.naccoll.boilerplate.sys.model.SysPermissionPo;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.util.StringUtils;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * The interface Sys permission dao.
 *
 * <AUTHOR>
 */
@SuppressWarnings("AlibabaAbstractMethodOrInterfaceMethodMustUseJavadoc")
public interface SysPermissionDao extends BaseDao<SysPermissionPo, Long> {

	@Override
	@Caching(evict = { @CacheEvict(value = SysCacheName.PERMISSION_ALL, allEntries = true) },
			put = { @CachePut(value = SysCacheName.PERMISSION_ID, key = "#result.id") })
	<S extends SysPermissionPo> S save(S entity);

	@Override
	@Caching(evict = { @CacheEvict(value = SysCacheName.PERMISSION_ALL, allEntries = true) },
			put = { @CachePut(value = SysCacheName.PERMISSION_ID, key = "#result.id") })
	<S extends SysPermissionPo> S saveAndFlush(S s);

	@Override
	@Caching(evict = { @CacheEvict(value = SysCacheName.PERMISSION_ALL, allEntries = true),
			@CacheEvict(value = SysCacheName.PERMISSION_ID, allEntries = true) })
	<S extends SysPermissionPo> List<S> saveAll(Iterable<S> iterable);

	@Override
	@Caching(evict = { @CacheEvict(value = SysCacheName.PERMISSION_ALL, allEntries = true),
			@CacheEvict(value = SysCacheName.PERMISSION_ID, allEntries = true) })
	<S extends SysPermissionPo> List<S> saveAllAndFlush(Iterable<S> entities);

	@Override
	@Caching(evict = { @CacheEvict(value = SysCacheName.PERMISSION_ALL, allEntries = true),
			@CacheEvict(value = SysCacheName.PERMISSION_ID, key = "#p0.id", condition = "#p0.id != null") })
	void delete(SysPermissionPo entity);

	@Override
	@Caching(evict = { @CacheEvict(value = SysCacheName.PERMISSION_ALL, allEntries = true),
			@CacheEvict(value = SysCacheName.PERMISSION_ID, allEntries = true) })
	void deleteAll();

	@Override
	@Caching(evict = { @CacheEvict(value = SysCacheName.PERMISSION_ALL, allEntries = true),
			@CacheEvict(value = SysCacheName.PERMISSION_ID, allEntries = true) })
	void deleteAllById(Iterable<? extends Long> longs);

	@Override
	@Caching(evict = { @CacheEvict(value = SysCacheName.PERMISSION_ALL, allEntries = true),
			@CacheEvict(value = SysCacheName.PERMISSION_ID, allEntries = true) })
	void deleteAllByIdInBatch(Iterable<Long> longs);

	@Override
	@Caching(evict = { @CacheEvict(value = SysCacheName.PERMISSION_ALL, allEntries = true),
			@CacheEvict(value = SysCacheName.PERMISSION_ID, allEntries = true) })
	void deleteAll(Iterable<? extends SysPermissionPo> entities);

	@Override
	@Caching(evict = { @CacheEvict(value = SysCacheName.PERMISSION_ALL, allEntries = true),
			@CacheEvict(value = SysCacheName.PERMISSION_ID, allEntries = true) })
	void deleteAllInBatch(Iterable<SysPermissionPo> entities);

	@Override
	@Caching(evict = { @CacheEvict(value = SysCacheName.PERMISSION_ALL, allEntries = true),
			@CacheEvict(value = SysCacheName.PERMISSION_ID, allEntries = true) })
	void deleteAllInBatch();

	@Override
	@Caching(evict = { @CacheEvict(value = SysCacheName.PERMISSION_ALL, allEntries = true),
			@CacheEvict(value = SysCacheName.PERMISSION_ID, key = "#p0") })
	void deleteById(Long aLong);

	@Override
	@Cacheable(value = SysCacheName.PERMISSION_ALL)
	List<SysPermissionPo> findAll();

	@Override
	@Cacheable(value = SysCacheName.PERMISSION_ID, key = "#p0")
	Optional<SysPermissionPo> findById(Long aLong);

	/**
	 * 根据标识查询权限
	 * @param identity 权限标识
	 * @return 权限信息
	 */
	SysPermissionPo findFirstByIdentity(String identity);

	/**
	 * 根据条件分页查询权限信息
	 * @param condition 查询条件
	 * @param pageable 分页参数
	 * @return 分页结果
	 */
	default Page<SysPermissionPo> pageByCondition(SysPermissionQueryCondition condition, Pageable pageable) {
		Specifications<SysPermissionPo> spec = Specifications.builder();
		if (StringUtils.hasText(condition.getKey())) {
			spec.and(and -> {
				and.or().contain(SysPermissionPo::getName, condition.getKey());
				and.or().contain(SysPermissionPo::getIdentity, condition.getKey());
			});
		}
		return this.findAll(spec, pageable);
	}

	/**
	 * 根据权限标识查询拥有该权限的所有用户ID
	 * @param identity 权限标识
	 * @return 用户ID集合
	 */
	@Query("""
			SELECT ur.userId
			FROM SysPermissionPo p
			INNER JOIN SysRolePermissionPo rp ON p.id = rp.permissionId
				INNER JOIN SysUserRolePo ur ON rp.roleId = ur.roleId
			WHERE p.identity = :identity
			""")
	Set<Long> findUserIdByPermissionIdentity(@Param("identity") String identity);

	List<SysPermissionPo> findByIdentityIn(Collection<String> identities);

}
