package io.naccoll.boilerplate.audit.aop;

import cn.hutool.crypto.SecureUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.naccoll.boilerplate.audit.dto.AuditDataModelCreateCommand;
import io.naccoll.boilerplate.audit.dto.event.DataModelCreateEvent;
import io.naccoll.boilerplate.core.audit.operate.AuditOperationLogCommand;
import io.naccoll.boilerplate.core.audit.operate.OperateLog;
import io.naccoll.boilerplate.core.audit.operate.OperateLogEvent;
import io.naccoll.boilerplate.core.exception.ServerException;
import io.naccoll.boilerplate.core.ip.Ip2RegionService;
import io.naccoll.boilerplate.core.ip.IpUtil;
import io.naccoll.boilerplate.core.security.authtication.entity.UserDetailsImpl;
import io.naccoll.boilerplate.core.security.authtication.session.SessionHelper;
import io.naccoll.boilerplate.core.springdoc.ModelUtils;
import io.naccoll.boilerplate.core.utils.JsonUtil;
import io.swagger.v3.core.converter.ResolvedSchema;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.expression.BeanFactoryResolver;
import org.springframework.core.ParameterNameDiscoverer;
import org.springframework.core.StandardReflectionParameterNameDiscoverer;
import org.springframework.core.task.TaskExecutor;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;

/**
 * 操作日志拦截器辅助类，用于处理操作日志的生成和存储
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class OperationLogAspectHelper {

	/**
	 * 类到签名的映射，用于存储数据模型的唯一标识
	 */
	private final Map<Class<?>, String> class2Sign = new ConcurrentHashMap<>();

	/**
	 * 需要忽略的类集合，用于避免重复处理
	 */
	private final Set<Class<?>> ignoreClasses = new HashSet<>();

	/**
	 * JSON对象映射器，用于对象与JSON字符串之间的转换
	 */
	private final ObjectMapper objectMapper;

	@Resource(name = "applicationTaskExecutor")
	private TaskExecutor taskExecutor;

	@Resource
	private BeanFactory beanFactory;

	@Resource
	private ApplicationEventPublisher applicationEventPublisher;

	@Resource
	private SessionHelper sessionHelper;

	@Resource
	private Ip2RegionService ip2RegionService;

	@Resource
	private HttpServletRequest request;

	public OperationLogAspectHelper() {
		objectMapper = JsonUtil.createObjectMapper();
		objectMapper.addMixIn(Object.class, AuditIgnoreFieldsMixin.class);
	}

	@Transactional(rollbackFor = Exception.class, propagation = Propagation.NOT_SUPPORTED)
	public Object getData(ProceedingJoinPoint joinPoint, Function<OperateLog, String> function, Object result) {
		OperateLog aopLog = getAnnotation(joinPoint);
		String dataExp = function.apply(aopLog);
		if (!StringUtils.hasText(dataExp)) {
			return null;
		}
		Object[] args = joinPoint.getArgs();
		MethodSignature signature = (MethodSignature) joinPoint.getSignature();
		Method method = signature.getMethod();
		ExpressionParser parser = new SpelExpressionParser();
		ParameterNameDiscoverer discoverer = new StandardReflectionParameterNameDiscoverer();
		String[] params = discoverer.getParameterNames(method);
		StandardEvaluationContext context = new StandardEvaluationContext();
		if (params != null) {
			for (int len = 0; len < params.length; len++) {
				context.setVariable("p0" + len, args[len]);
				context.setVariable(params[len], args[len]);
			}
		}
		if (result != null) {
			context.setVariable("result", result);
		}
		// 添加方法参数的简便访问
		context.setVariable("arguments", args);
		context.setVariable("method", method);
		context.setBeanResolver(new BeanFactoryResolver(beanFactory));
		Expression expression = parser.parseExpression(dataExp);
		try {
			return expression.getValue(context, Object.class);
		}
		catch (Exception e) {
			log.error("进行审计拦截变更数据时出现错误, 类名{}, 方法名为{}, 审计表达式为{}, 错误信息: {}\n", method.getDeclaringClass().getName(),
					method.getName(), dataExp, e.getMessage());
			return null;
		}
	}

	/**
	 * 存储操作日志
	 * @param annotation 操作日志注解
	 * @param id 目标ID
	 * @param beforeData 操作前数据
	 * @param afterData 操作后数据
	 */
	public void storeOperateLog(OperateLog annotation, String id, Object beforeData, Object afterData) {
		AuditOperationLogCommand command = getAuditOperationLogCommand(annotation);
		command.setTargetId(id);
		taskExecutor.execute(() -> {
			try {
				if (beforeData != null) {
					String beforeDataJson = objectMapper.writeValueAsString(beforeData);
					command.setBeforeData(beforeDataJson);
				}
				if (afterData != null) {
					String afterDataJson = objectMapper.writeValueAsString(afterData);
					command.setAfterData(afterDataJson);
				}
				if (beforeData != null || afterData != null) {
					Class<?> dataClass = getDataClass(beforeData, afterData);
					if (dataClass != null && !ignoreClasses.contains(dataClass)) {
						if (!class2Sign.containsKey(dataClass)) {
							ResolvedSchema schema = ModelUtils.resolveSchema(dataClass);
							if (schema != null) {
								String schemaJson = objectMapper.writeValueAsString(schema);
								String dataModelSign = SecureUtil.sha256(schemaJson);
								class2Sign.put(dataClass, dataModelSign);
								AuditDataModelCreateCommand dataModelCreateCommand = new AuditDataModelCreateCommand();
								dataModelCreateCommand.setDataSchema(schemaJson);
								dataModelCreateCommand.setClassName(dataClass.getName());
								dataModelCreateCommand.setModelSign(dataModelSign);
								applicationEventPublisher
									.publishEvent(new DataModelCreateEvent(this, dataModelCreateCommand));

								command.setModelSign(dataModelSign);
							}
							else {
								ignoreClasses.add(dataClass);
							}
						}
						else {
							command.setModelSign(class2Sign.get(dataClass));
						}
					}
				}

				applicationEventPublisher.publishEvent(new OperateLogEvent(command, command));
			}
			catch (JsonProcessingException e) {
				throw new ServerException(e);
			}
		});
	}

	/**
	 * 获取数据类类型
	 * @param beforeData 操作前数据
	 * @param afterData 操作后数据
	 * @return 数据类类型
	 */
	@Nullable
	private static Class<?> getDataClass(Object beforeData, Object afterData) {
		Class<?> dataClass = null;
		if (beforeData != null && afterData != null && Objects.equals(beforeData.getClass(), afterData.getClass())) {
			dataClass = beforeData.getClass();
		}
		if (beforeData != null && afterData == null) {
			dataClass = beforeData.getClass();
		}
		if (afterData != null && beforeData == null) {
			dataClass = afterData.getClass();
		}
		return dataClass;
	}

	/**
	 * 创建审计操作日志命令
	 * @param aopLog 操作日志注解
	 * @return 审计操作日志命令
	 */
	public AuditOperationLogCommand getAuditOperationLogCommand(OperateLog aopLog) {
		AuditOperationLogCommand command = new AuditOperationLogCommand();
		UserDetailsImpl loginUser = null;
		try {
			loginUser = sessionHelper.getCurrentUser();
		}
		catch (Exception ignored) {
		}
		String ip = IpUtil.getIpAddr(request);
		command.setClientAddress(ip2RegionService.getCityInfo(ip));
		command.setClientIp(ip);
		command.setDescription(aopLog.value());
		command.setCreatedDate(new Date());
		command.setType(aopLog.type());
		if (loginUser != null) {
			command.setRealm(loginUser.getRealm().name());
			command.setUserId(loginUser.getId());
			command.setUsername(loginUser.getUsername());
			command.setName(loginUser.getName());
		}
		else {
			command.setRealm(null);
			command.setUserId(null);
			command.setUsername(null);
			command.setName(null);
		}

		return command;
	}

	/**
	 * 获取操作日志注解
	 * @param joinPoint 切点
	 * @return 操作日志注解
	 */
	OperateLog getAnnotation(ProceedingJoinPoint joinPoint) {
		MethodSignature signature = (MethodSignature) joinPoint.getSignature();
		Method method = signature.getMethod();
		return method.getAnnotation(OperateLog.class);
	}

	/**
	 * 获取操作日志的唯一标识（从Aspect迁移）
	 */
	public String getId(ProceedingJoinPoint joinPoint, OperateLog operateLog, Object result) {
		String id = null;
		String idExp = operateLog.id();
		if (StringUtils.hasLength(idExp)) {
			Object[] args = joinPoint.getArgs();
			MethodSignature signature = (MethodSignature) joinPoint.getSignature();
			Method method = signature.getMethod();
			ExpressionParser parser = new SpelExpressionParser();
			ParameterNameDiscoverer discoverer = new StandardReflectionParameterNameDiscoverer();
			String[] params = discoverer.getParameterNames(method);
			StandardEvaluationContext context = new StandardEvaluationContext();
			if (params != null) {
				for (int len = 0; len < params.length; len++) {
					context.setVariable("p0" + len, args[len]);
					context.setVariable(params[len], args[len]);
				}
			}
			if (result != null) {
				context.setVariable("result", result);
			}
			Expression expression = parser.parseExpression(idExp);
			Object idValue = expression.getValue(context, Object.class);
			if (idValue != null) {
				id = idValue.toString();
			}
		}
		if (id == null && result != null) {
			try {
				java.lang.reflect.Field field = io.naccoll.boilerplate.core.persistence.dao.PersistenceHelper
					.getPrimaryKey(result.getClass());
				Object idObj = field.get(result);
				id = idObj.toString();
			}
			catch (Exception ignore) {
				// nothing
			}
		}
		return id;
	}

	/**
	 * 从数据对象中提取ID
	 * @param data 数据对象
	 * @param idExpression ID提取表达式
	 * @return 提取的ID，如果提取失败返回null
	 */
	public String extractIdFromData(Object data, String idExpression) {
		if (data == null || !StringUtils.hasText(idExpression)) {
			return null;
		}

		try {
			ExpressionParser parser = new SpelExpressionParser();
			StandardEvaluationContext context = new StandardEvaluationContext();
			context.setRootObject(data);
			context.setVariable("data", data);
			Expression expression = parser.parseExpression(idExpression);
			Object idValue = expression.getValue(context, Object.class);
			return idValue != null ? idValue.toString() : null;
		}
		catch (Exception e) {
			log.warn("从数据中提取ID失败，表达式: {}, 错误: {}", idExpression, e.getMessage());
			return null;
		}
	}

	/**
	 * 根据ID表达式匹配前后数据
	 * @param searchDataList 待匹配数据集合
	 * @param curData 数据项
	 * @param idExpression ID提取表达式
	 * @return 匹配的前置数据项，如果没有匹配则返回null
	 */
	public Object findMatchingBeforeData(Collection<?> searchDataList, Object curData, String idExpression) {
		if (searchDataList == null || curData == null || !StringUtils.hasText(idExpression)) {
			return null;
		}

		String id = extractIdFromData(curData, idExpression);
		if (id == null) {
			return null;
		}

		for (Object searchItem : searchDataList) {
			String searchId = extractIdFromData(searchItem, idExpression);
			if (id.equals(searchId)) {
				return searchItem;
			}
		}

		return null;
	}

	/**
	 * 处理包装返回结果，提取实际结果（从Aspect迁移）
	 */
	public Object getUnwrapResult(Object originResult) {
		Object result = originResult;
		if (result != null) {
			Class<?> resultClass = result.getClass();
			if (Arrays
				.asList(org.springframework.http.ResponseEntity.class,
						org.springframework.web.context.request.async.DeferredResult.class,
						java.util.concurrent.Callable.class, java.util.concurrent.Future.class)
				.stream()
				.anyMatch(i -> Objects.equals(resultClass, i))) {
				result = getResultFromWrapper(resultClass, result);
			}
		}
		return result;
	}

	/**
	 * 从响应包装中提取实际结果（从Aspect迁移）
	 */
	public static Object getResultFromWrapper(Class<?> resultClass, Object result) {
		try {
			if (Objects.equals(resultClass, org.springframework.http.ResponseEntity.class)) {
				result = ((org.springframework.http.ResponseEntity<?>) result).getBody();
			}
			else if (Objects.equals(resultClass, org.springframework.web.context.request.async.DeferredResult.class)) {
				result = ((org.springframework.web.context.request.async.DeferredResult<?>) result).getResult();
			}
			else if (Objects.equals(resultClass, java.util.concurrent.Callable.class)) {
				result = ((java.util.concurrent.Callable<?>) result).call();
			}
			else if (Objects.equals(resultClass, java.util.concurrent.Future.class)) {
				result = ((java.util.concurrent.Future<?>) result).get();
			}
			else {
				result = null;
			}
		}
		catch (InterruptedException e) {
			Thread.currentThread().interrupt();
			result = null;
		}
		catch (Exception e) {
			result = null;
		}
		return result;
	}

	@JsonIgnoreProperties(value = { "createdDate", "lastModifiedDate", "createdUserId", "lastModifiedUserId",
			"createdUserName", "lastModifiedUserName", "isDeleted", "password" })
	static class AuditIgnoreFieldsMixin {

	}

}
