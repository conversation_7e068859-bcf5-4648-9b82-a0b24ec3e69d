package io.naccoll.boilerplate.core.audit;

/**
 * 持久化对象接口定义 <br>
 * 用于标记实体对象的持久化状态，区分新建对象与已有持久化对象 <br>
 * 通常与数据库操作和缓存机制配合使用 <br>
 * 该接口通过{@link #isNew()}和{@link #setNew(boolean)}方法管理对象的持久化状态 <br>
 * 主要用于ORM框架和缓存机制的集成
 *
 * <AUTHOR>
 */
public interface PersistableObj {

	/**
	 * 判断对象是否为新建状态 <br>
	 * 新建状态的对象在持久化时应执行insert操作而非update <br>
	 * 该方法用于ORM框架和缓存机制的集成，判断对象是否需要插入新记录
	 * @return true表示新数据，尚未持久化；false表示已存在持久化记录
	 */
	boolean isNew();

	/**
	 * 设置对象的新建状态标记 <br>
	 * 主要供缓存反序列化时使用，数据库持久化操作应使用
	 * {@link io.naccoll.boilerplate.core.persistence.model.BasePersistableEntity#markNotNew(boolean)}
	 * <br>
	 * 该方法用于在反序列化过程中恢复对象的持久化状态
	 * @param isNew 是否为新数据，true表示新建状态，false表示已持久化
	 */
	void setNew(boolean isNew);

}
