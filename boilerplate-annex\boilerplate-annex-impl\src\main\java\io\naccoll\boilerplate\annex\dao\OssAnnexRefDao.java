package io.naccoll.boilerplate.annex.dao;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import io.naccoll.boilerplate.annex.dto.OssAnnexRefQueryCondition;
import io.naccoll.boilerplate.annex.model.OssAnnexRefPo;
import io.naccoll.boilerplate.core.persistence.dao.BaseDao;
import io.naccoll.boilerplate.core.persistence.jpa.specification.Specifications;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 通用附件引用数据库访问层
 *
 * <AUTHOR>
 */
@SuppressWarnings("AlibabaAbstractMethodOrInterfaceMethodMustUseJavadoc")
public interface OssAnnexRefDao extends BaseDao<OssAnnexRefPo, Long> {

	/**
	 * 根据目标ID、目标类型、附件ID和附件组查询第一个匹配的通用附件引用
	 * @param targetId 目标ID
	 * @param targetType 目标类型
	 * @param annexId 附件ID
	 * @param annexGroup 附件组
	 * @return 匹配的通用附件引用
	 */
	OssAnnexRefPo findFirstByTargetIdAndTargetTypeAndAnnexIdAndAnnexGroup(String targetId, String targetType,
			Long annexId, String annexGroup);

	/**
	 * 查询通用附件引用列表
	 * @param condition 通用附件引用查询条件
	 * @return 通用附件引用列表
	 */
	default List<OssAnnexRefPo> findAll(OssAnnexRefQueryCondition condition) {
		Specification<OssAnnexRefPo> spec = buildSpecification(condition);
		return findAll(spec);
	}

	/**
	 * 分页查询通用附件引用
	 * @param condition 通用附件引用查询条件
	 * @param pageable 分页参数
	 * @return 通用附件引用分页
	 */
	default Page<OssAnnexRefPo> page(OssAnnexRefQueryCondition condition, Pageable pageable) {
		Specification<OssAnnexRefPo> spec = buildSpecification(condition);
		return findAll(spec, pageable);
	}

	/**
	 * 构建通用附件引用查询条件的Specification
	 * @param condition 通用附件引用查询条件
	 * @return 查询条件的Specification
	 */
	default Specification<OssAnnexRefPo> buildSpecification(OssAnnexRefQueryCondition condition) {
		return Specifications.builder(OssAnnexRefPo.class)
			.in(CollUtil.isNotEmpty(condition.getAnnexId()), OssAnnexRefPo::getAnnexId, condition.getAnnexId())
			.in(CollUtil.isNotEmpty(condition.getTargetId()), OssAnnexRefPo::getTargetId, condition.getTargetId())
			.eq(CharSequenceUtil.isNotBlank(condition.getTargetType()), OssAnnexRefPo::getTargetType,
					condition.getTargetType())
			.like(CharSequenceUtil.isNotBlank(condition.getAnnexGroup()), OssAnnexRefPo::getAnnexGroup,
					condition.getAnnexGroup())
			.eq(condition.getDepartId() != null, OssAnnexRefPo::getDepartId, condition.getDepartId())
			.eq(condition.getOrganizationId() != null, OssAnnexRefPo::getOrganizationId, condition.getOrganizationId())
			.contain(StringUtils.hasText(condition.getAnnexName()), OssAnnexRefPo::getAnnexName,
					condition.getAnnexName())
			.contain(StringUtils.hasText(condition.getD1()), OssAnnexRefPo::getD1, condition.getD1())
			.contain(StringUtils.hasText(condition.getD2()), OssAnnexRefPo::getD2, condition.getD2())
			.contain(StringUtils.hasText(condition.getD3()), OssAnnexRefPo::getD3, condition.getD3())
			.contain(StringUtils.hasText(condition.getD4()), OssAnnexRefPo::getD4, condition.getD4())
			.contain(StringUtils.hasText(condition.getD5()), OssAnnexRefPo::getD5, condition.getD5())
			.contain(StringUtils.hasText(condition.getD6()), OssAnnexRefPo::getD6, condition.getD6())
			.contain(StringUtils.hasText(condition.getD7()), OssAnnexRefPo::getD7, condition.getD7())
			.contain(StringUtils.hasText(condition.getD8()), OssAnnexRefPo::getD8, condition.getD8())
			.contain(StringUtils.hasText(condition.getD9()), OssAnnexRefPo::getD9, condition.getD9());
	}

}
