package io.naccoll.boilerplate.core.validate;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 身份证号码验证注解
 *
 * 用于验证身份证号码格式是否正确
 *
 * <AUTHOR>
 */
@Target({ ElementType.FIELD })
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = IdCardValidator.class)
public @interface IdCard {

	/**
	 * 获取验证失败的消息
	 *
	 * 返回验证失败时的提示信息
	 * @return 验证失败提示信息
	 */
	String message() default "{error.idcard.format}";

	/**
	 * 获取验证组别
	 *
	 * 定义验证的组别，用于分组验证
	 * @return 验证组别类数组
	 */
	Class<?>[] groups() default {};

	/**
	 * 获取负载信息
	 *
	 * 定义验证的负载信息
	 * @return 负载类数组
	 */
	Class<? extends Payload>[] payload() default {};

}
