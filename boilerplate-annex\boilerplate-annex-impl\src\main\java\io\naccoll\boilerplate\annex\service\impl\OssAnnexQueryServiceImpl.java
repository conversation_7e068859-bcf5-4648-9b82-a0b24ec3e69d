package io.naccoll.boilerplate.annex.service.impl;

import io.naccoll.boilerplate.annex.dao.OssAnnexDao;
import io.naccoll.boilerplate.annex.dto.OssAnnexQueryCondition;
import io.naccoll.boilerplate.annex.model.OssAnnexPo;
import io.naccoll.boilerplate.annex.service.OssAnnexQueryService;
import io.naccoll.boilerplate.core.exception.ResourceNotFoundException;
import io.naccoll.boilerplate.core.persistence.dao.DaoUtils;
import io.naccoll.boilerplate.oss.config.OssType;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 通用附件查询服务实现
 *
 * <AUTHOR>
 */
@Service
public class OssAnnexQueryServiceImpl implements OssAnnexQueryService {

	@Resource
	private OssAnnexDao ossAnnexDao;

	@Override
	public Page<OssAnnexPo> page(OssAnnexQueryCondition condition, Pageable pageable) {
		return ossAnnexDao.page(condition, pageable);
	}

	@Override
	public List<OssAnnexPo> findAll(OssAnnexQueryCondition condition) {
		return ossAnnexDao.findAll(condition);
	}

	@Override
	public OssAnnexPo findById(Long id) {
		return ossAnnexDao.findById(id).orElse(null);
	}

	@Override
	public OssAnnexPo findByIdNotNull(Long id) {
		return ossAnnexDao.findById(id).orElseThrow(() -> new ResourceNotFoundException("通用附件不存在"));
	}

	@Override
	public List<OssAnnexPo> findByIds(Collection<Long> ids) {
		return DaoUtils.findByPrimaryKeyIn(ossAnnexDao, ids);
	}

	@Override
	public Map<Long, OssAnnexPo> findMapByIds(Collection<Long> ids) {
		return DaoUtils.findMapByPrimaryKeyIn(ossAnnexDao, ids);
	}

	@Override
	public OssAnnexPo findNoRepeat(String annexSign, OssType ossStore, String bucketName) {
		return ossAnnexDao.findFirstByAnnexSignAndOssStoreAndBucketName(annexSign, ossStore.toString(), bucketName);
	}

}
