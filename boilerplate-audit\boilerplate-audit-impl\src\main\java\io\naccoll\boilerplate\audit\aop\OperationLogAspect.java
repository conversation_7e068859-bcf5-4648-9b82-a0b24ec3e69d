package io.naccoll.boilerplate.audit.aop;

import cn.hutool.core.collection.CollectionUtil;
import io.naccoll.boilerplate.audit.config.AuditLogProperties;
import io.naccoll.boilerplate.core.audit.operate.OperateLog;
import io.naccoll.boilerplate.core.persistence.utils.TransactionUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 操作日志切面类，用于拦截带有 {@link OperateLog} 注解的方法，记录操作日志。
 *
 * <AUTHOR>
 */
@Component
@Aspect
@Slf4j
@Order
@ConditionalOnProperty(value = "custom.security.audit.operation.enabled", matchIfMissing = true)
public class OperationLogAspect {

	@Resource
	private AuditLogProperties auditLogProperties;

	@Resource
	private OperationLogAspectHelper operationLogAspectHelper;

	/**
	 * 定义切点，拦截带有 {@link OperateLog} 注解的方法。
	 */
	@Pointcut("@annotation(io.naccoll.boilerplate.core.audit.operate.OperateLog)")
	public void logPointcut() {
	}

	/**
	 * 环绕通知方法，执行切点拦截后的逻辑，记录操作日志。
	 * @param joinPoint 切点
	 * @return 拦截方法的执行结果
	 * @throws Throwable 可能抛出的异常
	 */
	@Around("logPointcut()")
	public Object logAround(ProceedingJoinPoint joinPoint) throws Throwable {
		OperateLog annotation = operationLogAspectHelper.getAnnotation(joinPoint);

		// 判断是否为批量模式
		if (annotation.batch()) {
			return handleBatchAudit(joinPoint, annotation);
		}
		else {
			return handleSingleAudit(joinPoint, annotation);
		}
	}

	/**
	 * 处理单个记录审计
	 */
	private Object handleSingleAudit(ProceedingJoinPoint joinPoint, OperateLog annotation) throws Throwable {
		// 同步获取前置数据和上下文信息，避免事务和线程上下文问题
		Object beforeData = getBeforeData(joinPoint);

		// 执行目标方法
		Object result = joinPoint.proceed();
		Object unwrapResult = operationLogAspectHelper.getUnwrapResult(result);
		String id = operationLogAspectHelper.getId(joinPoint, annotation, unwrapResult);

		TransactionUtil.completeTransaction(() -> {
			Object afterData = getAfterData(joinPoint, unwrapResult);
			operationLogAspectHelper.storeOperateLog(annotation, id, beforeData, afterData);
		});
		return result;
	}

	/**
	 * 处理批量审计
	 */
	private Object handleBatchAudit(ProceedingJoinPoint joinPoint, OperateLog annotation) throws Throwable {
		// 获取批量前置数据（如果需要）
		Object batchBeforeData = getBeforeData(joinPoint);

		// 执行目标方法
		Object result = joinPoint.proceed();
		Object unwrapResult = operationLogAspectHelper.getUnwrapResult(result);

		TransactionUtil.completeTransaction(() -> {
			Object batchAfterData = getAfterData(joinPoint, unwrapResult);
			processBatchData(annotation, batchBeforeData, batchAfterData);
		});

		return result;
	}

	/**
	 * 处理批量数据
	 */
	private void processBatchData(OperateLog annotation, Object batchBeforeData, Object batchAfterData) {
		// 确定要处理的数据集合
		Collection<?> dataCollection = determineBatchDataCollection(batchBeforeData, batchAfterData);

		if (CollectionUtil.isEmpty(dataCollection)) {
			log.debug("批量审计数据为空，不进行记录");
			return;
		}

		String idExpression = annotation.id();
		Collection<?> beforeCollection = batchBeforeData instanceof Collection ? (Collection<?>) batchBeforeData
				: Collections.emptyList();
		Collection<?> afterCollection = batchAfterData instanceof Collection ? (Collection<?>) batchAfterData
				: Collections.emptyList();

		Set<String> processedIds = new HashSet<>();

		// 处理新增的数据（after中有但before中没有）
		for (Object afterItem : afterCollection) {
			try {
				String id = operationLogAspectHelper.extractIdFromData(afterItem, idExpression);
				if (id == null) {
					log.warn("无法从后置数据中提取ID: {}", afterItem);
					continue;
				}

				Object beforeData = operationLogAspectHelper.findMatchingBeforeData(beforeCollection, afterItem,
						idExpression);
				if (beforeData == null) {
					// 新增的数据项
					operationLogAspectHelper.storeOperateLog(annotation, id, null, afterItem);
					processedIds.add(id);
				}
			}
			catch (Exception e) {
				log.error("处理新增审计数据项失败: {}", afterItem, e);
			}
		}

		// 处理删除和修改的数据（before中有）
		for (Object beforeItem : beforeCollection) {
			try {
				String id = operationLogAspectHelper.extractIdFromData(beforeItem, idExpression);
				if (id == null) {
					log.warn("无法从前置数据中提取ID: {}", beforeItem);
					continue;
				}

				if (processedIds.contains(id)) {
					continue;
				}

				Object afterData = operationLogAspectHelper.findMatchingBeforeData(afterCollection, beforeItem,
						idExpression);
				operationLogAspectHelper.storeOperateLog(annotation, id, beforeItem, afterData);
			}
			catch (Exception e) {
				log.error("处理删除/修改审计数据项失败: {}", beforeItem, e);
			}
		}
	}

	/**
	 * 确定批量数据集合
	 */
	@SuppressWarnings("unchecked")
	private Collection<?> determineBatchDataCollection(Object batchBeforeData, Object batchAfterData) {
		// 优先使用前置数据
		if (batchBeforeData instanceof Collection) {
			return (Collection<?>) batchBeforeData;
		}
		// 其次使用后置数据
		if (batchAfterData instanceof Collection) {
			return (Collection<?>) batchAfterData;
		}

		// 如果都不是集合，尝试包装为单元素集合
		if (batchAfterData != null) {
			return List.of(batchAfterData);
		}

		if (batchBeforeData != null) {
			return List.of(batchBeforeData);
		}

		return null;
	}

	/**
	 * 获取操作后的数据。
	 * @param joinPoint 切点
	 * @param result 操作结果
	 * @return 操作后的数据
	 */
	private Object getAfterData(ProceedingJoinPoint joinPoint, Object result) {
		Object afterData = null;
		if (auditLogProperties.getOperation().isDataAudit()) {
			afterData = operationLogAspectHelper.getData(joinPoint, OperateLog::afterDataAccess, result);
		}
		return afterData;
	}

	/**
	 * 获取操作前的数据。
	 * @param joinPoint 切点
	 * @return 操作前的数据
	 */
	private Object getBeforeData(ProceedingJoinPoint joinPoint) {
		Object beforeData = null;
		if (auditLogProperties.getOperation().isDataAudit()) {
			beforeData = operationLogAspectHelper.getData(joinPoint, OperateLog::beforeDataAccess, null);
		}
		return beforeData;
	}

}
