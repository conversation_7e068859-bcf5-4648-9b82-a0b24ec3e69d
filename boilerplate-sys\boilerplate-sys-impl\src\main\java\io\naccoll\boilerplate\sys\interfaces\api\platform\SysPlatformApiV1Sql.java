package io.naccoll.boilerplate.sys.interfaces.api.platform;

import io.naccoll.boilerplate.sys.constant.SysApiConstant;
import io.naccoll.boilerplate.sys.convert.SysSqlConvert;
import io.naccoll.boilerplate.sys.dto.SysSqlCreateCommand;
import io.naccoll.boilerplate.sys.dto.SysSqlDto;
import io.naccoll.boilerplate.sys.dto.SysSqlQueryCondition;
import io.naccoll.boilerplate.sys.dto.SysSqlUpdateCommand;
import io.naccoll.boilerplate.sys.model.SysSqlPo;
import io.naccoll.boilerplate.sys.service.SysSqlQueryService;
import io.naccoll.boilerplate.sys.service.SysSqlService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;

/**
 * 动态SQL集管理接口
 *
 * 该接口提供动态SQL集的增删改查功能，支持单条和批量操作，并提供分页查询能力。 所有接口均需权限校验，具体权限点如下： - 读取：sys/sql:read -
 * 新增：sys/sql:add - 修改：sys/sql:edit - 删除：sys/sql:del
 *
 * <AUTHOR>
 */
@RestController
@Tag(name = "动态SQL集管理")
@RequestMapping(SysApiConstant.PlatformApiV1.SQL)
public class SysPlatformApiV1Sql {

	@Resource
	private SysSqlService sysSqlService;

	@Resource
	private SysSqlQueryService sysSqlQueryService;

	@Resource
	private SysSqlConvert sysSqlConvert;

	/**
	 * 分页查询动态SQL集
	 *
	 * 根据查询条件分页查询动态SQL集列表
	 * @param condition 查询条件
	 * @param pageable 分页参数
	 * @return 分页结果，包含动态SQL集dto列表
	 * @prePermission hasPermission(0L,'GLOBAL','sys/sql:read')
	 */
	@GetMapping("/page")
	@Operation(summary = "分页查询动态SQL集")
	@PreAuthorize("hasPermission(0L,'GLOBAL','sys/sql:read')")
	public ResponseEntity<Page<SysSqlDto>> page(SysSqlQueryCondition condition, Pageable pageable) {
		Page<SysSqlPo> sysSqlPage = sysSqlQueryService.page(condition, pageable);
		Page<SysSqlDto> sysSqlDtoPage = sysSqlConvert.convertSysSqlDtoPage(sysSqlPage);
		return new ResponseEntity<>(sysSqlDtoPage, HttpStatus.OK);
	}

	/**
	 * 查询单个动态SQL集
	 *
	 * 根据ID查询单个动态SQL集详情
	 * @param id 动态SQL集ID
	 * @return 动态SQL集dto
	 * @prePermission hasPermission(0L,'GLOBAL','sys/sql:read')
	 */
	@GetMapping("/{id}")
	@Operation(summary = "查询动态SQL集")
	@PreAuthorize("hasPermission(0L,'GLOBAL','sys/sql:read')")
	public ResponseEntity<SysSqlDto> queryOne(@PathVariable Long id) {
		SysSqlPo sysSqlPo = sysSqlQueryService.findByIdNotNull(id);
		SysSqlDto sysSqlDto = sysSqlConvert.convertSysSqlDto(sysSqlPo);
		return new ResponseEntity<>(sysSqlDto, HttpStatus.OK);
	}

	/**
	 * 新增动态SQL集
	 *
	 * 创建新的动态SQL集记录
	 * @param command 新增命令对象
	 * @return 新增的动态SQL集dto
	 * @prePermission hasPermission(0L,'GLOBAL','sys/sql:add')
	 */
	@PostMapping
	@Operation(summary = "新增动态SQL集")
	@PreAuthorize("hasPermission(0L,'GLOBAL','sys/sql:add')")
	public ResponseEntity<SysSqlDto> createSysSql(@RequestBody SysSqlCreateCommand command) {
		SysSqlPo sysSqlPo = sysSqlService.create(command);
		SysSqlDto sysSqlDto = sysSqlConvert.convertSysSqlDto(sysSqlPo);
		return new ResponseEntity<>(sysSqlDto, HttpStatus.CREATED);
	}

	/**
	 * 批量新增动态SQL集
	 *
	 * 批量创建新的动态SQL集记录
	 * @param commands 新增命令对象列表
	 * @prePermission hasPermission(0L,'GLOBAL','sys/sql:add')
	 */
	@PostMapping("/batch")
	@Operation(summary = "批量新增动态SQL集")
	@PreAuthorize("hasPermission(0L,'GLOBAL','sys/sql:add')")
	public ResponseEntity<Void> batchCreateSysSql(@RequestBody List<SysSqlCreateCommand> commands) {
		for (SysSqlCreateCommand command : commands) {
			sysSqlService.create(command);
		}
		return new ResponseEntity<>(HttpStatus.CREATED);
	}

	/**
	 * 修改动态SQL集
	 *
	 * 根据ID修改动态SQL集记录
	 * @param command 修改命令对象
	 * @return 修改后的动态SQL集dto
	 * @prePermission hasPermission(0L,'GLOBAL','sys/sql:edit')
	 */
	@PutMapping
	@Operation(summary = "修改动态SQL集")
	@PreAuthorize("hasPermission(0L,'GLOBAL','sys/sql:edit')")
	public ResponseEntity<SysSqlDto> updateSysSql(@RequestBody SysSqlUpdateCommand command) {
		SysSqlPo sysSqlPo = sysSqlService.update(command);
		SysSqlDto sysSqlDto = sysSqlConvert.convertSysSqlDto(sysSqlPo);
		return new ResponseEntity<>(sysSqlDto, HttpStatus.OK);
	}

	/**
	 * 批量修改动态SQL集
	 *
	 * 批量修改动态SQL集记录
	 * @param commands 修改命令对象列表
	 * @prePermission hasPermission(0L,'GLOBAL','sys/sql:edit')
	 */
	@PutMapping("/batch")
	@Operation(summary = "批量修改动态SQL集")
	@PreAuthorize("hasPermission(0L,'GLOBAL','sys/sql:edit')")
	@Transactional(rollbackFor = Exception.class)
	public ResponseEntity<SysSqlDto> batchUpdateSysSql(@RequestBody List<SysSqlUpdateCommand> commands) {
		sysSqlService.update(commands);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	/**
	 * 删除动态SQL集
	 *
	 * 根据ID删除动态SQL集记录
	 * @param id 动态SQL集ID
	 * @prePermission hasPermission(0L,'GLOBAL','sys/sql:del')
	 */
	@DeleteMapping("/{id}")
	@Operation(summary = "删除动态SQL集")
	@PreAuthorize("hasPermission(0L,'GLOBAL','sys/sql:del')")
	public ResponseEntity<Void> deleteSysSql(@PathVariable Long id) {
		sysSqlService.deleteById(id);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	/**
	 * 批量删除动态SQL集
	 *
	 * 批量删除动态SQL集记录
	 * @param ids 动态SQL集ID列表
	 * @prePermission hasPermission(0L,'GLOBAL','sys/sql:del')
	 */
	@DeleteMapping("/batch")
	@Operation(summary = "批量删除动态SQL集")
	@PreAuthorize("hasPermission(0L,'GLOBAL','sys/sql:del')")
	@Transactional(rollbackFor = Exception.class)
	public ResponseEntity<Void> batchDeleteSysSql(@RequestParam Collection<Long> ids) {
		sysSqlService.deleteByIds(ids);
		return new ResponseEntity<>(HttpStatus.OK);
	}

}
