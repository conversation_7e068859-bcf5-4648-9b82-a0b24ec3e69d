package io.naccoll.boilerplate.core.cache.simple;

import io.naccoll.boilerplate.core.cache.CacheTemplate;
import io.naccoll.boilerplate.core.cache.CustomCacheProperties;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;

/**
 * 内存缓存模板自动配置类，用于Spring Boot应用的自动装配
 * <p>
 * 该类负责初始化内存缓存相关组件，并提供默认的缓存模板配置。 通过{@link EnableConfigurationProperties}注解启用缓存配置属性。
 * </p>
 *
 * <AUTHOR>
 */
@AutoConfiguration
@EnableConfigurationProperties(CustomCacheProperties.class)
public class InMemoryCacheTemplateAutoConfig {

	/**
	 * 创建内存缓存写入器Bean，用于执行具体的缓存写入操作
	 * @return 初始化后的内存缓存写入器实例
	 */
	@Bean
	public InMemoryCacheWriter inMemoryCacheWriter() {
		return new InMemoryCacheWriter();
	}

	/**
	 * 创建内存缓存模板Bean，提供默认的缓存实现
	 * <p>
	 * 该方法在没有其他{@link CacheTemplate}实现时自动装配生效。
	 * 使用{@link ConditionalOnMissingBean}确保仅在没有其他实现时创建。
	 * </p>
	 * @param inMemoryCacheWriter 内存缓存写入器，用于执行缓存写入操作
	 * @param customCacheProperties 缓存配置属性，包含缓存前缀等配置信息
	 * @return 初始化后的内存缓存模板实例，实现了{@link CacheTemplate}接口
	 */
	@Bean
	@ConditionalOnMissingBean(CacheTemplate.class)
	public CacheTemplate inMemoryCacheTemplate(InMemoryCacheWriter inMemoryCacheWriter,
			CustomCacheProperties customCacheProperties) {
		return new InMemoryCacheTemplateImpl(inMemoryCacheWriter, 0xfff, customCacheProperties.getPrefix());
	}

}
