package io.naccoll.boilerplate.core.statemachine.builder;

/**
 * ExternalTransitionsBuilder
 *
 * This builder is for multiple transitions, currently only support multiple sources
 * <----> one target
 *
 * @param <S> the type parameter
 * @param <E> the type parameter
 * @param <C> the type parameter
 * <AUTHOR>
 * @date 2020 -02-08 7:41 PM
 */
public interface ExternalTransitionsBuilder<S, E, C> {

	/**
	 * Sources source.
	 * @param stateIds the state ids
	 * @return the source
	 */
	Source<S, E, C> sources(S... stateIds);

}
