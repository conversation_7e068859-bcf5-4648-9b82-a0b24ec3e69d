package io.naccoll.boilerplate.audit.dto;

import io.naccoll.boilerplate.audit.model.AuditDataModelPo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 数据审计模型DTO类，用于数据审计模型的返回结果。 该类继承自AuditDataModelPo，通过Lombok的@Data注解提供标准的getter和setter方法，
 * 并通过@EqualsAndHashCode注解提供equals和hashCode方法实现。
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AuditDataModelDto extends AuditDataModelPo {

}
