package io.naccoll.boilerplate.core.statemachine.builder;

/**
 * De<PERSON>ult fail callback, do nothing.
 *
 * @param <S> the type parameter
 * @param <E> the type parameter
 * @param <C> the type parameter
 * <AUTHOR>
 * @date 2022 /9/15 12:02 PM
 */
public class NumbFailCallback<S, E, C> implements FailCallback<S, E, C> {

	@Override
	public void onFail(S sourceState, E event, C context) {
		// do nothing
	}

}
