package io.naccoll.boilerplate.core.statemachine.builder;

/**
 * StateMachineBuilderFactory
 *
 * <AUTHOR>
 * @date 2020 -02-08 12:33 PM
 */
public class StateMachineBuilderFactory {

	private StateMachineBuilderFactory() {
	}

	/**
	 * Create state machine builder.
	 * @param <S> the type parameter
	 * @param <E> the type parameter
	 * @param <C> the type parameter
	 * @return the state machine builder
	 */
	public static <S, E, C> StateMachineBuilder<S, E, C> create() {
		return new StateMachineBuilderImpl<>();
	}

	/**
	 * Create state machine builder.
	 * @param <S> the type parameter
	 * @param <E> the type parameter
	 * @param <C> the type parameter
	 * @param state the state
	 * @return the state machine builder
	 */
	public static <S, E, C> StateMachineBuilder<S, E, C> create(S state) {
		return new StateMachineBuilderImpl<>(state);
	}

}
