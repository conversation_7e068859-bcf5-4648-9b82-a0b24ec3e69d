package io.naccoll.boilerplate.core.ratelimit;

import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.data.redis.core.StringRedisTemplate;

/**
 * Redis限流器自动配置类，用于在Redis环境下自动配置限流器相关功能
 *
 * <AUTHOR>
 */
@AutoConfiguration
@ConditionalOnClass(RateLimiter.class)
@EnableConfigurationProperties(RateLimiterProperties.class)
@ConditionalOnProperty(value = "spring.cache.type", havingValue = "redis")
public class RedisRateLimiterAutoConfig {

	/**
	 * 创建RedisTokenBucketRateLimiter bean，用于实现基于Redis的令牌桶限流功能
	 * @param redisTemplate Redis字符串模板，用于与Redis进行交互
	 * @param defaultConfig 限流器默认配置参数
	 * @return RedisTokenBucketRateLimiter 限流器实例
	 */
	@Bean
	public RedisTokenBucketRateLimiter redisTokenBucketRateLimiter(StringRedisTemplate redisTemplate,
			RateLimiterProperties defaultConfig) {
		return new RedisTokenBucketRateLimiter(redisTemplate, defaultConfig);
	}

	/**
	 * 创建RedisLeakBucketRateLimiter bean，用于实现基于Redis的漏桶限流功能
	 * @param redisTemplate Redis字符串模板
	 * @param defaultConfig 限流器默认配置参数
	 * @return RedisLeakBucketRateLimiter 限流器实例
	 */
	@Bean
	public RedisLeakBucketRateLimiter redisLeakBucketRateLimiter(StringRedisTemplate redisTemplate,
			RateLimiterProperties defaultConfig) {
		return new RedisLeakBucketRateLimiter(redisTemplate, defaultConfig);
	}

}
