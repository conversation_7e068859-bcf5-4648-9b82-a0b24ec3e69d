package io.naccoll.boilerplate.core.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

/**
 * Spring AOP配置类
 * <p>
 * 通过@Configuration注解标识这是一个配置类 启用AspectJ自动代理功能，支持面向切面编程
 *
 * @EnableAspectJAutoProxy注解用于开启AspectJ自动代理 proxyTargetClass属性设置为true表示使用CGLIB代理而非JDK动态代理
 * exposeProxy属性设置为true表示允许通过AopContext获取代理对象
 * </p>
 * <AUTHOR>
 */
@Configuration
@EnableAspectJAutoProxy
public class AopConfig {

}
