package io.naccoll.boilerplate.annex.dao;

import io.naccoll.boilerplate.annex.dto.OssAnnexQueryCondition;
import io.naccoll.boilerplate.annex.model.OssAnnexPo;
import io.naccoll.boilerplate.core.persistence.dao.BaseDao;
import io.naccoll.boilerplate.core.persistence.jpa.specification.Specifications;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 通用附件数据库访问层
 *
 * <AUTHOR>
 */
@SuppressWarnings("AlibabaAbstractMethodOrInterfaceMethodMustUseJavadoc")
public interface OssAnnexDao extends BaseDao<OssAnnexPo, Long> {

	/**
	 * 根据附件标识、存储位置和桶名称查询第一个匹配的附件
	 * @param sign 附件标识
	 * @param store 存储位置
	 * @param bucketName 桶名称
	 * @return OssAnnexPo
	 */
	OssAnnexPo findFirstByAnnexSignAndOssStoreAndBucketName(String sign, String store, String bucketName);

	/**
	 * 查询OssAnnex列表
	 * @param condition OssAnnex查询条件
	 * @return OssAnnex列表 list
	 */
	default List<OssAnnexPo> findAll(OssAnnexQueryCondition condition) {
		Specification<OssAnnexPo> spec = buildSpecification(condition);
		return findAll(spec);
	}

	/**
	 * 查询OssAnnex分页
	 * @param condition OssAnnex查询条件
	 * @param pageable 分页参数
	 * @return OssAnnex分页 page
	 */
	default Page<OssAnnexPo> page(OssAnnexQueryCondition condition, Pageable pageable) {
		Specification<OssAnnexPo> spec = buildSpecification(condition);
		return findAll(spec, pageable);
	}

	/**
	 * 构建查询条件
	 * @param condition 查询条件
	 * @return Specification
	 */
	default Specification<OssAnnexPo> buildSpecification(OssAnnexQueryCondition condition) {
		Specifications<OssAnnexPo> spec = Specifications.builder();
		spec.contain(StringUtils.hasText(condition.getAnnexName()), OssAnnexPo::getAnnexName, condition.getAnnexName());
		return spec;
	}

}
