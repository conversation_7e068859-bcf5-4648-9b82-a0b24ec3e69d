package io.naccoll.boilerplate.cms.interfaces.api.pub;

import io.naccoll.boilerplate.cms.constant.CmsApiConstant;
import io.naccoll.boilerplate.cms.convert.CmsConvert;
import io.naccoll.boilerplate.cms.dto.CmsColumnDto;
import io.naccoll.boilerplate.cms.service.CmsColumnQueryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 公共栏目API接口
 *
 * <AUTHOR>
 */
@Tag(name = "栏目API")
@RestController
@RequestMapping(CmsApiConstant.PublicApiV1.COLUMN)
public class CmsPublicApiV1Column {

	@Resource
	private CmsConvert cmsConvert;

	@Resource
	private CmsColumnQueryService cmsColumnQueryService;

	/**
	 * 获取所有可用栏目
	 * @return 栏目DTO列表
	 */
	@Operation(summary = "获取可用的栏目")
	@GetMapping("/all-enable")
	public List<CmsColumnDto> getAllColumnEnable() {
		return cmsConvert.convertCmsColumnDtoTree(cmsColumnQueryService.findAllEnable());
	}

}
