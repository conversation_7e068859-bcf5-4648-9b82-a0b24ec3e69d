package io.naccoll.boilerplate.core.security.ratelimit;

import io.naccoll.boilerplate.core.exception.RateLimitException;
import io.naccoll.boilerplate.core.ratelimit.RateLimiterProperties;
import io.naccoll.boilerplate.core.security.filter.AbstractCustomFilter;
import io.naccoll.boilerplate.core.security.filter.AbstractCustomFilterFactory;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.context.ApplicationContext;

import java.io.IOException;
import java.util.Map;

/**
 * 限流过滤器工厂类，用于创建限流过滤器实例
 *
 * <AUTHOR>
 */
public class RateLimiterFilterFactory extends AbstractCustomFilterFactory<RateLimiterProperties> {

	/**
	 * 限流服务实现类
	 */
	private final RateLimiterService rateLimitService;

	/**
	 * 默认的限流键解析器
	 */
	private final RateLimitKeyResolver defaultResolver;

	/**
	 * 限流键解析器映射，用于根据配置选择不同的解析器
	 */
	private final Map<String, RateLimitKeyResolver> resolverMap;

	/**
	 * 构造方法，初始化限流服务、默认解析器和解析器映射
	 * @param rateLimitService 限流服务实现类
	 * @param defaultResolver 默认的限流键解析器
	 * @param applicationContext Spring应用上下文，用于获取所有解析器实例
	 */
	protected RateLimiterFilterFactory(RateLimiterService rateLimitService, RateLimitKeyResolver defaultResolver,
			ApplicationContext applicationContext) {
		super(RateLimiterProperties.class);
		this.rateLimitService = rateLimitService;
		this.defaultResolver = defaultResolver;
		this.resolverMap = applicationContext.getBeansOfType(RateLimitKeyResolver.class);
	}

	@Override
	public AbstractCustomFilter apply(RateLimiterProperties args) {
		return new AbstractCustomFilter() {
			@Override
			public void doFilterReal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
					throws ServletException, IOException {
				RateLimitKeyResolver resolver = resolverMap.getOrDefault(args.getResolver(), defaultResolver);
				String key = resolver.resolve(request, response);
				if (!rateLimitService.isAllowed(key, args)) {
					throw new RateLimitException("访问过于频繁");
				}
				filterChain.doFilter(request, response);
			}
		};
	}

}
