package io.naccoll.boilerplate.core.persistence.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.naccoll.boilerplate.core.audit.PersistableObj;
import io.swagger.v3.oas.annotations.Hidden;
import jakarta.persistence.MappedSuperclass;
import jakarta.persistence.PostLoad;
import jakarta.persistence.PrePersist;
import jakarta.persistence.Transient;
import org.springframework.data.domain.Persistable;
import org.springframework.data.jpa.domain.AbstractPersistable;
import org.springframework.data.util.ProxyUtils;
import org.springframework.lang.NonNull;

/**
 * 基础持久化实体类，作为所有持久化实体的基类，提供基础的持久化功能和状态管理。 继承自AbstractPersistable，实现Persistable接口，用于Spring
 * Data的持久化操作。 该类通过{@link #isNewPersistableEntity}字段管理实体的新建状态， 并在实体被持久化或加载后自动标记为非新建状态。
 *
 * <AUTHOR>
 */
@MappedSuperclass
public abstract class BasePersistableEntity<T> implements Persistable<T>, PersistableObj {

	/**
	 * 获取实体的唯一标识符。
	 * @return 实体的唯一标识符
	 */
	@Override
	@NonNull
	public abstract T getId();

	@Transient
	@JsonIgnore
	@Hidden
	private boolean isNewPersistableEntity = true;

	/**
	 * 在实体被持久化或加载后标记实体为非新建状态。
	 * 该方法在{@link jakarta.persistence.PrePersist}和{@link jakarta.persistence.PostLoad}注解的作用下，
	 * 会在实体保存前和加载后自动调用，将{@link #isNewPersistableEntity}设置为false。
	 */
	@PrePersist
	@PostLoad
	void markNotNew() {
		this.isNewPersistableEntity = false;
	}

	/**
	 * 判断实体是否为新建状态。
	 * @return true表示实体为新建状态，false表示已存在
	 */
	@Override
	@Transient
	@JsonIgnore
	@Hidden
	public boolean isNew() {
		return isNewPersistableEntity;
	}

	/**
	 * 设置实体的新建状态。
	 * @param isNew 新建状态标志
	 */
	@Override
	@Transient
	@JsonIgnore
	@Hidden
	public void setNew(boolean isNew) {
		this.isNewPersistableEntity = isNew;
	}

	@Override
	public boolean equals(Object obj) {
		if (null == obj) {
			return false;
		}

		if (this == obj) {
			return true;
		}

		if (!getClass().equals(ProxyUtils.getUserClass(obj))) {
			return false;
		}

		BasePersistableEntity<?> that = (BasePersistableEntity<?>) obj;
		Object id = getId();

		return null != id && id.equals(that.getId());
	}

	@Override
	public int hashCode() {
		int hashCode = 17;
		Object id = getId();
		hashCode += null == id ? 0 : id.hashCode() * 31;
		return hashCode;
	}

}
