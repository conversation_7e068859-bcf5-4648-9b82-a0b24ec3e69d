package io.naccoll.boilerplate.annex.interfaces.api.user;

import io.naccoll.boilerplate.annex.convert.OssAnnexRefConvert;
import io.naccoll.boilerplate.annex.dto.*;
import io.naccoll.boilerplate.annex.model.OssAnnexRefPo;
import io.naccoll.boilerplate.annex.service.OssAnnexRefQueryService;
import io.naccoll.boilerplate.annex.service.OssAnnexRefService;
import io.naccoll.boilerplate.annex.service.OssAnnexService;
import io.naccoll.boilerplate.core.ratelimit.RateLimit;
import io.naccoll.boilerplate.core.ratelimit.RateLimiterMode;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collection;
import java.util.List;

/**
 * 通用附件管理
 *
 * <AUTHOR>
 */
@RestController
@Tag(name = "通用附件管理")
@RequestMapping("/annex/user/api/v1/annex")
public class AnnexUserApiV1Annex {

	@Resource
	private OssAnnexService ossAnnexService;

	@Resource
	private OssAnnexRefService ossAnnexRefService;

	@Resource
	private OssAnnexRefQueryService ossAnnexRefQueryService;

	@Resource
	private OssAnnexRefConvert ossAnnexRefConvert;

	/**
	 * 分页查询通用附件
	 * @param condition the condition
	 * @param pageable the pageable
	 * @return the response entity
	 */
	@GetMapping("/ref/page")
	@Operation(summary = "分页查询通用附件")
	public ResponseEntity<Page<OssAnnexRefDto>> page(OssAnnexRefQueryCondition condition, Pageable pageable) {
		Page<OssAnnexRefPo> ossAnnexPage = ossAnnexRefQueryService.page(condition, pageable);
		Page<OssAnnexRefDto> ossAnnexDtoPage = ossAnnexRefConvert.convertOssAnnexRefDtoPage(ossAnnexPage);
		return new ResponseEntity<>(ossAnnexDtoPage, HttpStatus.OK);
	}

	/**
	 * 新增通用附件
	 * @param command the command
	 * @param file the file
	 * @return the response entity
	 */
	@PostMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
	@Operation(summary = "新增通用附件")
	@RateLimit(key = "#command", mode = RateLimiterMode.WINDOWS, rate = 1)
	public ResponseEntity<OssAnnexRefDto> createOssAnnex(OssAnnexCreateCommand command,
			@RequestPart(value = "file", required = false) MultipartFile file) {
		OssAnnexRefPo ossAnnexPo = ossAnnexService.create(command, file);
		OssAnnexRefDto ossAnnexDto = ossAnnexRefConvert.convertOssAnnexRefDto(ossAnnexPo);
		return new ResponseEntity<>(ossAnnexDto, HttpStatus.CREATED);
	}

	/**
	 * 修改通用附件
	 * @param command the command
	 * @param file the file
	 * @return the response entity
	 */
	@PutMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
	@Operation(summary = "修改通用附件")
	public ResponseEntity<OssAnnexRefDto> updateOssAnnex(OssAnnexUpdateCommand command,
			@RequestPart(value = "file", required = false) MultipartFile file) {
		OssAnnexRefPo ossAnnexPo = ossAnnexService.update(command, file);
		OssAnnexRefDto ossAnnexDto = ossAnnexRefConvert.convertOssAnnexRefDto(ossAnnexPo);
		return new ResponseEntity<>(ossAnnexDto, HttpStatus.OK);
	}

	/**
	 * 新增通用附件引用
	 */
	@PostMapping("/ref")
	@Operation(summary = "保存通用附件引用")
	public ResponseEntity<OssAnnexRefDto> createOssAnnexRef(@RequestBody OssAnnexRefCreateCommand command) {
		OssAnnexRefPo ossAnnexRefPo = ossAnnexRefService.save(command);
		OssAnnexRefDto ossAnnexRefDto = ossAnnexRefConvert.convertOssAnnexRefDto(ossAnnexRefPo);
		return new ResponseEntity<>(ossAnnexRefDto, HttpStatus.CREATED);
	}

	/**
	 * 批量新增通用附件引用
	 */
	@PostMapping("/ref/batch")
	@Operation(summary = "批量保存通用附件引用")
	@Transactional(rollbackFor = Exception.class)
	public ResponseEntity<Void> batchCreateOssAnnexRef(@RequestBody List<OssAnnexRefCreateCommand> commands) {
		for (OssAnnexRefCreateCommand command : commands) {
			ossAnnexRefService.save(command);
		}
		return new ResponseEntity<>(HttpStatus.CREATED);
	}

	/**
	 * 删除通用附件引用
	 * @param id the id
	 * @return the response entity
	 */
	@DeleteMapping("/ref/{id}")
	@Operation(summary = "删除通用附件引用")
	public ResponseEntity<Void> deleteOssAnnex(@PathVariable Long id) {
		ossAnnexRefService.deleteById(id);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	/**
	 * 批量删除通用附件引用
	 * @param ids the ids
	 * @return the response entity
	 */
	@DeleteMapping("/ref/batch")
	@Operation(summary = "批量删除通用附件引用")
	@Transactional(rollbackFor = Exception.class)
	public ResponseEntity<Void> batchDeleteOssAnnex(@RequestParam Collection<Long> ids) {
		for (Long id : ids) {
			ossAnnexRefService.deleteById(id);
		}
		return new ResponseEntity<>(HttpStatus.OK);
	}

}
