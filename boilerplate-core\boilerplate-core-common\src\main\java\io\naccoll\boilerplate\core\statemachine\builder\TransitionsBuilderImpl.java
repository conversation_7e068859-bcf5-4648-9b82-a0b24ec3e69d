package io.naccoll.boilerplate.core.statemachine.builder;

import io.naccoll.boilerplate.core.statemachine.Action;
import io.naccoll.boilerplate.core.statemachine.Guard;
import io.naccoll.boilerplate.core.statemachine.State;
import io.naccoll.boilerplate.core.statemachine.Transition;
import io.naccoll.boilerplate.core.statemachine.impl.StateHelper;
import io.naccoll.boilerplate.core.statemachine.impl.TransitionType;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * TransitionsBuilderImpl
 *
 * @param <S> the type parameter
 * @param <E> the type parameter
 * @param <C> the type parameter
 * <AUTHOR>
 * @date 2020 -02-08 7:43 PM
 */
public class TransitionsBuilderImpl<S, E, C> extends TransitionBuilderImpl<S, E, C>
		implements ExternalTransitionsBuilder<S, E, C> {

	/**
	 * This is for sources where multiple sources can be configured target point target
	 * one target
	 */
	private final List<State<S, E, C>> sources = new ArrayList<>();

	private final List<Transition<S, E, C>> transitions = new ArrayList<>();

	/**
	 * Instantiates a new Transitions builder.
	 * @param stateMap the state map
	 * @param transitionType the transition type
	 */
	public TransitionsBuilderImpl(Map<S, State<S, E, C>> stateMap, TransitionType transitionType) {
		super(stateMap, transitionType);
	}

	@Override
	public Source<S, E, C> sources(S... stateIds) {
		for (S stateId : stateIds) {
			sources.add(StateHelper.getState(super.stateMap, stateId));
		}
		return this;
	}

	@Override
	public On<S, E, C> event(E event) {
		for (State<S, E, C> source : sources) {
			Transition<S, E, C> transition = source.addTransition(event, super.target, super.transitionType);
			transitions.add(transition);
		}
		return this;
	}

	@Override
	public When<S, E, C> guard(Guard<C> guard) {
		for (Transition<S, E, C> transition : transitions) {
			transition.setCondition(guard);
		}
		return this;
	}

	@Override
	public void action(Action<S, E, C>... action) {
		for (Transition<S, E, C> transition : transitions) {
			transition.setActions(action);
		}
	}

}
