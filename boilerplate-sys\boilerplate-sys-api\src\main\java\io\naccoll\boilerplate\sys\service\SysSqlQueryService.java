package io.naccoll.boilerplate.sys.service;

import io.naccoll.boilerplate.sys.dto.SysSqlQueryCondition;
import io.naccoll.boilerplate.sys.model.SysSqlPo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 动态SQL集查询服务接口 提供对动态SQL集的各类查询操作
 *
 * <AUTHOR>
 */
public interface SysSqlQueryService {

	/**
	 * 根据编码查询动态SQL集
	 * @param code 动态SQL集编码
	 * @return 查询到的动态SQL集对象，可能为null
	 */
	SysSqlPo findByCode(String code);

	/**
	 * 根据编码查询动态SQL集
	 * @param codes 动态SQL集编码
	 * @return 查询到的动态SQL集对象，可能为null
	 */
	List<SysSqlPo> findByCodes(Collection<String> codes);

	default Map<String, SysSqlPo> findMapByCodes(Collection<String> codes) {
		List<SysSqlPo> list = findByCodes(codes);
		return list.stream().collect(java.util.stream.Collectors.toMap(SysSqlPo::getCode, sysSqlPo -> sysSqlPo));
	}

	/**
	 * 根据编码查询动态SQL集，结果为空则抛出异常
	 * @param code 动态SQL集编码
	 * @return 查询到的动态SQL集对象，不为null
	 * @throws RuntimeException 当查询结果为空时抛出
	 */
	SysSqlPo findByCodeNotNull(String code);

	/**
	 * 分页查询动态SQL集数据
	 * @param condition 查询条件对象
	 * @param pageable 分页参数
	 * @return 分页查询结果
	 */
	Page<SysSqlPo> page(SysSqlQueryCondition condition, Pageable pageable);

	/**
	 * 查询所有符合条件的动态SQL集数据（不分页）
	 * @param condition 查询条件对象
	 * @return 符合条件的动态SQL集列表
	 */
	List<SysSqlPo> findAll(SysSqlQueryCondition condition);

	/**
	 * 根据ID查询动态SQL集
	 * @param id 动态SQL集ID
	 * @return 查询到的动态SQL集对象，可能为null
	 */
	SysSqlPo findById(Long id);

	/**
	 * 根据ID查询动态SQL集，结果为空则抛出异常
	 * @param id 动态SQL集ID
	 * @return 查询到的动态SQL集对象，不为null
	 * @throws RuntimeException 当查询结果为空时抛出
	 */
	SysSqlPo findByIdNotNull(Long id);

	/**
	 * 根据ID集合批量查询动态SQL集列表
	 * @param ids 动态SQL集ID集合
	 * @return 查询到的动态SQL集列表
	 */
	List<SysSqlPo> findByIds(Collection<Long> ids);

	/**
	 * 根据ID集合批量查询动态SQL集映射表
	 * @param ids 动态SQL集ID集合
	 * @return ID到动态SQL集对象的映射表
	 */
	Map<Long, SysSqlPo> findMapByIds(Collection<Long> ids);

}
