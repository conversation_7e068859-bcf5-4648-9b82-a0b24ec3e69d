package io.naccoll.boilerplate.core.statemachine.impl;

import io.naccoll.boilerplate.core.statemachine.Transition;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * EventTransitions
 *
 * @param <S> the type parameter
 * @param <E> the type parameter
 * @param <C> the type parameter
 * <AUTHOR>
 */
public class EventTransitions<S, E, C> {

	private final HashMap<E, List<Transition<S, E, C>>> eventTransitionsMap;

	/**
	 * Instantiates a new Event transitions.
	 */
	public EventTransitions() {
		eventTransitionsMap = HashMap.newHashMap(10);
	}

	/**
	 * Put.
	 * @param event the event
	 * @param transition the transition
	 */
	public void put(E event, Transition<S, E, C> transition) {
		if (eventTransitionsMap.get(event) == null) {
			List<Transition<S, E, C>> transitions = new ArrayList<>();
			transitions.add(transition);
			eventTransitionsMap.put(event, transitions);
		}
		else {
			List<Transition<S, E, C>> existingTransitions = eventTransitionsMap.get(event);
			verify(existingTransitions, transition);
			existingTransitions.add(transition);
		}
	}

	/**
	 * Per one source and target state, there is only one transition is allowed
	 * @param existingTransitions
	 * @param newTransition
	 */
	private void verify(List<Transition<S, E, C>> existingTransitions, Transition<S, E, C> newTransition) {
		for (Transition<S, E, C> transition : existingTransitions) {
			if (transition.equals(newTransition)) {
				throw new StateMachineException(transition + " already Exist, you can not add another one");
			}
		}
	}

	/**
	 * Get list.
	 * @param event the event
	 * @return the list
	 */
	public List<Transition<S, E, C>> get(E event) {
		return eventTransitionsMap.get(event);
	}

	/**
	 * All transitions list.
	 * @return the list
	 */
	public List<Transition<S, E, C>> allTransitions() {
		List<Transition<S, E, C>> allTransitions = new ArrayList<>();
		for (List<Transition<S, E, C>> transitions : eventTransitionsMap.values()) {
			allTransitions.addAll(transitions);
		}
		return allTransitions;
	}

}
