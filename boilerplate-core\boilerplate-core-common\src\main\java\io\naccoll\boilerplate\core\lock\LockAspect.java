package io.naccoll.boilerplate.core.lock;

import io.naccoll.boilerplate.core.cache.CacheTemplate;
import io.naccoll.boilerplate.core.exception.RateLimitException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.context.expression.BeanFactoryResolver;
import org.springframework.core.Ordered;
import org.springframework.core.ParameterNameDiscoverer;
import org.springframework.core.StandardReflectionParameterNameDiscoverer;
import org.springframework.core.annotation.Order;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.lang.reflect.Method;
import java.time.Duration;
import java.util.Objects;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.locks.Lock;

/**
 * 分布式锁切面实现类 通过AOP实现方法级别的分布式锁控制 支持两种锁模式：普通锁和尝试锁 当使用尝试锁时，超过等待时间会抛出RateLimitException
 *
 * <AUTHOR>
 */
@Component
@Aspect
@Slf4j
@Order(Ordered.LOWEST_PRECEDENCE - 99)
public class LockAspect {

	@Resource
	private CacheTemplate cacheTemplate;

	@Resource
	private BeanFactory beanFactory;

	/**
	 * 定义切入点：标记了@UseLock注解的方法
	 */
	@Pointcut("@annotation(io.naccoll.boilerplate.core.lock.UseLock)")
	public void lockPointcut() {
	}

	/**
	 * 获取方法上的UseLock注解
	 * @param joinPoint 连接点对象
	 * @return 方法上的注解实例
	 */
	private UseLock getAnnotation(ProceedingJoinPoint joinPoint) {
		MethodSignature signature = (MethodSignature) joinPoint.getSignature();
		Method method = signature.getMethod();
		return method.getAnnotation(UseLock.class);
	}

	/**
	 * 生成分布式锁键 1. 使用SpEL解析注解的key表达式 2. 未配置表达式时返回空字符串
	 * @param joinPoint 连接点对象
	 * @return 分布式锁键字符串
	 */
	private String getKey(ProceedingJoinPoint joinPoint) {
		UseLock aopLog = getAnnotation(joinPoint);
		String keyExp = aopLog.key();
		if (!StringUtils.hasLength(keyExp)) {
			return "";
		}
		Object[] args = joinPoint.getArgs();
		MethodSignature signature = (MethodSignature) joinPoint.getSignature();
		Method method = signature.getMethod();
		ExpressionParser parser = new SpelExpressionParser();
		ParameterNameDiscoverer discoverer = new StandardReflectionParameterNameDiscoverer();
		String[] params = discoverer.getParameterNames(method);
		StandardEvaluationContext context = new StandardEvaluationContext();
		for (int len = 0; len < Objects.requireNonNull(params).length; len++) {
			context.setVariable("p" + len, args[len]);
			context.setVariable(params[len], args[len]);
		}
		context.setBeanResolver(new BeanFactoryResolver(beanFactory));
		Expression expression = parser.parseExpression(keyExp);
		Object key = expression.getValue(context, Object.class);
		if (key == null) {
			return null;
		}
		return key.toString();
	}

	/**
	 * 环绕通知：实现分布式锁逻辑
	 * @param joinPoint 连接点对象
	 * @return 方法执行结果
	 * @throws Throwable 可能抛出的异常
	 */
	@Around("lockPointcut()")
	public Object lockAround(ProceedingJoinPoint joinPoint) throws Throwable {
		UseLock annotation = getAnnotation(joinPoint);
		String prefix = annotation.prefix();
		String key = getKey(joinPoint);
		LockMode lockMode = annotation.lockMode();
		String lockKey = prefix + ":" + key;
		Lock lock = cacheTemplate.getLock(prefix + ":" + key);
		if (Objects.requireNonNull(lockMode) == LockMode.TRY_LOCK) {
			try {
				return cacheTemplate.executeLocked(lockKey, Duration.ofMillis(annotation.waitTime()),
						() -> joinPoint.proceed());
			}
			catch (TimeoutException e) {
				throw new RateLimitException("业务繁忙，请重试");
			}
		}
		else {
			return cacheTemplate.executeLocked(lockKey, () -> joinPoint.proceed());
		}
	}

}
