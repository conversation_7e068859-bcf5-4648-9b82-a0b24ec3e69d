package io.naccoll.boilerplate.core.ratelimit;

import io.naccoll.boilerplate.core.cache.CacheTemplate;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;

/**
 * 简单限流器自动配置类<br>
 * 该类用于Spring Boot应用的自动配置，提供基于令牌桶算法的限流器实现<br>
 * 通过{@link RateLimiterProperties}配置默认限流规则，并依赖{@link CacheTemplate}进行缓存操作
 *
 * <AUTHOR>
 */
@AutoConfiguration
@ConditionalOnClass(RateLimiter.class)
@EnableConfigurationProperties(RateLimiterProperties.class)
public class SimpleRateLimiterAutoConfig {

	/**
	 * 创建简单令牌桶限流器Bean<br>
	 * 根据配置文件中的限流规则创建限流器实例，并使用缓存模板进行令牌存储和管理
	 * @param cacheTemplate 缓存操作模板，用于令牌的存储和获取
	 * @param defaultConfig 默认限流配置，包含令牌桶容量、生成速率等参数
	 * @return 简单令牌桶限流器实例
	 */
	@Bean
	@ConditionalOnMissingBean(BaseTokenBucketRateLimiter.class)
	public SimpleTokenBucketRateLimiter simpleTokenBucketRateLimiter(CacheTemplate cacheTemplate,
			RateLimiterProperties defaultConfig) {
		return new SimpleTokenBucketRateLimiter(defaultConfig, cacheTemplate);
	}

	/**
	 * 创建简单漏桶限流器Bean
	 * @param cacheTemplate 缓存操作模板
	 * @param defaultConfig 默认限流配置
	 * @return 简单漏桶限流器实例
	 */
	@Bean
	@ConditionalOnMissingBean(BaseLeakBucketRateLimiter.class)
	public SimpleLeakBucketRateLimiter simpleLeakBucketRateLimiter(CacheTemplate cacheTemplate,
			RateLimiterProperties defaultConfig) {
		return new SimpleLeakBucketRateLimiter(defaultConfig, cacheTemplate);
	}

}
