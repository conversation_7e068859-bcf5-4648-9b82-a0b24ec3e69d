package io.naccoll.boilerplate.core.utils;

import cn.hutool.core.util.ReflectUtil;
import io.naccoll.boilerplate.core.exception.ServerException;

import java.lang.invoke.*;
import java.lang.reflect.Constructor;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * Lambda表达式工厂工具类，用于动态创建Lambda表达式和构造函数引用
 *
 * <AUTHOR>
 */
public class LambdaFactory {

	/**
	 * 存储构造器Supplier的缓存Map，键为类类型，值为对应的Supplier
	 */
	public static final Map<Class<?>, Supplier<?>> CONSTRUCTOR_SUPPLIERS = new ConcurrentHashMap<>(100);

	/**
	 * 创建指定类的Supplier构造器
	 * <p>
	 * 测试见{@link io.naccoll.boilerplate.core.utils.LambdaFactoryTest#testNewInstance()}
	 * </p>
	 * <p>
	 * 经测试性能与直接new对象差异不大，可作为{@link ReflectUtil#newInstance(Class, Object...)}的替代品
	 * </p>
	 * @param clazz 目标类
	 * @return 该类的Supplier构造器
	 * @param <T> 目标类型
	 */
	public static <T> Supplier<T> createConstructorSupplier(Class<?> clazz) {
		try {
			Supplier<T> supplier = (Supplier<T>) CONSTRUCTOR_SUPPLIERS.get(clazz);
			if (supplier != null) {
				return supplier;
			}
			Class<Supplier> lambdaType = Supplier.class;
			Method lambdaMethod = findLambdaMethod(lambdaType);
			// 获取目标类的构造方法
			Constructor<?> constructor = clazz.getDeclaredConstructor();
			constructor.setAccessible(true);

			MethodHandles.Lookup targetLookup = MethodHandles.privateLookupIn(clazz, MethodHandles.lookup());
			MethodHandle constructorHandle = targetLookup.unreflectConstructor(constructor);

			CallSite metafactory = LambdaMetafactory.metafactory(targetLookup, lambdaMethod.getName(),
					MethodType.methodType(lambdaType), MethodType.methodType(Object.class), constructorHandle,
					MethodType.methodType(clazz));

			supplier = (Supplier<T>) metafactory.getTarget().invoke();
			CONSTRUCTOR_SUPPLIERS.put(clazz, supplier);
			return supplier;
		}
		catch (Throwable e) {
			throw new RuntimeException("Failed to create constructor supplier", e);
		}
	}

	/**
	 * 创建Lambda表达式工厂
	 * @param lambdaType Lambda接口类型
	 * @param implMethod 实现方法
	 * @return 创建Lambda表达式的函数
	 * @param <T> 输入类型
	 * @param <L> Lambda类型
	 */
	public static <T, L> Function<T, L> createLambdaFactory(Class<? super L> lambdaType, Method implMethod) {
		try {
			Method lambdaMethod = findLambdaMethod(lambdaType);
			MethodType lambdaMethodType = MethodType.methodType(lambdaMethod.getReturnType(),
					lambdaMethod.getParameterTypes());

			Class<?> implType = implMethod.getDeclaringClass();

			MethodHandles.Lookup lookup = MethodHandles.lookup().in(LambdaFactory.class);
			MethodType implMethodType = MethodType.methodType(implMethod.getReturnType(),
					implMethod.getParameterTypes());
			MethodHandle implMethodHandle = lookup.findVirtual(implType, implMethod.getName(), implMethodType);

			MethodType invokedMethodType = MethodType.methodType(lambdaType, implType);

			CallSite metafactory = LambdaMetafactory.metafactory(lookup, lambdaMethod.getName(), invokedMethodType,
					lambdaMethodType, implMethodHandle, implMethodType);

			MethodHandle factory = metafactory.getTarget();
			return instance -> {
				try {
					@SuppressWarnings("unchecked")
					L lambda = (L) factory.invoke(instance);
					return lambda;
				}
				catch (Throwable throwable) {
					throw new RuntimeException(throwable);
				}
			};
		}
		catch (NoSuchMethodException | IllegalAccessException | LambdaConversionException e) {
			throw new ServerException(e);
		}
	}

	/**
	 * 查找Lambda接口中的抽象方法
	 * @param type Lambda接口类型
	 * @return 接口中的抽象方法
	 * @throws IllegalArgumentException 如果不是函数式接口
	 */
	public static Method findLambdaMethod(Class<?> type) {
		if (!type.isInterface()) {
			throw new IllegalArgumentException("This must be interface: " + type);
		}
		Method[] methods = getAllMethods(type);
		if (methods.length == 0) {
			throw new IllegalArgumentException("No methods in: " + type.getName());
		}
		Method targetMethod = null;
		for (Method method : methods) {
			if (isInterfaceMethod(method)) {
				if (targetMethod != null) {
					throw new IllegalArgumentException("This isn't functional interface: " + type.getName());
				}
				targetMethod = method;
			}
		}
		if (targetMethod == null) {
			throw new IllegalArgumentException("No method in: " + type.getName());
		}
		return targetMethod;
	}

	/**
	 * 获取类及其父类的所有方法
	 * @param type 目标类
	 * @return 方法数组
	 */
	public static Method[] getAllMethods(Class<?> type) {
		LinkedList<Method> result = new LinkedList<>();
		Class<?> current = type;
		do {
			result.addAll(Arrays.asList(current.getMethods()));
		}
		while ((current = current.getSuperclass()) != null);
		return result.toArray(new Method[0]);
	}

	/**
	 * 判断方法是否为接口抽象方法
	 * @param method 要检查的方法
	 * @return 如果是接口抽象方法返回true
	 */
	public static boolean isInterfaceMethod(Method method) {
		return !method.isDefault() && Modifier.isAbstract(method.getModifiers());
	}

}
