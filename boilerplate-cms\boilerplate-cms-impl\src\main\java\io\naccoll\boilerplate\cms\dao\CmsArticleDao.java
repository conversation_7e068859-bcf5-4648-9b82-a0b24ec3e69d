package io.naccoll.boilerplate.cms.dao;

import io.naccoll.boilerplate.cms.constant.CmsCacheName;
import io.naccoll.boilerplate.cms.dto.CmsArticleQueryCondition;
import io.naccoll.boilerplate.cms.model.CmsArticlePo;
import io.naccoll.boilerplate.core.persistence.dao.BaseDao;
import io.naccoll.boilerplate.core.persistence.jpa.specification.Specifications;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Optional;

/**
 * 文章数据访问接口.
 *
 * <AUTHOR>
 */
@SuppressWarnings("AlibabaAbstractMethodOrInterfaceMethodMustUseJavadoc")
public interface CmsArticleDao extends BaseDao<CmsArticlePo, Long> {

	/**
	 * 根据ID查询文章.
	 * @param id 文章ID
	 * @return 文章实体
	 */
	@Override
	@Cacheable(value = CmsCacheName.ARTICLE_ID, key = "#p0")
	Optional<CmsArticlePo> findById(Long id);

	/**
	 * 查询所有文章.
	 * @return 文章列表
	 */
	@Override
	List<CmsArticlePo> findAll();

	/**
	 * 保存文章.
	 * @param entity 文章实体
	 * @param <S> 泛型类型
	 * @return 保存后的文章实体
	 */
	@Override
	@Caching(put = { @CachePut(value = CmsCacheName.ARTICLE_ID, key = "#result.id"), })
	<S extends CmsArticlePo> S save(S entity);

	/**
	 * 批量保存文章.
	 * @param entities 文章实体集合
	 * @param <S> 泛型类型
	 * @return 保存后的文章实体列表
	 */
	@Override
	@Caching(evict = { @CacheEvict(value = CmsCacheName.ARTICLE_ID, allEntries = true) })
	<S extends CmsArticlePo> List<S> saveAll(Iterable<S> entities);

	/**
	 * 保存并刷新文章缓存.
	 * @param entity 文章实体
	 * @param <S> 泛型类型
	 * @return 保存后的文章实体
	 */
	@Override
	@Caching(put = { @CachePut(value = CmsCacheName.ARTICLE_ID, key = "#result.id"), })
	<S extends CmsArticlePo> S saveAndFlush(S entity);

	/**
	 * 批量保存并刷新文章缓存.
	 * @param entities 文章实体集合
	 * @param <S> 泛型类型
	 * @return 保存后的文章实体列表
	 */
	@Override
	@Caching(evict = { @CacheEvict(value = CmsCacheName.ARTICLE_ID, allEntries = true) })
	<S extends CmsArticlePo> List<S> saveAllAndFlush(Iterable<S> entities);

	/**
	 * 根据ID删除文章.
	 * @param id 文章ID
	 */
	@Override
	@Caching(evict = { @CacheEvict(value = CmsCacheName.ARTICLE_ID, key = "#p0") })
	void deleteById(Long id);

	/**
	 * 删除文章.
	 * @param entity 文章实体
	 */
	@Override
	@Caching(evict = { @CacheEvict(value = CmsCacheName.ARTICLE_ID, key = "#p0.id", condition = "#p0.id != null") })
	void delete(CmsArticlePo entity);

	/**
	 * 批量删除文章.
	 * @param entities 文章实体集合
	 */
	@Override
	@Caching(evict = { @CacheEvict(value = CmsCacheName.ARTICLE_ID, allEntries = true) })
	void deleteAllInBatch(Iterable<CmsArticlePo> entities);

	/**
	 * 删除所有文章.
	 */
	@Override
	@Caching(evict = { @CacheEvict(value = CmsCacheName.ARTICLE_ID, allEntries = true) })
	void deleteAllInBatch();

	/**
	 * 删除所有文章.
	 * @param entities 文章实体集合
	 */
	@Override
	@Caching(evict = { @CacheEvict(value = CmsCacheName.ARTICLE_ID, allEntries = true) })
	void deleteAll(Iterable<? extends CmsArticlePo> entities);

	/**
	 * 根据ID集合删除文章.
	 * @param longs ID集合
	 */
	@Override
	@Caching(evict = { @CacheEvict(value = CmsCacheName.ARTICLE_ID, allEntries = true) })
	void deleteAllById(Iterable<? extends Long> longs);

	/**
	 * 批量删除文章.
	 * @param longs ID集合
	 */
	@Override
	@Caching(evict = { @CacheEvict(value = CmsCacheName.ARTICLE_ID, allEntries = true) })
	void deleteAllByIdInBatch(Iterable<Long> longs);

	/**
	 * 删除所有文章.
	 */
	@Override
	@Caching(evict = { @CacheEvict(value = CmsCacheName.ARTICLE_ID, allEntries = true) })
	void deleteAll();

	/**
	 * 分页查询文章.
	 * @param pageable 分页参数
	 * @param c 查询条件
	 * @return 分页结果
	 */
	default Page<CmsArticlePo> page(Pageable pageable, CmsArticleQueryCondition c) {
		Specifications<CmsArticlePo> spec = Specifications.builder();
		if (c.getColumnId() != null) {
			spec.eq(CmsArticlePo::getColumnId, c.getColumnId());
		}
		if (StringUtils.hasLength(c.getTitle())) {
			spec.contain(CmsArticlePo::getTitle, c.getTitle());
		}
		if (c.getStatus() != null) {
			spec.eq(CmsArticlePo::getStatus, c.getStatus());
		}
		return findAll(spec, pageable);
	}

}
