package io.naccoll.boilerplate.core.exception;

import java.io.Serial;

/**
 * 客户端异常，表示客户端请求参数错误或非法操作
 *
 * <AUTHOR>
 */
public class ClientException extends BusinessException {

	@Serial
	private static final long serialVersionUID = 234133358318709L;

	/**
	 * 创建客户端异常实例
	 * @param code 错误码
	 * @param parameters 错误参数列表
	 * @throws IllegalArgumentException 如果参数数量与错误码定义不匹配
	 */
	public ClientException(String code, Object... parameters) {
		super(code, BusinessError.GENERAL_CLIENT, parameters);
	}

	/**
	 * 创建包含原始异常的客户端异常实例
	 * @param code 错误码
	 * @param throwable 原始异常对象
	 * @param parameters 错误参数列表
	 * @throws IllegalArgumentException 如果参数数量与错误码定义不匹配
	 */
	public ClientException(String code, Throwable throwable, Object... parameters) {
		super(code, BusinessError.GENERAL_CLIENT, throwable, parameters);
	}

	/**
	 * 创建指定业务错误类型的客户端异常实例
	 * @param code 错误码
	 * @param error 业务错误类型
	 * @param throwable 原始异常对象
	 * @param parameters 错误参数列表
	 * @throws IllegalArgumentException 如果参数数量与错误码定义不匹配
	 */
	public ClientException(String code, BusinessError error, Throwable throwable, Object... parameters) {
		super(code, error, throwable, parameters);
	}

}
