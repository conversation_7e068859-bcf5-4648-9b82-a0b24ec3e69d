package io.naccoll.boilerplate.audit.convert;

import io.naccoll.boilerplate.audit.dto.AuditDataModelDto;
import io.naccoll.boilerplate.audit.model.AuditDataModelPo;
import io.naccoll.boilerplate.core.utils.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 数据审计模型转换器 将数据审计模型的PO对象转换为DTO对象
 *
 * <AUTHOR>
 */
@Component
public class AuditDataModelConvert {

	/**
	 * 将单个数据审计模型PO对象转换为DTO对象
	 * @param po 数据审计模型PO对象
	 * @return 数据审计模型DTO对象
	 */
	public AuditDataModelDto convertAuditDataModelDto(AuditDataModelPo po) {
		return convertAuditDataModelDtoList(List.of(po)).getFirst();
	}

	/**
	 * 将数据审计模型PO对象列表转换为DTO对象列表
	 * @param list 数据审计模型PO对象列表
	 * @return 数据审计模型DTO对象列表
	 */
	public List<AuditDataModelDto> convertAuditDataModelDtoList(List<AuditDataModelPo> list) {
		return list.stream().map(po -> {
			AuditDataModelDto dto = new AuditDataModelDto();
			BeanUtils.copyProperties(po, dto);
			return dto;
		}).toList();
	}

	/**
	 * 将数据审计模型PO对象分页结果转换为DTO对象分页结果
	 * @param page 数据审计模型PO对象分页
	 * @return 数据审计模型DTO对象分页
	 */
	public Page<AuditDataModelDto> convertAuditDataModelDtoPage(Page<AuditDataModelPo> page) {
		List<AuditDataModelDto> auditDataModelList = convertAuditDataModelDtoList(page.getContent());
		return new PageImpl<>(auditDataModelList, page.getPageable(), page.getTotalElements());
	}

}
