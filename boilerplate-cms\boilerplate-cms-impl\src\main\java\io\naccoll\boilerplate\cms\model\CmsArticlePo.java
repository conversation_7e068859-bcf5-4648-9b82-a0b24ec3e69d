package io.naccoll.boilerplate.cms.model;

import io.naccoll.boilerplate.core.persistence.model.AbstractEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 文章
 *
 * <AUTHOR>
 */
@SQLDelete(sql = "update t_cms_article set is_deleted = true where id = ?")
@SQLRestriction(value = "is_deleted = false")
@Schema(description = "栏目")
@Table(name = "t_cms_article")
@Entity
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
public class CmsArticlePo extends AbstractEntity<Long> implements Serializable {

	@Serial
	private static final long serialVersionUID = 67945834754285674L;

	/**
	 * 文章Id
	 **/
	@Id
	private Long id;

	/**
	 * 文章标题
	 */
	@Schema(description = "文章标题")
	private String title;

	/**
	 * 文章头图
	 */
	@Schema(description = "文章头图")
	private String headImage;

	/**
	 * 文章状态
	 */
	@Schema(description = "文章状态")
	private Integer status;

	/**
	 * 栏目Id
	 */
	@Schema(description = "栏目Id")
	private Long columnId;

	/**
	 * 文章内容
	 */
	@Schema(description = "文章内容")
	private String content;

	/**
	 * 发布时间
	 */
	@Schema(description = "发布时间")
	private Date publishDate;

	/**
	 * 摘要
	 */
	@Schema(description = "摘要")
	private String summary;

}
