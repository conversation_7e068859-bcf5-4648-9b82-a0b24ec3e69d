package io.naccoll.boilerplate.core.security.filter;

import io.naccoll.boilerplate.core.security.properties.SecurityProperties;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.config.AutowireCapableBeanFactory;
import org.springframework.security.web.servlet.util.matcher.PathPatternRequestMatcher;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 签名认证过滤器工厂类，用于创建和配置签名认证过滤器实例
 *
 * <AUTHOR>
 */
@Component
public class SignAuthenticationFilterFactory extends AbstractCustomFilterFactory<SignAuthenticationFilter.Config> {

	/**
	 * Spring容器的Bean工厂，用于自动装配Bean
	 */
	@Resource
	private AutowireCapableBeanFactory beanFactory;

	/**
	 * 安全配置属性，包含忽略Token的URL配置
	 */
	@Resource
	private SecurityProperties securityProperties;

	/**
	 * 初始化父类，指定配置类类型
	 */
	protected SignAuthenticationFilterFactory() {
		super(SignAuthenticationFilter.Config.class);
	}

	/**
	 * 创建并配置签名认证过滤器实例
	 * @param stringObjectMap 配置参数
	 * @return 配置好的签名认证过滤器实例
	 */
	@Override
	public AbstractCustomFilter apply(SignAuthenticationFilter.Config stringObjectMap) {
		List<PathPatternRequestMatcher> matchers = securityProperties.getIgnoreTokenUrls()
			.stream()
			.map(pattern -> PathPatternRequestMatcher.withDefaults().matcher(pattern.getMethod(), pattern.getPath()))
			.collect(Collectors.toList());
		SignAuthenticationFilter signAuthenticationFilter = new SignAuthenticationFilter();
		signAuthenticationFilter.setExcludePatterns(matchers);
		beanFactory.autowireBean(signAuthenticationFilter);
		return signAuthenticationFilter;
	}

}
