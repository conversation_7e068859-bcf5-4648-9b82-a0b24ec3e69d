package io.naccoll.boilerplate.core.persistence.dao;

import cn.hutool.core.collection.ConcurrentHashSet;
import cn.hutool.core.text.NamingCase;
import cn.hutool.core.util.ReflectUtil;
import io.naccoll.boilerplate.core.interfaces.function.PropertyFunc;
import io.naccoll.boilerplate.core.utils.AopTargetUtil;
import io.naccoll.boilerplate.core.utils.PojoHelper;
import jakarta.persistence.Column;
import jakarta.persistence.Id;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 持久化帮助类，用于处理与数据库持久化相关的工具方法。 提供主键字段获取和数据库列名映射功能。
 *
 * <AUTHOR>
 */
@Slf4j
@SuppressWarnings("all")
public class PersistenceHelper {

	public static final String OBJECT = "java.lang.object";

	/**
	 * DAO主键缓存，用于存储BaseDao实例与其对应的主键字段映射关系。
	 */
	private static final Map<BaseDao, Field> DAO_PK_CACHE = new ConcurrentHashMap<>(50);

	/**
	 * 领域类主键缓存，用于存储领域类与其对应的主键字段映射关系。
	 */
	private static final Map<Class, Field> DOMAIN_PK_CACHE = new ConcurrentHashMap<>(50);

	/**
	 * 无主键类集合，用于存储没有定义主键字段的领域类。
	 */
	private static final Set<Class> NO_PK_SET = new ConcurrentHashSet<>();

	/**
	 * 数据库列名缓存，用于存储属性函数与数据库列名的映射关系。
	 */
	private static final Map<PropertyFunc, String> DB_COLUMN_CACHE = new ConcurrentHashMap<>(50);

	private PersistenceHelper() {
	}

	/**
	 * 获取指定DAO实例的主键字段。
	 * @param object BaseDao实例
	 * @return 主键字段
	 */
	public static Field getPrimaryKey(BaseDao object) {
		Field field = DAO_PK_CACHE.get(object);
		if (field == null) {
			try {
				SimpleJpaRepository clazz = (SimpleJpaRepository) AopTargetUtil.getTarget(object);
				Method method = ReflectUtil.getMethodByName(clazz.getClass(), "getDomainClass");
				method.setAccessible(true);
				Class domainClass = (Class) method.invoke(clazz);
				field = getPrimaryKey(domainClass);
				DAO_PK_CACHE.putIfAbsent(object, field);
			}
			catch (Exception e) {
				throw new IllegalArgumentException(
						String.format("PersistenceHelper do not get primary key(%s).", object.getClass().getName()), e);
			}
		}
		return field;
	}

	/**
	 * 获取指定领域类的主键字段。
	 * @param domainClass 领域类
	 * @return 主键字段
	 */
	public static Field getPrimaryKey(Class<?> domainClass) {
		if (NO_PK_SET.contains(domainClass)) {
			throw new IllegalArgumentException(
					String.format("PersistenceHelper do not get primary key(%s).", domainClass.getName()));
		}
		Field result = DOMAIN_PK_CACHE.get(domainClass);
		if (result == null) {
			List<Field> fields = new LinkedList<>();
			Class<?> loopClass = domainClass;
			while (loopClass != null && !OBJECT.equalsIgnoreCase(loopClass.getName())) {
				fields.addAll(Arrays.stream(loopClass.getDeclaredFields()).toList());
				loopClass = loopClass.getSuperclass();
			}

			for (Field field : fields) {
				field.setAccessible(true);
				boolean isId = Arrays.stream(field.getDeclaredAnnotations())
					.anyMatch(i -> i.annotationType().equals(Id.class));
				if (isId) {
					result = field;
					break;
				}
			}
			if (result == null) {
				NO_PK_SET.add(domainClass);
				throw new IllegalArgumentException(
						String.format("PersistenceHelper do not get primary key(%s).", domainClass.getName()));
			}
			DOMAIN_PK_CACHE.put(domainClass, result);
		}
		return result;
	}

	/**
	 * 获取属性函数对应的数据库列名。
	 * @param fn 属性函数
	 * @return 数据库列名
	 */
	public static <T, R> String getDatabaseColumn(PropertyFunc<T, R> fn) {
		if (DB_COLUMN_CACHE.containsKey(fn)) {
			return DB_COLUMN_CACHE.get(fn);
		}
		String columnName;
		Field field = PojoHelper.getField(fn);
		Column column = field.getAnnotation(Column.class);
		if (column != null && !column.name().isEmpty()) {
			columnName = column.name();
		}
		else {
			columnName = NamingCase.toUnderlineCase(field.getName()).toLowerCase();
		}
		DB_COLUMN_CACHE.put(fn, columnName);
		return columnName;
	}

}
