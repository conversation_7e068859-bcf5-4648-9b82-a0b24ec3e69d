package io.naccoll.boilerplate.cms.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 文章创建命令类
 *
 * 该类用于封装创建文章所需的信息
 *
 * <AUTHOR>
 */
@Data
public class CmsArticleCreateCommand {

	/**
	 * 文章标题
	 */
	@Schema(description = "文章标题")
	private String title;

	@Schema(description = "头图", hidden = true)
	private String headImage;

	/**
	 * 文章状态
	 */
	@Schema(description = "文章状态 0:禁用 1:启用")
	private Integer status;

	/**
	 * 栏目Id
	 */
	@Schema(description = "栏目Id")
	private Long columnId;

	/**
	 * 文章内容
	 */
	@Schema(description = "文章内容")
	private String content;

	/**
	 * 发布时间
	 */
	@Schema(description = "发布时间")
	private Date publishDate;

}
