package io.naccoll.boilerplate.annex.model;

import io.naccoll.boilerplate.core.persistence.model.AbstractEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

import java.io.Serial;
import java.io.Serializable;

/**
 * 通用附件
 *
 * <AUTHOR>
 */
@SQLDelete(sql = "update t_oss_annex set is_deleted = true where id = ?")
@SQLRestriction(value = "is_deleted = false")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_oss_annex")
public class OssAnnexPo extends AbstractEntity<Long> implements Serializable {

	@Serial
	private static final long serialVersionUID = 1774425613918908416L;

	/**
	 * 主键ID
	 */
	@Id
	@Schema(description = "id")
	private Long id;

	/**
	 * 附件名称
	 */
	@Schema(description = "附件名称")
	private String annexName;

	/**
	 * 附件地址
	 */
	@Schema(description = "附件地址")
	private String annexUrl;

	/**
	 * OSS存储类型
	 */
	@Schema(description = "OSS存储类型")
	private String ossStore;

	/**
	 * bucket名称
	 */
	@Schema(description = "bucket名称")
	private String bucketName;

	/**
	 * 附件存储路径
	 */
	@Schema(description = "附件存储路径")
	private String objectName;

	/**
	 * 附件类型
	 */
	@Schema(description = "附件类型")
	private String contentType;

	/**
	 * 文件大小
	 */
	@Schema(description = "文件大小")
	private Long size;

	/**
	 * 附件拓展名
	 */
	@Schema(description = "附件拓展名")
	private String extName;

	/**
	 * 附件签名
	 */
	@Schema(description = "附件签名")
	private String annexSign;

	/**
	 * 备注
	 */
	@Schema(description = "备注")
	private String remark;

}
