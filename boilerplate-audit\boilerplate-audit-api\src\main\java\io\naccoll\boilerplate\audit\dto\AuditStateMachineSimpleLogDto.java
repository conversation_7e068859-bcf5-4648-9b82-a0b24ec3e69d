package io.naccoll.boilerplate.audit.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.naccoll.boilerplate.audit.model.AuditStateMachineLogPo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 状态机日志返回结果
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AuditStateMachineSimpleLogDto extends AuditStateMachineLogPo {

	/**
	 * 修改前的上下文
	 */
	@JsonIgnore
	@Schema(description = "修改前的上下文")
	private String beforeContext;

	/**
	 * 修改后的上下文
	 */
	@JsonIgnore
	@Schema(description = "修改后的上下文")
	private String afterContext;

}
