package io.naccoll.boilerplate.audit.interfaces.api.user;

import io.naccoll.boilerplate.audit.constant.AuditApiConstant;
import io.naccoll.boilerplate.audit.convert.AuditStateMachineLogConvert;
import io.naccoll.boilerplate.audit.dto.*;
import io.naccoll.boilerplate.audit.service.AuditStateMachineLogQueryService;
import io.naccoll.boilerplate.audit.service.StateMachineDelegateService;
import io.naccoll.boilerplate.core.dto.Option;
import io.naccoll.boilerplate.core.exception.ResourceNotFoundException;
import io.naccoll.boilerplate.core.ratelimit.RateLimit;
import io.naccoll.boilerplate.core.ratelimit.RateLimiterMode;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.io.IOException;
import java.util.List;

/**
 * 状态机管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = AuditApiConstant.UserApiV1.STATE_MACHINE)
@Tag(name = "状态机管理")
public class AuditUserApiV1StateMachine {

	/**
	 * 状态机委托服务
	 */
	@Resource
	private StateMachineDelegateService stateMachineDelegateService;

	/**
	 * 状态机日志查询服务
	 */
	@Resource
	private AuditStateMachineLogQueryService auditStateMachineLogQueryService;

	/**
	 * 状态机日志转换器
	 */
	@Resource
	private AuditStateMachineLogConvert auditStateMachineLogConvert;

	/**
	 * 获取所有可用的状态机
	 * @return 可用状态机列表
	 */
	@GetMapping("/all")
	@Operation(summary = "获取可用的状态机")
	public List<String> getAll() {
		return stateMachineDelegateService.getAll();
	}

	/**
	 * 获取状态机的PlantUml图
	 * @param stateMachine 状态机标识
	 * @return PlantUml图形字符串
	 * @throws IOException 如果读取PlantUml图形时发生错误
	 */
	@GetMapping("/{stateMachine}/plantuml")
	@Operation(summary = "获取状态机的PlantUml")
	public String getPlantUml(@PathVariable String stateMachine) throws IOException {
		return stateMachineDelegateService.getImage(stateMachine);
	}

	/**
	 * 获取状态机的可选状态
	 * @param stateMachine 状态机标识
	 * @return 状态选项列表
	 */
	@GetMapping("/{stateMachine}/states")
	@Operation(summary = "获取状态机的可选状态")
	public List<Option> getStates(@PathVariable String stateMachine) {
		return stateMachineDelegateService.getStateMachineServiceById(stateMachine).getStateOptions();
	}

	/**
	 * 获取状态机的可选事件
	 * @param stateMachine 状态机标识
	 * @return 事件选项列表
	 */
	@GetMapping("/{stateMachine}/events")
	@Operation(summary = "获取状态机的可选事件")
	public List<Option> getEvents(@PathVariable String stateMachine) {
		return stateMachineDelegateService.getStateMachineServiceById(stateMachine).getEventOptions();
	}

	/**
	 * 获取状态机的状态转换
	 * @param stateMachine 状态机标识
	 * @return 状态转换列表
	 */
	@GetMapping("/{stateMachine}/transitions")
	@Operation(summary = "获取状态机的状态转换")
	public List<StateMachineTransitionDto> getTransitions(@PathVariable String stateMachine) {
		return stateMachineDelegateService.getStateMachineServiceById(stateMachine).getTransitions(null);
	}

	/**
	 * 获取某个状态的状态转换
	 * @param stateMachine 状态机标识
	 * @param sourceState 源状态
	 * @return 状态转换列表
	 */
	@GetMapping("/{stateMachine}/transitions/{sourceState}/next")
	@Operation(summary = "获取某个状态的状态转换")
	public List<StateMachineTransitionDto> getStateTransition(@PathVariable String stateMachine,
			@PathVariable Integer sourceState) {
		return stateMachineDelegateService.getStateMachineServiceById(stateMachine).getTransitions(sourceState);
	}

	/**
	 * 获取状态机某个目标的日志
	 * @param stateMachine 状态机标识
	 * @param targetId 目标ID
	 * @return 状态机日志列表
	 */
	@GetMapping("/{stateMachine}/{targetId}/log")
	@Operation(summary = "获取状态机某个目标的日志")
	public List<AuditStateMachineSimpleLogDto> getStateLogs(@PathVariable String stateMachine,
			@PathVariable Long targetId) {
		var queryCondition = new AuditStateMachineLogQueryCondition();
		queryCondition.setTargetId(targetId);
		queryCondition.setStateMachine(stateMachine);
		var list = auditStateMachineLogQueryService.findAll(queryCondition);
		return auditStateMachineLogConvert.convertAuditStateMachineSimpleLogDtoList(list);
	}

	/**
	 * 获取状态机某个目标的日志详情
	 * @param stateMachine 状态机标识
	 * @param targetId 目标ID
	 * @param logId 日志ID
	 * @return 日志详情
	 */
	@GetMapping("/{stateMachine}/{targetId}/log/{logId}")
	@Operation(summary = "获取状态机某个目标的日志详情")
	public AuditStateMachineLogDto getStateLogs(@PathVariable String stateMachine, @PathVariable Long targetId,
			@PathVariable Long logId) {
		var entity = auditStateMachineLogQueryService.findByIdNotNull(logId);
		if (!(stateMachine.equals(entity.getStateMachine()) && targetId.equals(entity.getTargetId()))) {
			throw new ResourceNotFoundException("无法查询到指定的状态机日志");
		}
		return auditStateMachineLogConvert.convertAuditStateMachineLogDto(entity);
	}

	/**
	 * 触发状态机事件
	 * @param stateMachine 状态机标识
	 * @param command 事件命令
	 * @return 响应实体
	 */
	@PostMapping("/{stateMachine}/fire-event")
	@Operation(summary = "状态轮转")
	@RateLimit(mode = RateLimiterMode.WINDOWS, rate = 1)
	public ResponseEntity<Void> fireEvent(@PathVariable String stateMachine,
			@RequestBody StateMachineFireEventCommand command) {
		command.setMachineId(stateMachine);
		stateMachineDelegateService.fireEvent(command);
		return ResponseEntity.accepted().build();
	}

}
