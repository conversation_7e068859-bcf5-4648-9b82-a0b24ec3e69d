package io.naccoll.boilerplate.core.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AdvisedSupport;
import org.springframework.aop.framework.AopProxy;
import org.springframework.aop.support.AopUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Proxy;

/**
 * AOP目标工具类，用于获取代理对象的原始目标对象
 * <p>
 * 支持JDK动态代理和CGLIB代理
 *
 * <AUTHOR>
 */
@Slf4j
public class AopTargetUtil {

	private AopTargetUtil() {
	}

	/**
	 * 获取代理对象的原始目标对象
	 * <p>
	 * 如果是非代理对象则直接返回
	 * @param proxy 代理对象
	 * @return 原始目标对象
	 * @throws IllegalArgumentException 如果检查代理对象失败
	 */
	public static Object getTarget(Object proxy) {
		try {
			if (!AopUtils.isAopProxy(proxy)) {
				// 不是代理对象
				return proxy;
			}

			if (AopUtils.isJdkDynamicProxy(proxy)) {
				return getTarget(getJdkDynamicProxyTargetObject(proxy));
			}
			else { // cglib
				return getTarget(getCglibProxyTargetObject(proxy));
			}

		}
		catch (Exception e) {
			log.error("check proxy object fail, ", e);
			throw new IllegalArgumentException("check proxy object fail");
		}
	}

	/**
	 * 获取CGLIB代理的目标对象
	 * @param proxy CGLIB代理对象
	 * @return 原始目标对象
	 * @throws Exception 如果反射操作失败
	 */
	private static Object getCglibProxyTargetObject(Object proxy) throws Exception {
		Field h = proxy.getClass().getDeclaredField("CGLIB$CALLBACK_0");
		h.setAccessible(true);
		Object dynamicAdvisedInterceptor = h.get(proxy);

		Field advised = dynamicAdvisedInterceptor.getClass().getDeclaredField("advised");
		advised.setAccessible(true);

		return ((AdvisedSupport) advised.get(dynamicAdvisedInterceptor)).getTargetSource().getTarget();
	}

	/**
	 * 获取JDK动态代理的目标对象
	 * @param proxy JDK动态代理对象
	 * @return 原始目标对象
	 * @throws Exception 如果反射操作失败
	 */
	private static Object getJdkDynamicProxyTargetObject(Object proxy) throws Exception {
		AopProxy aopProxy = (AopProxy) Proxy.getInvocationHandler(proxy);
		Field advised = aopProxy.getClass().getDeclaredField("advised");
		advised.setAccessible(true);
		AdvisedSupport support = (AdvisedSupport) advised.get(aopProxy);
		return support.getTargetSource().getTarget();
	}

}
