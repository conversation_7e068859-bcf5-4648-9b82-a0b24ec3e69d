package io.naccoll.boilerplate.audit.dto;

import io.swagger.v3.core.converter.ResolvedSchema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 状态机转移数据传输对象
 *
 * 该类用于表示状态机中的状态转移信息，包含事件编码、事件名称、原状态和目标状态等信息
 *
 * <AUTHOR>
 */
@Data
public class StateMachineTransitionDto {

	/**
	 * 事件编码
	 */
	@Schema(description = "事件编码")
	private Integer eventCode;

	/**
	 * 事件名称
	 */
	@Schema(description = "事件名称")
	private String eventName;

	/**
	 * 原状态编码
	 */
	@Schema(description = "原状态编码")
	private Integer fromStateCode;

	/**
	 * 原状态名称
	 */
	@Schema(description = "原状态名称")
	private String fromStateName;

	/**
	 * 目标状态编码
	 */
	@Schema(description = "目标状态编码")
	private Integer targetStateCode;

	/**
	 * 目标状态名称
	 */
	@Schema(description = "目标状态名称")
	private String targetStateName;

	/**
	 * 元数据信息
	 */
	@Schema(description = "元数据信息")
	private String metadata;

	/**
	 * 参数类型列表
	 */
	@Schema(description = "参数类型列表")
	private List<ResolvedSchema> parameterTypes;

}
