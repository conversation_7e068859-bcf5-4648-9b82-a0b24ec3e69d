package io.naccoll.boilerplate.audit.interfaces.eventlisten;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import io.naccoll.boilerplate.audit.config.AuditLogProperties;
import io.naccoll.boilerplate.core.audit.enums.AuditStorageType;
import io.naccoll.boilerplate.core.audit.login.LoginSuccessEvent;
import io.naccoll.boilerplate.core.ip.Ip2RegionService;
import io.naccoll.boilerplate.core.security.authtication.entity.UserDetailsImpl;
import io.naccoll.boilerplate.core.security.enums.Realm;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 审计登录事件SLF4J处理器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@EnableConfigurationProperties(AuditLogProperties.class)
public class AuditLoginEventSlf4jHandler {

	/**
	 * 审计日志配置属性
	 */
	@Resource
	private AuditLogProperties auditLogProperties;

	/**
	 * IP地址解析服务
	 */
	@Resource
	private Ip2RegionService ip2RegionService;

	/**
	 * 处理登录成功事件
	 * @param event 登录成功事件
	 */
	@EventListener
	@Async
	public void onLoginSuccess(LoginSuccessEvent event) {
		AuditLogProperties.Login login = auditLogProperties.getLogin();
		if (login.isEnabled() && login.getType().contains(AuditStorageType.SLF4J)) {
			UserDetailsImpl userDetails = event.getPayload();
			Realm realm = userDetails.getRealm();
			Long userId = userDetails.getId();
			String username = userDetails.getUsername();
			String loginIp = event.getSource().toString();
			String date = DateUtil.format(new Date(), DatePattern.ISO8601_PATTERN);
			String loginAddress = ip2RegionService.getCityInfo(loginIp);
			log.info("REALM=[{}], UserId=[{}], UserName=[{}], LoginIp=[{}], LoginDate=[{}], LoginAddress=[{}]", realm,
					userId, username, loginIp, date, loginAddress);
		}
	}

}
