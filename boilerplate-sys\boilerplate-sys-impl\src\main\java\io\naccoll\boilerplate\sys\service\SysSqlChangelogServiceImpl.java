package io.naccoll.boilerplate.sys.service;

import io.naccoll.boilerplate.core.id.IdService;
import io.naccoll.boilerplate.core.utils.BeanUtils;
import io.naccoll.boilerplate.sys.dao.SysSqlChangelogDao;
import io.naccoll.boilerplate.sys.model.SysSqlChangelogPo;
import io.naccoll.boilerplate.sys.model.SysSqlPo;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
 * 动态SQL集变更日志服务实现
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SysSqlChangelogServiceImpl implements SysSqlChangelogService {

	@Resource
	private SysSqlChangelogDao sysSqlChangelogDao;

	@Resource
	private SysSqlChangelogQueryService sysSqlChangelogQueryService;

	@Resource
	private IdService idService;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public SysSqlChangelogPo create(@Valid SysSqlPo command) {
		SysSqlChangelogPo sysSqlChangelog = new SysSqlChangelogPo();
		BeanUtils.copyProperties(command, sysSqlChangelog);
		sysSqlChangelog.setRelationId(command.getId());
		sysSqlChangelog.setId(idService.getId());
		sysSqlChangelog.setNew(true);
		return sysSqlChangelogDao.save(sysSqlChangelog);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public List<SysSqlChangelogPo> batchCreate(@Valid List<SysSqlPo> commands) {
		List<SysSqlChangelogPo> sysSqlChangelogs = commands.stream().map(command -> {
			SysSqlChangelogPo sysSqlChangelog = new SysSqlChangelogPo();
			BeanUtils.copyProperties(command, sysSqlChangelog);
			sysSqlChangelog.setRelationId(command.getId());
			sysSqlChangelog.setId(idService.getId());
			sysSqlChangelog.setNew(true);
			return sysSqlChangelog;
		}).toList();
		return sysSqlChangelogDao.saveAll(sysSqlChangelogs);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void deleteById(Long id) {
		sysSqlChangelogQueryService.findByIdNotNull(id);
		sysSqlChangelogDao.deleteById(id);
	}

}
