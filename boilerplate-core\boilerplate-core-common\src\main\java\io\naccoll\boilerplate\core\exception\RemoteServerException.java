package io.naccoll.boilerplate.core.exception;

import lombok.Getter;
import lombok.Setter;

/**
 * 远程服务调用异常 <br>
 * 用于第三方系统调用失败时抛出
 *
 * <AUTHOR>
 */
public class RemoteServerException extends BusinessException {

	@Getter
	@Setter
	/**
	 * 第三方系统错误代码 用于存储第三方系统返回的错误代码信息
	 */
	private String thirdErrorCode;

	/**
	 * 构造远程服务异常
	 * @param messageCode 错误消息代码
	 */
	public RemoteServerException(String messageCode) {
		super(messageCode, BusinessError.THIRD_SYSTEM_ERROR);
	}

	/**
	 * 构造远程服务异常（带原始异常）
	 * @param e 原始异常对象，包含详细的异常信息
	 */
	public RemoteServerException(Throwable e) {
		super(e.getMessage(), BusinessError.THIRD_SYSTEM_ERROR, e);
	}

	/**
	 * 构造远程服务异常
	 * @param messageCode 错误消息代码，用于标识具体的错误类型
	 * @param parameters 错误参数，用于替换错误消息中的占位符
	 *
	 */
	public RemoteServerException(String messageCode, Object... parameters) {
		super(messageCode, BusinessError.THIRD_SYSTEM_ERROR, parameters);
	}

	/**
	 * 构造远程服务异常（带原始异常）
	 * @param messageCode 错误消息代码
	 * @param e 原始异常对象，包含详细的异常信息
	 * @param parameters 错误参数，用于替换错误消息中的占位符
	 *
	 */
	public RemoteServerException(String messageCode, Throwable e, Object... parameters) {
		super(messageCode, BusinessError.THIRD_SYSTEM_ERROR, e, parameters);
	}

	/**
	 * 构造远程服务异常
	 * @param messageCode 错误消息代码
	 * @param error 业务错误类型，标识具体的业务错误
	 * @param parameters 错误参数，用于替换错误消息中的占位符
	 *
	 */
	public RemoteServerException(String messageCode, BusinessError error, Object... parameters) {
		super(messageCode, error, null, parameters);
	}

}
