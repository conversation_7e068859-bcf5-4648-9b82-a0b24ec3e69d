package io.naccoll.boilerplate.audit.service;

import io.naccoll.boilerplate.audit.dto.AuditStateMachineLogQueryCondition;
import io.naccoll.boilerplate.audit.model.AuditStateMachineLogPo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 状态机日志查询服务
 *
 * <AUTHOR>
 */
public interface AuditStateMachineLogQueryService {

	/**
	 * 根据条件分页查询状态机日志
	 * @param condition 查询条件
	 * @param pageable 分页参数
	 * @return 符合条件的状态机日志分页结果
	 */
	Page<AuditStateMachineLogPo> page(AuditStateMachineLogQueryCondition condition, Pageable pageable);

	/**
	 * 根据条件查询所有状态机日志
	 * @param condition 查询条件
	 * @return 符合条件的所有状态机日志列表
	 */
	List<AuditStateMachineLogPo> findAll(AuditStateMachineLogQueryCondition condition);

	/**
	 * 根据ID查询单条状态机日志
	 * @param id 状态机日志ID
	 * @return 对应的单条状态机日志
	 */
	AuditStateMachineLogPo findById(Long id);

	/**
	 * 根据ID查询单条状态机日志，若不存在则抛出异常
	 * @param id 状态机日志ID
	 * @return 对应的单条状态机日志
	 */
	AuditStateMachineLogPo findByIdNotNull(Long id);

	/**
	 * 根据ID集合批量查询状态机日志列表
	 * @param ids 状态机日志ID集合
	 * @return 对应的状态机日志列表
	 */
	List<AuditStateMachineLogPo> findByIds(Collection<Long> ids);

	/**
	 * 根据ID集合批量查询状态机日志映射
	 * @param ids 状态机日志ID集合
	 * @return 对应的状态机日志映射（ID为键）
	 */
	Map<Long, AuditStateMachineLogPo> findMapByIds(Collection<Long> ids);

	/**
	 * 根据状态机类型和目标ID集合查询最新的状态机日志映射
	 * @param stateMachine 状态机类型
	 * @param targetIds 目标ID集合
	 * @return 最新的状态机日志映射（目标ID为键）
	 */
	Map<Long, AuditStateMachineLogPo> findLastByStateMachineAndTargetId(String stateMachine,
			Collection<Long> targetIds);

}
