
package io.naccoll.boilerplate.core.statemachine.exporter;

import java.io.BufferedWriter;
import java.io.IOException;
import java.io.Writer;
import java.util.Iterator;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * The type Csv writer.
 *
 * <AUTHOR>
 */
@SuppressWarnings({ "AlibabaConstantFieldShouldBeUpperCase", "AlibabaClassNamingShouldBeCamel" })
public class CSVWriter extends BufferedWriter {

	private static final Pattern escapePattern = Pattern.compile("(\")");

	private static final Pattern specialCharsPattern = Pattern.compile("[,\r\n]");

	private final StringBuffer tmpBuffer = new StringBuffer();

	private boolean newLine = true;

	private boolean newWriter = true;

	/**
	 * Instantiates a new Csv writer.
	 * @param out the out
	 */
	public CSVWriter(Writer out) {
		super(out);
	}

	/**
	 * Instantiates a new Csv writer.
	 * @param out the out
	 * @param sz the sz
	 */
	public CSVWriter(Writer out, int sz) {
		super(out, sz);
	}

	/**
	 * Write fields.
	 * @param fields the fields
	 * @throws IOException the io exception
	 */
	public void writeFields(List<String> fields) throws IOException {
		if (this.newWriter) {
			this.newWriter = false;
		}
		else {
			this.newLine();
		}

		Iterator<String> si = fields.iterator();

		while (si.hasNext()) {
			this.writeField(si.next());
		}

	}

	/**
	 * Write fields.
	 * @param fields the fields
	 * @throws IOException the io exception
	 */
	public void writeFields(String[] fields) throws IOException {
		if (this.newWriter) {
			this.newWriter = false;
		}
		else {
			this.newLine();
		}

		for (int i = 0; i < fields.length; ++i) {
			this.writeField(fields[i]);
		}

	}

	@Override
	public void newLine() throws IOException {
		this.newLine = true;
		super.write("\r\n");
	}

	/**
	 * Write field.
	 * @param field the field
	 * @throws IOException the io exception
	 */
	public void writeField(String field) throws IOException {
		if (this.newLine) {
			this.newLine = false;
		}
		else {
			this.write(44);
		}

		if (field != null && !field.isEmpty()) {
			Matcher matcher = escapePattern.matcher(field);
			if (!matcher.find()) {
				matcher = specialCharsPattern.matcher(field);
				if (matcher.find()) {
					this.write(34);
					this.write(field);
					this.write(34);
				}
				else {
					this.append(field);
				}
			}
			else {
				this.write(34);
				this.tmpBuffer.setLength(0);

				do {
					matcher.appendReplacement(this.tmpBuffer, "\"\"");
				}
				while (matcher.find());

				matcher.appendTail(this.tmpBuffer);
				this.write(this.tmpBuffer.toString());
				this.write(34);
			}
		}
	}

}
