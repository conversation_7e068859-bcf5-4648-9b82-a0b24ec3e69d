package io.naccoll.boilerplate.core.utils.watermark;

import cn.hutool.core.img.ImgUtil;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.font.PDType1Font;
import org.apache.pdfbox.pdmodel.graphics.state.PDExtendedGraphicsState;
import org.apache.pdfbox.util.Matrix;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.stream.Stream;

/**
 * 水印工具类，支持图片和PDF文件添加水印
 * <p>
 * 支持格式：JPG、PNG、PDF
 * </p>
 *
 * <AUTHOR>
 */
public class WatermarkUtil {

	/** 支持的图片格式列表 */
	private static final List<String> IMAGE_EXT = Arrays.asList("jpg", "png", "jpeg");

	/** 支持的PDF格式列表 */
	private static final List<String> PDF_EXT = List.of("pdf");

	/** 所有支持的文件格式列表 */
	private static final List<String> SUPPORT_EXT = Stream.of(IMAGE_EXT, PDF_EXT)
		.flatMap(Collection::stream)
		.distinct()
		.toList();

	/**
	 * 检查是否支持指定格式的文件添加水印
	 * @param fileExt 文件扩展名
	 * @return 是否支持
	 */
	public static boolean support(String fileExt) {
		return SUPPORT_EXT.contains(fileExt);
	}

	/**
	 * 为输入流添加水印文字
	 * @param inputStream 输入流
	 * @param fileExt 文件扩展名
	 * @param args 水印参数
	 * @return 添加水印后的字节数组
	 * @throws IOException 如果处理失败
	 * @throws IllegalArgumentException 如果文件格式不支持
	 */
	public static byte[] addWatermarkText(InputStream inputStream, String fileExt, WatermarkArgs args)
			throws IOException {
		return switch (fileExt) {
			case "jpg", "png", "jpeg" -> processImage(inputStream, fileExt, args);
			case "pdf" -> processPdf(inputStream, fileExt, args);
			default -> throw new IllegalArgumentException("不支持添加水印的类型: " + fileExt);
		};
	}

	/**
	 * 给图片添加水印文字
	 * @param inputStream 图片输入流
	 * @param fileExt 图片格式(jpg/png/jpeg)
	 * @param args 水印参数(包含文字内容、字体、颜色、透明度、旋转角度等)
	 * @return 添加水印后的图片字节数组
	 * @throws IOException 如果图片处理失败
	 */
	public static byte[] processImage(InputStream inputStream, String fileExt, WatermarkArgs args) throws IOException {
		// 源图片
		Image srcImg = ImgUtil.read(inputStream);
		// 原图宽度
		int width = srcImg.getWidth(null);
		// 原图高度
		int height = srcImg.getHeight(null);
		BufferedImage buffImg = new BufferedImage(srcImg.getWidth(null), srcImg.getHeight(null),
				BufferedImage.TYPE_INT_RGB);
		// 得到画笔对象
		Graphics2D g = buffImg.createGraphics();
		// 设置对线段的锯齿状边缘处理
		g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
		g.drawImage(srcImg.getScaledInstance(srcImg.getWidth(null), srcImg.getHeight(null), Image.SCALE_SMOOTH), 0, 0,
				null);
		// 设置水印旋转
		if (null != args.getDegree()) {
			g.rotate(Math.toRadians(args.getDegree()), (double) buffImg.getWidth() / 2,
					(double) buffImg.getHeight() / 2);
		}
		// 设置水印文字颜色
		g.setColor(args.getColor());
		// 设置水印文字Font
		g.setFont(args.getFont());
		// 设置水印文字透明度
		g.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_ATOP, args.getAlpha()));

		int x = -width / 2;

		// 字体长度
		int markWidth = args.getFontSize() * getTextLength(args.getWatermarkText());
		// 字体高度
		int markHeight = args.getFontSize();

		// 循环添加水印
		while (x < width * 1.5) {
			int y = -height / 2;
			while (y < height * 1.5) {
				g.drawString(args.getWatermarkText(), x, y);
				y += markHeight + args.getYMove();
			}
			x += markWidth + args.getXMove();
		}
		// 释放资源
		g.dispose();
		ByteArrayOutputStream os = new ByteArrayOutputStream();
		ImageIO.write(buffImg, fileExt, os);
		return os.toByteArray();
	}

	/**
	 * 计算文本长度(汉字为1:1，英文和数字为2:1)
	 * @param text 输入文本
	 * @return 计算后的长度
	 */
	private static int getTextLength(String text) {
		int length = text.length();
		for (int i = 0; i < text.length(); i++) {
			String s = String.valueOf(text.charAt(i));
			if (s.getBytes().length > 1) {
				length++;
			}
		}
		length = length % 2 == 0 ? length / 2 : length / 2 + 1;
		return length;
	}

	/**
	 * 给PDF文件添加水印
	 * @param inputStream PDF输入流
	 * @param fileExt 文件格式(应为pdf)
	 * @param args 水印参数(包含文字内容、字体大小、颜色、透明度、旋转角度等)
	 * @return 添加水印后的PDF字节数组
	 * @throws IOException 如果PDF处理失败
	 */
	public static byte[] processPdf(InputStream inputStream, String fileExt, WatermarkArgs args) throws IOException {
		try (PDDocument document = PDDocument.load(inputStream)) {
			for (PDPage page : document.getPages()) {
				addWatermarkToPage(document, page, args);
			}
			ByteArrayOutputStream baos = new ByteArrayOutputStream();
			document.save(baos);
			return baos.toByteArray();
		}
	}

	/**
	 * 为PDF页面添加水印
	 * @param document PDF文档对象
	 * @param page PDF页面对象
	 * @param args 水印参数
	 * @throws IOException 如果处理失败
	 */
	private static void addWatermarkToPage(PDDocument document, PDPage page, WatermarkArgs args) throws IOException {
		PDRectangle pageSize = page.getMediaBox();
		float pageWidth = pageSize.getWidth();
		float pageHeight = pageSize.getHeight();

		// 计算单个水印尺寸
		float textWidth = PDType1Font.HELVETICA_BOLD.getStringWidth(args.getWatermarkText()) * args.getFontSize()
				/ 1000f;
		float textHeight = args.getFontSize();

		// 计算行列数量
		int cols = (int) Math.ceil(pageWidth / (textWidth + args.getXMove()));
		int rows = (int) Math.ceil(pageHeight / (textHeight + args.getYMove()));

		try (PDPageContentStream contentStream = new PDPageContentStream(document, page,
				PDPageContentStream.AppendMode.APPEND, true, true)) {

			// 设置透明度
			PDExtendedGraphicsState gs = new PDExtendedGraphicsState();
			gs.setNonStrokingAlphaConstant(args.getAlpha());
			contentStream.setGraphicsStateParameters(gs);

			// 设置字体和颜色
			contentStream.setFont(PDType1Font.HELVETICA_BOLD, args.getFontSize());
			contentStream.setNonStrokingColor(args.getColor());

			// 开始绘制水印
			contentStream.beginText();

			// 遍历所有行列位置
			for (int i = -1; i <= rows; i++) {
				for (int j = -1; j <= cols; j++) {
					float x = j * (textWidth + args.getXMove());
					float y = pageHeight - (i * (textHeight + args.getYMove())) - textHeight;

					// 应用旋转和位置变换
					Matrix matrix = Matrix.getRotateInstance(Math.toRadians(args.getDegree()), x + textWidth / 2,
							y + textHeight / 2);

					contentStream.setTextMatrix(matrix);
					contentStream.newLineAtOffset(x, y);
					contentStream.showText(args.getWatermarkText());
				}
			}

			contentStream.endText();
		}
	}

}
