package io.naccoll.boilerplate.audit.service;

import io.naccoll.boilerplate.audit.dto.AuditStateMachineLogCreateCommand;
import io.naccoll.boilerplate.audit.dto.AuditStateMachineLogUpdateCommand;
import io.naccoll.boilerplate.audit.model.AuditStateMachineLogPo;
import jakarta.validation.Valid;

/**
 * 状态机日志服务
 *
 * <AUTHOR>
 */
public interface AuditStateMachineLogService {

	/**
	 * 创建状态机日志
	 * @param command 创建参数
	 * @return AuditStateMachineLogPo audit state machine log po
	 */
	AuditStateMachineLogPo create(@Valid AuditStateMachineLogCreateCommand command);

	/**
	 * 更新状态机日志
	 * @param command 更新参数
	 * @return AuditStateMachineLogPo audit state machine log po
	 */
	AuditStateMachineLogPo update(@Valid AuditStateMachineLogUpdateCommand command);

	/**
	 * 删除状态机日志
	 * @param id 状态机日志Id
	 */
	void deleteById(Long id);

}
