package io.naccoll.boilerplate.cms.service;

import cn.hutool.http.HtmlUtil;
import io.naccoll.boilerplate.cms.config.ApplicationCmsProperties;
import io.naccoll.boilerplate.cms.dao.CmsArticleDao;
import io.naccoll.boilerplate.cms.dto.CmsArticleCreateCommand;
import io.naccoll.boilerplate.cms.dto.CmsArticleUpdateCommand;
import io.naccoll.boilerplate.cms.model.CmsArticlePo;
import io.naccoll.boilerplate.core.audit.operate.OperateLog;
import io.naccoll.boilerplate.core.id.IdService;
import io.naccoll.boilerplate.core.ratelimit.RateLimit;
import io.naccoll.boilerplate.core.ratelimit.RateLimiterMode;
import io.naccoll.boilerplate.oss.OssServiceHelper;
import jakarta.annotation.Resource;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 文章服务类，提供文章的创建、修改和删除功能
 *
 * <AUTHOR>
 */
@Service
@EnableConfigurationProperties(ApplicationCmsProperties.class)
public class CmsArticleService {

	@Resource
	private CmsArticleDao cmsArticleDao;

	@Resource
	private CmsArticleQueryService cmsArticleQueryService;

	@Resource
	private OssServiceHelper ossServiceHelper;

	@Resource
	private ApplicationCmsProperties applicationCmsProperties;

	@Resource
	private IdService idService;

	/**
	 * 创建文章
	 * @param command 文章创建命令
	 * @return 创建的文章实体
	 */
	@Transactional(rollbackFor = Exception.class)
	@OperateLog(value = "创建内容文章", id = "#result.id", type = "内容文章",
			afterDataAccess = "@cmsArticleQueryService.findById(#result.id)")
	@RateLimit(mode = RateLimiterMode.WINDOWS, rate = 1)
	public CmsArticlePo create(CmsArticleCreateCommand command) {
		CmsArticlePo cmsColumn = new CmsArticlePo();
		cmsColumn.setId(idService.getId());
		cmsColumn.setTitle(command.getTitle());
		cmsColumn.setHeadImage(command.getHeadImage());
		cmsColumn.setColumnId(command.getColumnId());
		cmsColumn.setPublishDate(command.getPublishDate());
		cmsColumn.setStatus(command.getStatus());
		cmsColumn = cmsArticleDao.save(cmsColumn);
		String text = HtmlUtil.cleanHtmlTag(command.getContent());
		if (text.length() > applicationCmsProperties.getUseSummaryLength()) {
			cmsColumn.setSummary(text.substring(0, applicationCmsProperties.getSummaryLength()));
		}
		else {
			cmsColumn.setSummary(text);
		}
		cmsColumn.setContent(ossServiceHelper.uploadPublicHtml("cms/article/content", cmsColumn.getId().toString(),
				command.getContent().getBytes()));
		return cmsArticleDao.save(cmsColumn);
	}

	/**
	 * 更新文章
	 * @param command 文章更新命令
	 * @return 更新后的文章实体
	 */
	@Transactional(rollbackFor = Exception.class)
	@OperateLog(value = "修改内容文章", id = "#command.id", type = "内容文章",
			beforeDataAccess = "@cmsArticleQueryService.findById(#command.id)",
			afterDataAccess = "@cmsArticleQueryService.findById(#result.id)")
	public CmsArticlePo update(CmsArticleUpdateCommand command) {
		CmsArticlePo cmsColumnPo = cmsArticleQueryService.findByIdNotNull(command.getId());
		cmsColumnPo.setTitle(command.getTitle());
		cmsColumnPo.setHeadImage(command.getHeadImage());
		cmsColumnPo.setColumnId(command.getColumnId());
		cmsColumnPo.setContent(command.getContent());
		cmsColumnPo.setPublishDate(command.getPublishDate());
		cmsColumnPo.setStatus(command.getStatus());
		cmsColumnPo = cmsArticleDao.save(cmsColumnPo);

		String text = HtmlUtil.cleanHtmlTag(command.getContent());
		if (text.length() > applicationCmsProperties.getUseSummaryLength()) {
			cmsColumnPo.setSummary(text.substring(0, applicationCmsProperties.getSummaryLength()));
		}
		else {
			cmsColumnPo.setSummary(text);
		}
		cmsColumnPo.setContent(ossServiceHelper.uploadPublicHtml("cms/article/content", cmsColumnPo.getId().toString(),
				command.getContent().getBytes()));
		return cmsArticleDao.save(cmsColumnPo);
	}

	/**
	 * 删除文章
	 * @param id 文章ID
	 */
	@Transactional(rollbackFor = Exception.class)
	@OperateLog(value = "删除内容文章", id = "#id", type = "内容文章", beforeDataAccess = "@cmsArticleQueryService.findById(#id)")
	@RateLimit(mode = RateLimiterMode.WINDOWS, rate = 1)
	public void delete(Long id) {
		cmsArticleQueryService.findByIdNotNull(id);
		cmsArticleDao.deleteById(id);
	}

}
