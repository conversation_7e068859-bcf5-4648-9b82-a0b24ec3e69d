package io.naccoll.boilerplate.audit.service.impl;

import io.naccoll.boilerplate.audit.dao.AuditDataModelDao;
import io.naccoll.boilerplate.audit.dto.AuditDataModelQueryCondition;
import io.naccoll.boilerplate.audit.model.AuditDataModelPo;
import io.naccoll.boilerplate.audit.service.AuditDataModelQueryService;
import io.naccoll.boilerplate.core.exception.ResourceNotFoundException;
import io.naccoll.boilerplate.core.persistence.dao.DaoUtils;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 数据审计模型查询服务实现
 *
 * <AUTHOR>
 */
@Service
public class AuditDataModelQueryServiceImpl implements AuditDataModelQueryService {

	@Resource
	private AuditDataModelDao auditDataModelDao;

	@Override
	public Page<AuditDataModelPo> page(AuditDataModelQueryCondition condition, Pageable pageable) {
		return auditDataModelDao.page(condition, pageable);
	}

	@Override
	public List<AuditDataModelPo> findAll(AuditDataModelQueryCondition condition) {
		return auditDataModelDao.findAll(condition);
	}

	@Override
	public AuditDataModelPo findById(Long id) {
		return auditDataModelDao.findById(id).orElse(null);
	}

	@Override
	public AuditDataModelPo findByModelSign(String modelSign) {
		return auditDataModelDao.findFirstByModelSign(modelSign);
	}

	@Override
	public AuditDataModelPo findByIdNotNull(Long id) {
		return Optional.ofNullable(findById(id)).orElseThrow(() -> new ResourceNotFoundException("数据审计模型不存在"));
	}

	@Override
	public List<AuditDataModelPo> findByIds(Collection<Long> ids) {
		return DaoUtils.findByPrimaryKeyIn(auditDataModelDao, ids);
	}

	@Override
	public Map<Long, AuditDataModelPo> findMapByIds(Collection<Long> ids) {
		return DaoUtils.findMapByPrimaryKeyIn(auditDataModelDao, ids);
	}

}
