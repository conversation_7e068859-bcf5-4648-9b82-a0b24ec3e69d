package io.naccoll.boilerplate.core.security.constants;

/**
 * 安全相关常量类
 * <p>
 * 包含认证授权相关的常量定义
 * </p>
 *
 * <AUTHOR>
 */
public class SecurityContants {

	/**
	 * Token前缀常量
	 * <p>
	 * 用于标识令牌的前缀字符串，通常用于HTTP请求头中
	 * </p>
	 */
	public static final String TOKEN_PREFIX = "Bearer ";

	/**
	 * 认证头字段常量
	 * <p>
	 * 用于HTTP请求头中的认证字段名称
	 * </p>
	 */
	public static final String HEADER_STRING = "Authorization";

	/**
	 * 双重安全验证代码常量
	 * <p>
	 * 用于标识双重安全验证的代码字段
	 * </p>
	 */
	public static final String TWICE_SECURITY_CODE = "TwiceSecurityCode";

	private SecurityContants() {
	}

}
