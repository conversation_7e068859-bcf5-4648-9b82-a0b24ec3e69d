package io.naccoll.boilerplate.annex.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 通用附件创建对象
 *
 * <AUTHOR>
 */
@Data
public class OssAnnexCreateCommand {

	/**
	 * 附件名称
	 */
	@Schema(description = "附件名称")
	private String annexName;

	/**
	 * 是否存储到公开读的桶中
	 */
	@Schema(description = "是否存储到公开读的桶中")
	private Boolean publicBucket = false;

	/**
	 * 备注
	 */
	@Schema(description = "备注")
	private String remark;

	/**
	 * 目标Id
	 */
	@Schema(description = "目标Id")
	private String targetId;

	/**
	 * 目标类型
	 */
	@Schema(description = "目标类型")
	private String targetType;

	/**
	 * 附件分类
	 */
	@Schema(description = "附件分类")
	private String annexGroup;

	/**
	 * 组织id
	 */
	@Schema(description = "组织id")
	private Long organizationId;

	/**
	 * 部门id
	 */
	@Schema(description = "部门id")
	private Long departId;

	/**
	 * 扩展字段1
	 */
	@Schema(description = "扩展字段1")
	private String d1;

	/**
	 * 扩展字段2
	 */
	@Schema(description = "扩展字段2")
	private String d2;

	/**
	 * 扩展字段3
	 */
	@Schema(description = "扩展字段3")
	private String d3;

	/**
	 * 扩展字段4
	 */
	@Schema(description = "扩展字段4")
	private String d4;

	/**
	 * 扩展字段5
	 */
	@Schema(description = "扩展字段5")
	private String d5;

	/**
	 * 扩展字段6
	 */
	@Schema(description = "扩展字段6")
	private String d6;

	/**
	 * 扩展字段7
	 */
	@Schema(description = "扩展字段7")
	private String d7;

	/**
	 * 扩展字段8
	 */
	@Schema(description = "扩展字段8")
	private String d8;

	/**
	 * 扩展字段9
	 */
	@Schema(description = "扩展字段9")
	private String d9;

	/**
	 * 检查目标Id和目标类型是否同时存在
	 * @return boolean 是否存在
	 */
	public boolean hasTarget() {
		return targetId != null && targetType != null;
	}

}
