package io.naccoll.boilerplate.core.statemachine;

import io.naccoll.boilerplate.core.statemachine.impl.TransitionType;

import java.util.List;

/**
 * {@code Transition} is something what a state machine associates with a state changes.
 *
 * @param <S> the type of state
 * @param <E> the type of event
 * @param <C> the type of user defined context, which is used to hold application data
 * <AUTHOR>
 * @date 2020 -02-07 2:20 PM
 */
public interface Transition<S, E, C> {

	/**
	 * Gets the source state of this transition.
	 * @return the source state
	 */
	State<S, E, C> getSource();

	/**
	 * Sets source.
	 * @param state the state
	 */
	void setSource(State<S, E, C> state);

	/**
	 * Gets event.
	 * @return the event
	 */
	E getEvent();

	/**
	 * Sets event.
	 * @param event the event
	 */
	void setEvent(E event);

	/**
	 * Sets type.
	 * @param type the type
	 */
	void setType(TransitionType type);

	/**
	 * Gets the target state of this transition.
	 * @return the target state
	 */
	State<S, E, C> getTarget();

	/**
	 * Sets target.
	 * @param state the state
	 */
	void setTarget(State<S, E, C> state);

	/**
	 * Gets the guard of this transition.
	 * @return the guard
	 */
	Guard<C> getCondition();

	/**
	 * Sets condition.
	 * @param guard the guard
	 */
	void setCondition(Guard<C> guard);

	/**
	 * Gets actions.
	 * @return the actions
	 */
	List<Action<S, E, C>> getActions();

	/**
	 * Sets actions.
	 * @param actions the actions
	 */
	void setActions(Action<S, E, C>... actions);

	/**
	 * Sets action.
	 * @param action the action
	 */
	void setAction(Action<S, E, C> action);

	/**
	 * Do transition source source state target target state.
	 * @param ctx the ctx
	 * @param checkCondition the check condition
	 * @return the target state
	 */
	State<S, E, C> transit(C ctx, boolean checkCondition);

	/**
	 * Verify transition correctness
	 */
	void verify();

}
