package io.naccoll.boilerplate.annex.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 通用附件引用查询条件
 *
 * 该类用于定义查询通用附件引用时的条件参数，包含附件id、目标信息、分类、组织信息等筛选条件。
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OssAnnexRefQueryCondition {

	/**
	 * 附件id
	 */
	@Schema(description = "附件id")
	private List<Long> annexId;

	/**
	 * 附件目标id
	 */
	@Schema(description = "附件目标id")
	private List<String> targetId;

	/**
	 * 附件目标类型
	 */
	@Schema(description = "附件目标类型")
	private String targetType;

	/**
	 * 附件名称
	 */
	@Schema(description = "附件名称")
	private String annexName;

	/**
	 * 附件分类
	 */
	@Schema(description = "附件分类")
	private String annexGroup;

	/**
	 * 部门id
	 */
	@Schema(description = "部门id")
	private Long departId;

	/**
	 * 组织id
	 */
	@Schema(description = "组织id")
	private Long organizationId;

	/**
	 * 是否携带附件信息
	 */
	@Schema(description = "是否携带附件信息")
	private Boolean withAnnex;

	/**
	 * 扩展字段1
	 */
	@Schema(description = "扩展字段1")
	private String d1;

	/**
	 * 扩展字段2
	 */
	@Schema(description = "扩展字段2")
	private String d2;

	/**
	 * 扩展字段3
	 */
	@Schema(description = "扩展字段3")
	private String d3;

	/**
	 * 扩展字段4
	 */
	@Schema(description = "扩展字段4")
	private String d4;

	/**
	 * 扩展字段5
	 */
	@Schema(description = "扩展字段5")
	private String d5;

	/**
	 * 扩展字段6
	 */
	@Schema(description = "扩展字段6")
	private String d6;

	/**
	 * 扩展字段7
	 */
	@Schema(description = "扩展字段7")
	private String d7;

	/**
	 * 扩展字段8
	 */
	@Schema(description = "扩展字段8")
	private String d8;

	/**
	 * 扩展字段9
	 */
	@Schema(description = "扩展字段9")
	private String d9;

}
