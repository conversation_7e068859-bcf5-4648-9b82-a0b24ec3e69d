package io.naccoll.boilerplate.cms.dto;

import io.naccoll.boilerplate.cms.enums.CmsColumnStatus;
import io.naccoll.boilerplate.cms.enums.CmsColumnType;
import io.naccoll.boilerplate.core.validate.IsIdableEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 栏目创建命令
 *
 * <AUTHOR>
 */
@Data
public class CmsColumnCreateCommand {

	/**
	 * 栏目名称
	 */
	@Schema(description = "栏目名称")
	private String name;

	/**
	 * 栏目状态
	 */
	@Schema(description = "栏目状态 0:禁用 1:启用")
	@IsIdableEnum(support = CmsColumnStatus.class)
	@NotNull
	private Integer status;

	/**
	 * 上级栏目Id
	 */
	@Schema(description = "上级栏目Id")
	private Long parentId;

	/**
	 * 栏目描述
	 */
	@Schema(description = "描述")
	private String description;

	/**
	 * 栏目类型
	 */
	@Schema(description = "栏目类型")
	@IsIdableEnum(support = CmsColumnType.class)
	private Integer type;

}
