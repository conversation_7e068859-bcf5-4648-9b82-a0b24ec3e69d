package io.naccoll.boilerplate.audit.service;

import io.naccoll.boilerplate.audit.dao.AuditLoginLogDao;
import io.naccoll.boilerplate.audit.dto.AuditLoginLogQueryCondition;
import io.naccoll.boilerplate.audit.model.AuditLoginLogPo;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

/**
 * 登录日志查询服务
 *
 * <AUTHOR>
 */
@Service
public class AuditLoginLogQueryService {

	@Resource
	private AuditLoginLogDao auditLoginLogDao;

	/**
	 * 分页查询登录日志
	 * @param pageable 分页参数
	 * @param condition 查询条件
	 * @return 登录日志分页结果
	 */
	public Page<AuditLoginLogPo> page(Pageable pageable, AuditLoginLogQueryCondition condition) {
		return auditLoginLogDao.page(pageable, condition);
	}

}
