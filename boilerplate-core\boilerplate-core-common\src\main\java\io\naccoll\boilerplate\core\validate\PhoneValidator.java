package io.naccoll.boilerplate.core.validate;

import cn.hutool.core.util.PhoneUtil;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

/**
 * 手机号验证器实现类
 * <p>
 * 用于验证输入是否为有效的手机号码 支持中国移动、中国联通、中国电信号码验证
 *
 * <AUTHOR>
 * @see Phone
 */
public class PhoneValidator implements ConstraintValidator<Phone, Object> {

	/**
	 * 验证手机号
	 * @param telephone 待验证的手机号
	 * @param constraintValidatorContext 约束验证上下文
	 * @return boolean 验证结果，true表示有效，false表示无效
	 */
	@Override
	public boolean isValid(Object telephone, ConstraintValidatorContext constraintValidatorContext) {
		if (ObjectUtils.isEmpty(telephone) || !StringUtils.hasText(telephone.toString())) {
			return true;
		}
		return PhoneUtil.isPhone(telephone.toString());
	}

}
