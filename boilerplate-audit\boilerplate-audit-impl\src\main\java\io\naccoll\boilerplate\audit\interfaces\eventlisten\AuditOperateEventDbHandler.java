package io.naccoll.boilerplate.audit.interfaces.eventlisten;

import io.naccoll.boilerplate.audit.config.AuditLogProperties;
import io.naccoll.boilerplate.audit.service.AuditOperationLogService;
import io.naccoll.boilerplate.core.audit.enums.AuditStorageType;
import io.naccoll.boilerplate.core.audit.operate.OperateLogEvent;
import jakarta.annotation.Resource;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 审计操作事件数据库处理器
 *
 * <AUTHOR>
 */
@Component
@EnableConfigurationProperties(AuditLogProperties.class)
public class AuditOperateEventDbHandler {

	/**
	 * 审计操作日志服务
	 */
	@Resource
	private AuditOperationLogService auditLogService;

	/**
	 * 审计日志配置属性
	 */
	@Resource
	private AuditLogProperties auditLogProperties;

	/**
	 * 处理操作事件
	 * @param event 操作事件
	 */
	@EventListener
	@Async
	public void onOperate(OperateLogEvent event) {
		AuditLogProperties.Operation operation = auditLogProperties.getOperation();
		if (operation.isEnabled() && operation.getType().contains(AuditStorageType.DB)) {
			auditLogService.add(event.getPayload());
		}
	}

}
