package io.naccoll.boilerplate.cms.service;

import io.naccoll.boilerplate.cms.dao.CmsArticleDao;
import io.naccoll.boilerplate.cms.dto.CmsArticleQueryCondition;
import io.naccoll.boilerplate.cms.model.CmsArticlePo;
import io.naccoll.boilerplate.core.exception.ResourceNotFoundException;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * 文章查询服务类，提供文章的查询功能
 *
 * <AUTHOR>
 */
@Service
public class CmsArticleQueryService {

	@Resource
	private CmsArticleDao cmsArticleDao;

	/**
	 * 根据ID查询文章
	 * @param id 文章ID
	 * @return 文章实体，如果不存在返回null
	 */
	public CmsArticlePo findById(Long id) {
		return cmsArticleDao.findById(id).orElse(null);
	}

	/**
	 * 根据ID查询文章，不存在时抛出异常
	 * @param id 文章ID
	 * @return 文章实体
	 * @throws ResourceNotFoundException 如果文章不存在
	 */
	public CmsArticlePo findByIdNotNull(Long id) {
		return Optional.ofNullable(findById(id)).orElseThrow(() -> new ResourceNotFoundException("文章不存在"));
	}

	/**
	 * 分页查询文章
	 * @param pageable 分页参数
	 * @param condition 查询条件
	 * @return 文章分页结果
	 */
	public Page<CmsArticlePo> page(Pageable pageable, CmsArticleQueryCondition condition) {
		return cmsArticleDao.page(pageable, condition);
	}

}
