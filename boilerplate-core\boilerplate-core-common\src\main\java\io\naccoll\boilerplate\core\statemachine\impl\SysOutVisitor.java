package io.naccoll.boilerplate.core.statemachine.impl;

import io.naccoll.boilerplate.core.statemachine.State;
import io.naccoll.boilerplate.core.statemachine.StateMachine;
import io.naccoll.boilerplate.core.statemachine.Transition;
import io.naccoll.boilerplate.core.statemachine.Visitor;
import lombok.extern.slf4j.Slf4j;

/**
 * SysOutVisitor
 *
 * <AUTHOR>
 * @date 2020 -02-08 8:48 PM
 */
@Slf4j
public class SysOutVisitor implements Visitor {

	@Override
	public String visitOnEntry(StateMachine<?, ?, ?> stateMachine) {
		String entry = "-----StateMachine:" + stateMachine.getMachineId() + "-------";
		log.info(entry);
		return entry;
	}

	@Override
	public String visitOnExit(StateMachine<?, ?, ?> stateMachine) {
		String exit = "------------------------";
		log.info(exit);
		return exit;
	}

	@Override
	public String visitOnEntry(State<?, ?, ?> state) {
		StringBuilder sb = new StringBuilder();
		String stateStr = "State:" + state.getId();
		sb.append(stateStr).append(LF);
		log.info(stateStr);
		for (Transition<?, ?, ?> transition : state.getAllTransitions()) {
			String transitionStr = "    Transition:" + transition;
			sb.append(transitionStr).append(LF);
			log.info(transitionStr);
		}
		return sb.toString();
	}

	@Override
	public String visitOnExit(State<?, ?, ?> visitable) {
		return "";
	}

}
