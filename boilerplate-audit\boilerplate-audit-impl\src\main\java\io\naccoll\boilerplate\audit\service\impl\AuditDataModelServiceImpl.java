package io.naccoll.boilerplate.audit.service.impl;

import io.naccoll.boilerplate.audit.dao.AuditDataModelDao;
import io.naccoll.boilerplate.audit.dto.AuditDataModelCreateCommand;
import io.naccoll.boilerplate.audit.dto.AuditDataModelUpdateCommand;
import io.naccoll.boilerplate.audit.model.AuditDataModelPo;
import io.naccoll.boilerplate.audit.service.AuditDataModelQueryService;
import io.naccoll.boilerplate.audit.service.AuditDataModelService;
import io.naccoll.boilerplate.core.audit.operate.OperateLog;
import io.naccoll.boilerplate.core.id.IdService;
import io.naccoll.boilerplate.core.utils.BeanUtils;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * 数据审计模型服务实现
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AuditDataModelServiceImpl implements AuditDataModelService {

	@Resource
	private AuditDataModelDao auditDataModelDao;

	@Resource
	private AuditDataModelQueryService auditDataModelQueryService;

	@Resource
	private IdService idService;

	@Override
	@OperateLog(value = "新增数据审计模型", id = "#result.id", type = "数据审计模型",
			afterDataAccess = "@auditDataModelQueryServiceImpl.findById(#result.id)")
	@Transactional(rollbackFor = Exception.class)
	public AuditDataModelPo create(@Valid AuditDataModelCreateCommand command) {
		AuditDataModelPo auditDataModel = new AuditDataModelPo();
		BeanUtils.copyProperties(command, auditDataModel);
		auditDataModel.setId(idService.getId());
		return auditDataModelDao.save(auditDataModel);
	}

	@Override
	@OperateLog(value = "修改数据审计模型", id = "#command.id", type = "数据审计模型",
			beforeDataAccess = "@auditDataModelQueryServiceImpl.findById(#command.id)",
			afterDataAccess = "@auditDataModelQueryServiceImpl.findById(#result.id)")
	@Transactional(rollbackFor = Exception.class)
	public AuditDataModelPo update(@Valid AuditDataModelUpdateCommand command) {
		AuditDataModelPo auditDataModel = auditDataModelQueryService.findByIdNotNull(command.getId());
		BeanUtils.copyProperties(command, auditDataModel);
		return auditDataModelDao.save(auditDataModel);
	}

	@Override
	@OperateLog(value = "删除数据审计模型", id = "#id", type = "数据审计模型",
			beforeDataAccess = "@auditDataModelQueryServiceImpl.findById(#id)")
	@Transactional(rollbackFor = Exception.class)
	public void deleteById(Long id) {
		auditDataModelQueryService.findByIdNotNull(id);
		auditDataModelDao.deleteById(id);
	}

}
