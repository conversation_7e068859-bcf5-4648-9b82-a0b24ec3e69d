package io.naccoll.boilerplate.core.audit.operate;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 审计日志注解，建议添加在Service层的方法上 该注解用于记录操作日志，包括操作描述、目标对象信息及数据变更前后对比
 * 注意：该注解不支持在Controller层使用，且在异步方法中需谨慎使用
 *
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface OperateLog {

	/**
	 * 操作描述信息 用于描述当前操作的具体内容，建议使用简洁明了的中文描述
	 * @return 操作描述内容
	 */
	String value() default "";

	/**
	 * 操作目标唯一标识 用于标识当前操作的目标对象，通常是数据库中的主键ID
	 * @return 目标对象的唯一标识
	 */
	String id() default "";

	/**
	 * 操作目标类型描述 用于描述当前操作的目标对象类型，通常为实体类的类名或业务模块名称
	 * @return 目标对象的类型描述
	 */
	String type() default "";

	/**
	 * 变更前数据访问方法表达式 用于定义获取变更前数据的方法表达式，支持Spring EL表达式
	 * @return 用于获取变更前数据的方法表达式
	 */
	String beforeDataAccess() default "";

	/**
	 * 变更后数据访问方法表达式 用于定义获取变更后数据的方法表达式，支持Spring EL表达式
	 * @return 用于获取变更后数据的方法表达式
	 */
	String afterDataAccess() default "";

	/**
	 * 是否启用批量审计模式 当设置为true时，id属性将用作批量数据中每个元素的ID提取表达式
	 * 此时beforeDataAccess和afterDataAccess用于获取批量数据
	 * @return true表示批量模式，false表示单个记录模式
	 */
	boolean batch() default false;

}
