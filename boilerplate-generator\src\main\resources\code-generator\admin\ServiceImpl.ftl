package ${package}.service.impl;

import io.naccoll.boilerplate.core.audit.operate.OperateLog;
import io.naccoll.boilerplate.core.id.IdService;
import io.naccoll.boilerplate.core.ratelimit.RateLimit;
import io.naccoll.boilerplate.core.ratelimit.RateLimiterMode;
import ${package}.dao.${className}Dao;
import ${package}.dto.${className}CreateCommand;
import ${package}.dto.${className}UpdateCommand;
import ${package}.model.${className}Po;
import ${package}.service.${className}QueryService;
import ${package}.service.${className}Service;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import io.naccoll.boilerplate.core.utils.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.util.*;

/**
 * ${apiAlias}服务实现
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ${className}ServiceImpl implements ${className}Service {

	@Resource
	private ${className}Dao ${changeClassName}Dao;

	@Resource
	private ${className}QueryService ${changeClassName}QueryService;

	@Resource
	private IdService idService;

	@Override
	@OperateLog(value = "新增${apiAlias}",id = "#result.id", type = "${apiAlias}", afterDataAccess = "@${changeClassName}QueryServiceImpl.findById(#result.id)")
	@Transactional(rollbackFor = Exception.class)
	@RateLimit(mode = RateLimiterMode.WINDOWS, rate = 1)
	public ${className}Po create(@Valid ${className}CreateCommand command) {
		return batchCreate(List.of(command)).getFirst();
	}

	@Override
	@OperateLog(value = "批量新增${apiAlias}", id = "id", type = "${apiAlias}", batch = true, afterDataAccess = "@${changeClassName}QueryServiceImpl.findByIds(#result.![id])")
	@Transactional(rollbackFor = Exception.class)
    @RateLimit(mode = RateLimiterMode.WINDOWS, rate = 1)
	public List<${className}Po> batchCreate(List<${className}CreateCommand> commands) {
		if (CollectionUtils.isEmpty(commands)) {
			return List.of();
		}
		List<${className}Po> entityList = new ArrayList<>(commands.size());
		for (${className}CreateCommand command : commands) {
			${className}Po ${changeClassName} = BeanUtils.copyProperties(command, ${className}Po::new);
			${changeClassName}.setId(idService.getId());
			entityList.add(${changeClassName});
		}
		return ${changeClassName}Dao.saveAll(entityList);
	}

	@Override
	@OperateLog(value = "修改${apiAlias}", id = "#command.id", type = "${apiAlias}",
				beforeDataAccess = "@${changeClassName}QueryServiceImpl.findById(#command.id)",
				afterDataAccess = "@${changeClassName}QueryServiceImpl.findById(#result.id)")
	@Transactional(rollbackFor = Exception.class)
	public ${className}Po update(@Valid ${className}UpdateCommand command) {
		return batchUpdate(List.of(command)).getFirst();
	}

	@Override
	@OperateLog(value = "批量修改${apiAlias}", id = "id", type = "${apiAlias}", batch = true,
		beforeDataAccess = "@${changeClassName}QueryServiceImpl.findByIds(#commands.![id])",
		afterDataAccess = "@${changeClassName}QueryServiceImpl.findByIds(#commands.![id])")
	@Transactional(rollbackFor = Exception.class)
	public List<${className}Po> batchUpdate(List<${className}UpdateCommand> commands) {
		if (CollectionUtils.isEmpty(commands)) {
			return List.of();
		}
		var ids = commands.stream().map(${className}UpdateCommand::getId).distinct().toList();
		var ${changeClassName}Map = ${changeClassName}QueryService.findMapByIds(ids);
		List<${className}Po> entityList = new ArrayList<>(commands.size());
		for (${className}UpdateCommand command : commands) {
			${className}Po ${changeClassName} = ${changeClassName}Map.get(command.getId());
			BeanUtils.copyProperties(command, ${changeClassName});
			entityList.add(${changeClassName});
		}
		return ${changeClassName}Dao.saveAll(entityList);
	}

	@Override
	@OperateLog(value = "删除${apiAlias}", id = "#id", type = "${apiAlias}", beforeDataAccess = "@${changeClassName}QueryServiceImpl.findById(#id)")
	@Transactional(rollbackFor = Exception.class)
	@RateLimit(mode = RateLimiterMode.WINDOWS, rate = 1)
	public void deleteById(${pkColumnType} id) {
		batchDelete(List.of(id));
	}

	@Override
	@OperateLog(value = "批量删除${apiAlias}", id = "id", type = "${apiAlias}", batch = true,
		beforeDataAccess = "@${changeClassName}QueryServiceImpl.findByIds(#ids)",
		afterDataAccess = "@${changeClassName}QueryServiceImpl.findByIds(#ids)")
	@Transactional(rollbackFor = Exception.class)
	public void batchDelete(Collection<${pkColumnType}> ids) {
		if (CollectionUtils.isEmpty(ids)) {
			return;
		}
		List<${className}Po> list = ${changeClassName}QueryService.findByIds(ids);
		${changeClassName}Dao.deleteAll(list);
	}

}
