package io.naccoll.boilerplate.annex.service;

import io.naccoll.boilerplate.annex.dto.OssAnnexRefCreateCommand;
import io.naccoll.boilerplate.annex.model.OssAnnexRefPo;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 通用附件引用服务
 *
 * <AUTHOR>
 */
public interface OssAnnexRefService {

	/**
	 * 创建通用附件引用
	 * @param command 创建参数
	 * @return 通用附件引用对象
	 */
	OssAnnexRefPo save(@Valid OssAnnexRefCreateCommand command);

	/**
	 * 全量保存附件引用关系（未设置的annexId会被清空）
	 * @param targetType 目标类型
	 * @param targetId 目标ID
	 * @param annexGroup 附件分组
	 * @param commands 创建引用参数列表
	 * @return 通用附件引用列表
	 */
	List<OssAnnexRefPo> deleteAndSave(String targetType, String targetId, String annexGroup,
			@Valid List<OssAnnexRefCreateCommand> commands);

	/**
	 * 删除通用附件引用
	 * @param id 通用附件引用ID
	 */
	void deleteById(Long id);

}
