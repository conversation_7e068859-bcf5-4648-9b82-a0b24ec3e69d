package io.naccoll.boilerplate.sys.service;

import io.naccoll.boilerplate.core.exception.ClientException;
import io.naccoll.boilerplate.core.persistence.dao.DaoUtils;
import io.naccoll.boilerplate.sys.dao.SysPermissionDao;
import io.naccoll.boilerplate.sys.dao.SysRolePermissionDao;
import io.naccoll.boilerplate.sys.dao.SysUserRoleDao;
import io.naccoll.boilerplate.sys.dto.SysPermissionQueryCondition;
import io.naccoll.boilerplate.sys.enums.PermissionLevel;
import io.naccoll.boilerplate.sys.model.SysPermissionPo;
import io.naccoll.boilerplate.sys.model.SysRolePermissionPo;
import io.naccoll.boilerplate.sys.model.SysRolePo;
import io.naccoll.boilerplate.sys.model.SysUserRolePo;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

/**
 * The type Sys permission query service.
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@Validated
public class SysPermissionQueryServiceImpl implements SysPermissionQueryService {

	@Resource
	private SysPermissionDao sysPermissionDao;

	@Resource
	private SysRolePermissionDao sysRolePermissionDao;

	@Resource
	private SysUserRoleDao sysUserRoleDao;

	@Resource
	private SysRoleQueryService sysRoleQueryService;

	@Resource
	private SysPermissionCacheService sysPermissionCacheService;

	@Override
	public List<SysPermissionPo> findAll() {
		return sysPermissionDao.findAll();
	}

	@Override
	public SysPermissionPo findById(Long id) {
		if (id == null || id <= 0) {
			return null;
		}
		return sysPermissionDao.findById(id).orElse(null);
	}

	@Override
	public List<SysPermissionPo> findByIds(Collection<Long> ids) {
		return DaoUtils.findByPrimaryKeyIn(sysPermissionDao, ids);
	}

	@Override
	public SysPermissionPo findByIdNotNull(Long id) {
		return Optional.ofNullable(findById(id))
			.orElseThrow(() -> new ClientException("error.permission.not.exists", id));
	}

	@Override
	public Page<SysPermissionPo> pageByCondition(Pageable pageable, @Valid SysPermissionQueryCondition condition) {
		return sysPermissionDao.pageByCondition(condition, pageable);
	}

	@Override
	public List<SysPermissionPo> findByRoleId(long roleId) {
		Set<Long> permissionIds = sysRolePermissionDao.findAllByRoleId(roleId)
			.stream()
			.map(SysRolePermissionPo::getPermissionId)
			.collect(Collectors.toSet());
		return findByIds(permissionIds);
	}

	@Override
	public List<SysPermissionPo> findAllByRoleNoAssign(long roleId) {
		SysRolePo sysRolePo = sysRoleQueryService.findByIdNotNull(roleId);
		Set<Long> permissionIds = sysRolePermissionDao.findAllByRoleId(roleId)
			.stream()
			.map(SysRolePermissionPo::getPermissionId)
			.collect(Collectors.toSet());
		return DaoUtils.findByPrimaryKeyNotIn(sysPermissionDao, permissionIds)
			.stream()
			.filter(p -> p.getLevel().equals(sysRolePo.getLevel()) && p.getOrigin().equals(sysRolePo.getOrigin()))
			.toList();
	}

	@Override
	public List<SysPermissionPo> findByUserId(Long userId, PermissionLevel resourceType, Long resourceId) {
		Set<Long> roleIds = sysUserRoleDao.findAllByUserId(userId)
			.stream()
			.filter(i -> Objects.equals(i.getLevel(), resourceType.getId())
					&& Objects.equals(i.getResourceId(), resourceId))
			.map(SysUserRolePo::getRoleId)
			.collect(Collectors.toSet());

		if (CollectionUtils.isEmpty(roleIds)) {
			return new LinkedList<>();
		}

		List<SysRolePermissionPo> roles = roleIds.size() == 1
				? sysRolePermissionDao.findAllByRoleId(new LinkedList<>(roleIds).getFirst())
				: sysRolePermissionDao.findAllByRoleIdIn(roleIds);

		Set<Long> permissionIds = roles.stream().map(SysRolePermissionPo::getPermissionId).collect(Collectors.toSet());
		if (CollectionUtils.isEmpty(permissionIds)) {
			return new LinkedList<>();
		}
		return findByIds(permissionIds);
	}

	@Override
	public SysPermissionPo findByIdentity(String identity) {
		return sysPermissionDao.findFirstByIdentity(identity);
	}

	@Override
	public boolean hasPermission(Long userId, PermissionLevel resourceType, Long resourceId, String permit) {
		Set<String> identities = getUserPermission(userId, resourceType, resourceId);
		return identities.contains(permit);
	}

	@Override
	public boolean hasAnyPermission(Long userId, PermissionLevel resourceType, Long resourceId,
			Collection<String> permits) {
		Set<String> identities = getUserPermission(userId, resourceType, resourceId);
		return permits.stream().anyMatch(identities::contains);
	}

	@Override
	public List<SysPermissionPo> findByIdentities(Collection<String> identities) {
		return sysPermissionDao.findByIdentityIn(identities);
	}

	private Set<String> getUserPermission(Long userId, PermissionLevel resourceType, Long resourceId) {
		Set<String> identities = sysPermissionCacheService.getHasPermissionCache(userId, resourceType, resourceId);
		if (identities == null) {
			identities = findByUserId(userId, resourceType, resourceId).stream()
				.map(SysPermissionPo::getIdentity)
				.collect(Collectors.toSet());
			sysPermissionCacheService.setHasPermissionCache(userId, resourceType, resourceId, identities);
		}
		return identities;
	}

}
