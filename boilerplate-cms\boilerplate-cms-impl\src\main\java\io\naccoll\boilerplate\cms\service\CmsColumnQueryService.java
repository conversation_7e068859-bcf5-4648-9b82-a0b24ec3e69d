package io.naccoll.boilerplate.cms.service;

import io.naccoll.boilerplate.cms.dao.CmsColumnDao;
import io.naccoll.boilerplate.cms.dto.CmsColumnQueryCondition;
import io.naccoll.boilerplate.cms.enums.CmsColumnStatus;
import io.naccoll.boilerplate.cms.model.CmsColumnPo;
import io.naccoll.boilerplate.core.exception.ResourceNotFoundException;
import io.naccoll.boilerplate.core.persistence.dao.DaoUtils;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 栏目查询服务类，提供栏目的查询功能
 *
 * <AUTHOR>
 */
@Service
public class CmsColumnQueryService {

	@Resource
	private CmsColumnDao cmsColumnDao;

	/**
	 * 根据ID查询栏目
	 * @param id 栏目ID
	 * @return 栏目实体，如果不存在抛出ResourceNotFoundException
	 * @throws ResourceNotFoundException 如果栏目不存在
	 */
	public CmsColumnPo findById(Long id) {
		return cmsColumnDao.findById(id).orElseThrow(() -> new ResourceNotFoundException("栏目不存在"));
	}

	/**
	 * 根据ID查询栏目，不存在时抛出异常
	 * @param id 栏目ID
	 * @return 栏目实体
	 * @throws ResourceNotFoundException 如果栏目不存在
	 */
	public CmsColumnPo findByIdNotNull(Long id) {
		return Optional.ofNullable(findById(id)).orElseThrow(() -> new ResourceNotFoundException("栏目不存在"));
	}

	/**
	 * 查询所有栏目
	 * @return 栏目列表
	 */
	public List<CmsColumnPo> findAll() {
		return cmsColumnDao.findAll();
	}

	/**
	 * 根据栏目类型查询栏目
	 * @param columnType 栏目类型
	 * @return 栏目列表
	 */
	public List<CmsColumnPo> findByColumnType(Integer columnType) {
		if (columnType == null) {
			return findAll();
		}
		return findAll().stream().filter(i -> i.getType().equals(columnType)).toList();
	}

	/**
	 * 根据查询条件查询栏目
	 * @param condition 查询条件
	 * @return 栏目列表
	 */
	public List<CmsColumnPo> findByCondition(CmsColumnQueryCondition condition) {
		return cmsColumnDao.findAll(condition);
	}

	/**
	 * 查询所有启用的栏目
	 * @return 启用的栏目列表
	 */
	public List<CmsColumnPo> findAllEnable() {
		return cmsColumnDao.findAll()
			.stream()
			.filter(i -> Objects.equals(CmsColumnStatus.ENABLE.getId(), i.getStatus()))
			.toList();
	}

	/**
	 * 根据ID列表查询栏目并返回Map
	 * @param ids 栏目ID列表
	 * @return 栏目ID与实体的Map映射
	 */
	public Map<Long, CmsColumnPo> findMapByIds(Collection<Long> ids) {
		return DaoUtils.findMapByPrimaryKeyIn(cmsColumnDao, ids);
	}

}
