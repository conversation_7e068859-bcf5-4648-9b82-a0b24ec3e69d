package ${package}.interfaces.api.platform;

import ${package}.convert.${className}Convert;
import ${package}.dto.${className}CreateCommand;
import ${package}.dto.${className}Dto;
import ${package}.dto.${className}QueryCondition;
import ${package}.dto.${className}UpdateCommand;
import ${package}.model.${className}Po;
import ${package}.service.${className}QueryService;
import ${package}.service.${className}Service;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;

/**
 * ${apiAlias}管理
 *
 * <AUTHOR>
 */
@RestController
@Tag(name = "${apiAlias}管理")
@RequestMapping("/${apiModule}/platform/api/v1/${permissionClassName}")
public class ${apiModuleU}PlatformApiV1${classNameWithoutModule} {

	@Resource
	private ${className}Service ${changeClassName}Service;

	@Resource
	private ${className}QueryService ${changeClassName}QueryService;

	@Resource
	private ${className}Convert ${changeClassName}Convert;

	/**
	 * 分页查询${apiAlias}
	 */
	@GetMapping("/page")
	@Operation(summary = "分页查询${apiAlias}")
    <#if hasPermission>
    @PreAuthorize("hasPermission(0L,'GLOBAL','${apiModule}/${permissionClassName}:read')")
    <#else>
    // @PreAuthorize("hasPermission(0L,'GLOBAL','${apiModule}/${permissionClassName}:read')")
    </#if>
	public ResponseEntity<Page<${className}Dto>> page(${className}QueryCondition condition, Pageable pageable) {
		Page<${className}Po> ${changeClassName}Page = ${changeClassName}QueryService.page(condition, pageable);
		Page<${className}Dto> ${changeClassName}DtoPage = ${changeClassName}Convert.convert${className}DtoPage(${changeClassName}Page);
		return new ResponseEntity<>(${changeClassName}DtoPage, HttpStatus.OK);
	}

	/**
	 * 查询${apiAlias}
	 */
	@GetMapping("/{id}")
	@Operation(summary = "查询${apiAlias}")
    <#if hasPermission>
	@PreAuthorize("hasPermission(0L,'GLOBAL','${apiModule}/${permissionClassName}:read')")
    <#else>
	// @PreAuthorize("hasPermission(0L,'GLOBAL','${apiModule}/${permissionClassName}:read')")
    </#if>
	public ResponseEntity<${className}Dto> queryOne(@PathVariable Long id) {
		${className}Po ${changeClassName}Po = ${changeClassName}QueryService.findByIdNotNull(id);
		${className}Dto ${changeClassName}Dto = ${changeClassName}Convert.convert${className}Dto(${changeClassName}Po);
		return new ResponseEntity<>(${changeClassName}Dto, HttpStatus.OK);
	}

	/**
	 * 新增${apiAlias}
	 */
	@PostMapping
	@Operation(summary = "新增${apiAlias}")
    <#if hasPermission>
	@PreAuthorize("hasPermission(0L,'GLOBAL','${apiModule}/${permissionClassName}:add')")
    <#else>
    // @PreAuthorize("hasPermission(0L,'GLOBAL','${apiModule}/${permissionClassName}:add')")
    </#if>
	public ResponseEntity<${className}Dto> create${className}(@RequestBody ${className}CreateCommand command) {
		${className}Po ${changeClassName}Po = ${changeClassName}Service.create(command);
		${className}Dto ${changeClassName}Dto = ${changeClassName}Convert.convert${className}Dto(${changeClassName}Po);
		return new ResponseEntity<>(${changeClassName}Dto, HttpStatus.CREATED);
	}

    /**
    * 批量新增${apiAlias}
    */
    @PostMapping("/batch")
    @Operation(summary = "批量新增${apiAlias}")
    <#if hasPermission>
    @PreAuthorize("hasPermission(0L,'GLOBAL','${apiModule}/${permissionClassName}:add')")
    <#else>
    // @PreAuthorize("hasPermission(0L,'GLOBAL','${apiModule}/${permissionClassName}:add')")
    </#if>
    public ResponseEntity<List<${className}Dto>> batchCreate${className}(@RequestBody List<${className}CreateCommand> commands) {
        var ${changeClassName}List = ${changeClassName}Service.batchCreate(commands);
        var ${changeClassName}Dtos = ${changeClassName}Convert.convert${className}DtoList(${changeClassName}List);
        return new ResponseEntity<>(${changeClassName}Dtos, HttpStatus.CREATED);
    }

	/**
	 * 修改${apiAlias}
	 */
	@PutMapping
	@Operation(summary = "修改${apiAlias}")
    <#if hasPermission>
	@PreAuthorize("hasPermission(0L,'GLOBAL','${apiModule}/${permissionClassName}:edit')")
    <#else>
    // @PreAuthorize("hasPermission(0L,'GLOBAL','${apiModule}/${permissionClassName}:edit')")
    </#if>
	public ResponseEntity<${className}Dto> update${className}(@RequestBody ${className}UpdateCommand command) {
		${className}Po ${changeClassName}Po = ${changeClassName}Service.update(command);
		${className}Dto ${changeClassName}Dto = ${changeClassName}Convert.convert${className}Dto(${changeClassName}Po);
		return new ResponseEntity<>(${changeClassName}Dto, HttpStatus.OK);
	}

    /**
    * 批量修改${apiAlias}
    */
    @PutMapping("/batch")
    @Operation(summary = "批量修改${apiAlias}")
    <#if hasPermission>
    @PreAuthorize("hasPermission(0L,'GLOBAL','${apiModule}/${permissionClassName}:edit')")
    <#else>
    // @PreAuthorize("hasPermission(0L,'GLOBAL','${apiModule}/${permissionClassName}:edit')")
    </#if>
    public ResponseEntity<List<${className}Dto>> batchUpdate${className}(@RequestBody List<${className}UpdateCommand> commands) {
        var ${changeClassName}List = ${changeClassName}Service.batchUpdate(commands);
        var ${changeClassName}Dtos = ${changeClassName}Convert.convert${className}DtoList(${changeClassName}List);
        return new ResponseEntity<>(${changeClassName}Dtos, HttpStatus.OK);
    }

	/**
	 * 删除${apiAlias}
	 */
	@DeleteMapping("/{id}")
	@Operation(summary = "删除${apiAlias}")
    <#if hasPermission>
	@PreAuthorize("hasPermission(0L,'GLOBAL','${apiModule}/${permissionClassName}:del')")
    <#else>
    // @PreAuthorize("hasPermission(0L,'GLOBAL','${apiModule}/${permissionClassName}:del')")
    </#if>
	public ResponseEntity<Void> delete${className}(@PathVariable ${pkColumnType} id) {
		${changeClassName}Service.deleteById(id);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	/**
	 * 批量删除${apiAlias}
	 */
	@DeleteMapping("/batch")
	@Operation(summary = "批量删除${apiAlias}")
    <#if hasPermission>
	@PreAuthorize("hasPermission(0L,'GLOBAL','${apiModule}/${permissionClassName}:del')")
    <#else>
    // @PreAuthorize("hasPermission(0L,'GLOBAL','${apiModule}/${permissionClassName}:del')")
    </#if>
	public ResponseEntity<Void> batchDelete${className}(@RequestParam Collection<${pkColumnType}> ids) {
		${changeClassName}Service.batchDelete(ids);
		return new ResponseEntity<>(HttpStatus.OK);
	}

}
