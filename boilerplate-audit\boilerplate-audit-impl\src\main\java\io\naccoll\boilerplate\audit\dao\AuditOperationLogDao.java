package io.naccoll.boilerplate.audit.dao;

import io.naccoll.boilerplate.audit.dto.AuditOperationLogQueryCondition;
import io.naccoll.boilerplate.audit.model.AuditOperationLogPo;
import io.naccoll.boilerplate.core.persistence.dao.BaseDao;
import io.naccoll.boilerplate.core.persistence.jpa.specification.Specifications;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.util.StringUtils;

/**
 * 审计操作日志数据访问接口。
 *
 * <AUTHOR>
 */
@SuppressWarnings("AlibabaAbstractMethodOrInterfaceMethodMustUseJavadoc")
public interface AuditOperationLogDao extends BaseDao<AuditOperationLogPo, Long> {

	/**
	 * 根据查询条件分页获取审计操作日志。
	 * @param condition 查询条件
	 * @param pageable 分页参数
	 * @return 分页的审计操作日志列表
	 */
	default Page<AuditOperationLogPo> page(AuditOperationLogQueryCondition condition, Pageable pageable) {
		Specifications<AuditOperationLogPo> spec = Specifications.builder(AuditOperationLogPo.class)
			.eq(condition.getUserId() != null, AuditOperationLogPo::getUserId, condition.getUserId())
			.contain(StringUtils.hasText(condition.getUsername()), AuditOperationLogPo::getUsername,
					condition.getUsername())
			.contain(StringUtils.hasText(condition.getName()), AuditOperationLogPo::getName, condition.getName())
			.eq(StringUtils.hasText(condition.getTargetId()), AuditOperationLogPo::getTargetId, condition.getTargetId())
			.eq(StringUtils.hasText(condition.getClientIp()), AuditOperationLogPo::getClientIp, condition.getClientIp())
			.eq(StringUtils.hasText(condition.getRealm()), AuditOperationLogPo::getRealm, condition.getRealm())
			.ge(condition.getStartDate() != null, AuditOperationLogPo::getCreatedDate, condition.getStartDate())
			.le(condition.getEndDate() != null, AuditOperationLogPo::getCreatedDate, condition.getEndDate());
		return findAll(spec, pageable);
	}

}
