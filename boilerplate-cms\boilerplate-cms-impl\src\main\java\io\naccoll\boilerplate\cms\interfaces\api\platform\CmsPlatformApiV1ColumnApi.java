package io.naccoll.boilerplate.cms.interfaces.api.platform;

import io.naccoll.boilerplate.cms.constant.CmsApiConstant;
import io.naccoll.boilerplate.cms.convert.CmsConvert;
import io.naccoll.boilerplate.cms.dto.CmsColumnCreateCommand;
import io.naccoll.boilerplate.cms.dto.CmsColumnDto;
import io.naccoll.boilerplate.cms.dto.CmsColumnQueryCondition;
import io.naccoll.boilerplate.cms.dto.CmsColumnUpdateCommand;
import io.naccoll.boilerplate.cms.service.CmsColumnQueryService;
import io.naccoll.boilerplate.cms.service.CmsColumnService;
import io.naccoll.boilerplate.core.constant.MediaTypeConstant;
import io.naccoll.boilerplate.core.dto.Option;
import io.naccoll.boilerplate.core.exception.ClientException;
import io.naccoll.boilerplate.oss.OssServiceHelper;
import io.naccoll.boilerplate.sys.service.SysVariableQueryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 栏目管理平台API接口
 *
 * <AUTHOR>
 */
@Tag(name = "栏目管理")
@RestController
@RequestMapping(CmsApiConstant.PlatformApiV1.COLUMN)
public class CmsPlatformApiV1ColumnApi {

	@Resource
	private CmsColumnService cmsColumnService;

	@Resource
	private CmsColumnQueryService cmsColumnQueryService;

	@Resource
	private CmsConvert cmsConvert;

	@Resource
	private OssServiceHelper ossServiceHelper;

	@Resource
	private SysVariableQueryService sysVariableQueryService;

	/**
	 * 获取所有栏目类型
	 * @return 栏目类型选项列表
	 */
	@PreAuthorize("hasPermission(0L,'GLOBAL','cms/column:read')")
	@Operation(summary = "获取所有栏目类型")
	@GetMapping("/column-types")
	public List<Option> getAllColumnType() {
		return sysVariableQueryService.listEnableByTypeCode("cms-column-type")
			.stream()
			.map(i -> Option.create(i.getCode(), i.getName()))
			.toList();
	}

	/**
	 * 根据栏目ID获取单个栏目信息
	 * @param columnId 栏目ID
	 * @return 栏目DTO
	 */
	@PreAuthorize("hasPermission(0L,'GLOBAL','cms/column:read')")
	@Operation(summary = "查询单个栏目")
	@GetMapping("/{columnId}")
	public CmsColumnDto getOneColumn(@PathVariable Long columnId) {
		return cmsConvert.convertCmsColumnDto(cmsColumnQueryService.findByIdNotNull(columnId));
	}

	/**
	 * 创建新的栏目
	 * @param command 栏目创建命令
	 * @return 新创建的栏目DTO
	 */
	@PreAuthorize("hasPermission(0L,'GLOBAL','cms/column:add')")
	@Operation(summary = "创建栏目")
	@PostMapping("")
	public CmsColumnDto create(@RequestBody CmsColumnCreateCommand command) {
		return cmsConvert.convertCmsColumnDto(cmsColumnService.create(command));
	}

	/**
	 * 更新现有栏目信息
	 * @param command 栏目更新命令
	 * @return 更新后的栏目DTO
	 */
	@PreAuthorize("hasPermission(0L,'GLOBAL','cms/column:edit')")
	@Operation(summary = "修改栏目")
	@PutMapping("")
	public CmsColumnDto update(@RequestBody CmsColumnUpdateCommand command) {
		return cmsConvert.convertCmsColumnDto(cmsColumnService.update(command));
	}

	/**
	 * 更新栏目Logo
	 * @param id 栏目ID
	 * @param logo 新的Logo文件
	 * @return 更新后的栏目DTO
	 */
	@PreAuthorize("hasPermission(0L,'GLOBAL','cms/column:edit')")
	@Operation(summary = "变更栏目logo")
	@PutMapping("/{id}/logo")
	public CmsColumnDto updateColumnLogo(@PathVariable Long id, @RequestPart MultipartFile logo) {
		if (logo == null || logo.getContentType() == null) {
			throw new ClientException("请上传LOGO");
		}
		if (!MediaType.parseMediaType(logo.getContentType()).isCompatibleWith(MediaTypeConstant.IMAGE)) {
			throw new ClientException("请上传图片的文件");
		}
		String logoUrl = ossServiceHelper.uploadPublicMultipartFile("cmslogo/" + id, logo);
		return cmsConvert.convertCmsColumnDto(cmsColumnService.updateLogo(id, logoUrl));
	}

	/**
	 * 根据查询条件获取栏目列表
	 * @param condition 查询条件
	 * @return 栏目DTO列表
	 */
	@PreAuthorize("hasPermission(0L,'GLOBAL','cms/column:read')")
	@Operation(summary = "获取栏目列表")
	@GetMapping("/list")
	public List<CmsColumnDto> getAllColumn(CmsColumnQueryCondition condition) {
		return cmsConvert.convertCmsColumnDtoTree(cmsColumnQueryService.findByCondition(condition));
	}

	/**
	 * 获取所有可用的栏目
	 * @return 栏目DTO列表
	 */
	@PreAuthorize("hasPermission(0L,'GLOBAL','cms/column:read')")
	@Operation(summary = "获取可用的栏目")
	@GetMapping("/all-enable")
	public List<CmsColumnDto> getAllColumnEnable() {
		return cmsConvert.convertCmsColumnDtoTree(cmsColumnQueryService.findAllEnable());
	}

	/**
	 * 删除指定的栏目
	 * @param id 栏目ID
	 * @return 响应状态
	 */
	@PreAuthorize("hasPermission(0L,'GLOBAL','cms/column:del')")
	@Operation(summary = "删除栏目")
	@DeleteMapping("/{id}")
	public ResponseEntity<Void> delete(@PathVariable Long id) {
		cmsColumnService.deleteById(id);
		return ResponseEntity.noContent().build();
	}

}
