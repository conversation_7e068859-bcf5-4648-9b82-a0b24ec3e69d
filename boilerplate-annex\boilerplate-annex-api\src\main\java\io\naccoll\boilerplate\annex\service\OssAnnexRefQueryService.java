package io.naccoll.boilerplate.annex.service;

import io.naccoll.boilerplate.annex.dto.OssAnnexRefQueryCondition;
import io.naccoll.boilerplate.annex.model.OssAnnexRefPo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 通用附件引用查询服务
 *
 * <AUTHOR>
 */
public interface OssAnnexRefQueryService {

	/**
	 * 分页查询通用附件引用
	 * @param condition 查询条件
	 * @param pageable 分页参数
	 * @return 分页结果
	 */
	Page<OssAnnexRefPo> page(OssAnnexRefQueryCondition condition, Pageable pageable);

	/**
	 * 查询所有通用附件引用
	 * @param condition 查询条件
	 * @return 通用附件引用列表
	 */
	List<OssAnnexRefPo> findAll(OssAnnexRefQueryCondition condition);

	/**
	 * 根据附件ID查询附件引用
	 * @param annexId 附件ID
	 * @return 附件引用列表
	 */
	default List<OssAnnexRefPo> findByAnnexId(Long annexId) {
		return findAll(OssAnnexRefQueryCondition.builder().annexId(Collections.singletonList(annexId)).build());
	}

	/**
	 * 根据ID查询通用附件引用
	 * @param id 附件引用ID
	 * @return 附件引用对象
	 */
	OssAnnexRefPo findById(Long id);

	/**
	 * 根据ID查询通用附件引用，不存在时抛出异常
	 * @param id 附件引用ID
	 * @return 附件引用对象
	 */
	OssAnnexRefPo findByIdNotNull(Long id);

	/**
	 * 根据ID集合查询通用附件引用列表
	 * @param ids 附件引用ID集合
	 * @return 附件引用列表
	 */
	List<OssAnnexRefPo> findByIds(Collection<Long> ids);

	/**
	 * 根据ID集合查询通用附件引用列表并转为Map
	 * @param ids 附件引用ID集合
	 * @return 附件引用Map
	 */
	Map<Long, OssAnnexRefPo> findMapByIds(Collection<Long> ids);

	/**
	 * 查询唯一记录
	 * @param annexId 附件ID
	 * @param targetType 目标类型
	 * @param targetId 目标ID
	 * @param annexGroup 附件分组
	 * @return 唯一附件引用记录
	 */
	OssAnnexRefPo findUniq(Long annexId, String targetType, String targetId, String annexGroup);

}
