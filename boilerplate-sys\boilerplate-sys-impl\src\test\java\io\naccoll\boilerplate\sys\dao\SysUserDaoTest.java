package io.naccoll.boilerplate.sys.dao;

import io.naccoll.boilerplate.sys.model.SysUserPo;
import jakarta.annotation.Resource;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.concurrent.atomic.AtomicLong;

/**
 * The type Sys user dao test.
 *
 * <AUTHOR>
 */
@DataJpaTest
class SysUserDaoTest {

	private final AtomicLong idGen = new AtomicLong(1);

	@Resource
	private SysUserDao sysUserDao;

	/**
	 * Test crud.
	 */
	@Test
	@Transactional(propagation = Propagation.NEVER)
	void testCrud() {
		SysUserPo origin = buildUser();
		SysUserPo saved = sysUserDao.save(origin);
		SysUserPo findByIdUser = sysUserDao.findById(saved.getId()).orElse(null);
		Assertions.assertThat(saved).isEqualTo(findByIdUser);
		origin.setPassword("testflush");
		sysUserDao.save(origin);
		SysUserPo findByIdUser3 = sysUserDao.findById(origin.getId()).orElse(null);
		Assertions.assertThat(findByIdUser3).isEqualTo(origin);
		sysUserDao.deleteById(origin.getId());
		Assertions.assertThat(sysUserDao.findById(origin.getId())).isNotPresent();
	}

	/**
	 * Build user sys user po.
	 * @return the sys user po
	 */
	SysUserPo buildUser() {
		SysUserPo user = new SysUserPo();
		user.setId(idGen.addAndGet(1));
		user.setUsername("hello");
		user.setPassword("password");
		user.setEnabled(true);
		user.setOrganizationId(1L);
		return user;
	}

}
