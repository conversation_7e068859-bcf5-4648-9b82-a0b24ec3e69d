package io.naccoll.boilerplate.core.exception;

/**
 * 服务器内部异常，表示未明确分类的系统级错误。 该异常用于处理无法明确分类的系统错误，提供统一的错误处理方式。
 *
 * <AUTHOR>
 */
public class ServerException extends BusinessException {

	/**
	 * 使用错误消息代码构造服务器异常。 该方法用于创建一个基本的服务器异常实例，仅包含错误消息代码。
	 * @param messageCode 错误消息代码，用于获取具体的错误消息
	 */
	public ServerException(String messageCode) {
		super(messageCode, BusinessError.UNDEFINED_ERROR);
	}

	/**
	 * 使用原始异常构造服务器异常。 该方法用于包装其他异常，提供统一的错误处理方式。
	 * @param e 原始异常，包含详细的错误信息和堆栈跟踪
	 */
	public ServerException(Throwable e) {
		super(e.getMessage(), BusinessError.UNDEFINED_ERROR, e);
	}

	/**
	 * 使用错误消息代码和参数构造服务器异常。 该方法用于创建一个包含错误参数的服务器异常实例。
	 * @param messageCode 错误消息代码，用于获取具体的错误消息
	 * @param parameters 错误参数，用于替换错误消息中的占位符
	 */
	public ServerException(String messageCode, Object... parameters) {
		super(messageCode, BusinessError.UNDEFINED_ERROR, parameters);
	}

	/**
	 * 使用错误消息代码、原始异常和参数构造服务器异常。 该方法用于包装其他异常并提供错误参数，提供统一的错误处理方式。
	 * @param messageCode 错误消息代码，用于获取具体的错误消息
	 * @param e 原始异常，包含详细的错误信息和堆栈跟踪
	 * @param parameters 错误参数，用于替换错误消息中的占位符
	 */
	public ServerException(String messageCode, Throwable e, Object... parameters) {
		super(messageCode, BusinessError.UNDEFINED_ERROR, e, parameters);
	}

	/**
	 * 使用错误消息代码、业务错误类型和参数构造服务器异常。 该方法用于创建一个包含业务错误类型和错误参数的服务器异常实例。
	 * @param messageCode 错误消息代码，用于获取具体的错误消息
	 * @param error 业务错误类型，表示具体的业务错误场景
	 * @param parameters 错误参数，用于替换错误消息中的占位符
	 */
	public ServerException(String messageCode, BusinessError error, Object... parameters) {
		super(messageCode, error, null, parameters);
	}

}
