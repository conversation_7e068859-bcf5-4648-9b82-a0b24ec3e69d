dependencies {
    api project(":boilerplate-core:boilerplate-core-common")
    api project(":boilerplate-core:boilerplate-core-web")
    api project(":boilerplate-core:boilerplate-core-persistence")
    api project(":boilerplate-core:boilerplate-core-security")
    implementation project(":boilerplate-sys:boilerplate-sys-api")
    implementation project(":boilerplate-audit:boilerplate-audit-api")

    testImplementation("org.springframework.boot:spring-boot-starter-test")
    testImplementation("com.h2database:h2")
}

bootJar {
    enabled = false
}
jar {
    enabled = true
}
