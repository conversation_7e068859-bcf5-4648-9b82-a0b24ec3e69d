package io.naccoll.boilerplate.core.statemachine.impl;

import io.naccoll.boilerplate.core.statemachine.State;

import java.util.Map;

/**
 * StateHelper
 *
 * <AUTHOR>
 * @date 2020 -02-08 4:23 PM
 */
public class StateHelper {

	private StateHelper() {
	}

	/**
	 * Gets state.
	 * @param <S> the type parameter
	 * @param <E> the type parameter
	 * @param <C> the type parameter
	 * @param stateMap the state map
	 * @param stateId the state id
	 * @return the state
	 */
	public static <S, E, C> State<S, E, C> getState(Map<S, State<S, E, C>> stateMap, S stateId) {
		return stateMap.computeIfAbsent(stateId, k -> new StateImpl<>(stateId));
	}

}
