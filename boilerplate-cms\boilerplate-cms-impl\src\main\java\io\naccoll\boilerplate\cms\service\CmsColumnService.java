package io.naccoll.boilerplate.cms.service;

import io.naccoll.boilerplate.cms.dao.CmsColumnDao;
import io.naccoll.boilerplate.cms.dto.CmsColumnCreateCommand;
import io.naccoll.boilerplate.cms.dto.CmsColumnUpdateCommand;
import io.naccoll.boilerplate.cms.model.CmsColumnPo;
import io.naccoll.boilerplate.core.audit.operate.OperateLog;
import io.naccoll.boilerplate.core.id.IdService;
import io.naccoll.boilerplate.core.ratelimit.RateLimit;
import io.naccoll.boilerplate.core.ratelimit.RateLimiterMode;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * 栏目服务类，提供栏目的创建、修改、修改logo和删除功能
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CmsColumnService {

	@Resource
	private CmsColumnDao cmsColumnDao;

	@Resource
	private CmsColumnQueryService cmsColumnQueryService;

	@Resource
	private IdService idService;

	/**
	 * 创建栏目
	 * @param command 栏目创建命令
	 * @return 创建的栏目实体
	 */
	@Transactional(rollbackFor = Exception.class)
	@OperateLog(value = "添加内容栏目", id = "#result.id", type = "内容栏目",
			afterDataAccess = "@cmsColumnQueryService.findById(#result.id)")
	@RateLimit(mode = RateLimiterMode.WINDOWS, rate = 1)
	public CmsColumnPo create(@Valid CmsColumnCreateCommand command) {
		if (command.getParentId() != null && command.getParentId() > 0) {
			cmsColumnQueryService.findByIdNotNull(command.getParentId());
		}
		CmsColumnPo cmsColumn = new CmsColumnPo();
		cmsColumn.setId(idService.getId());
		cmsColumn.setName(command.getName());
		cmsColumn.setStatus(command.getStatus());
		cmsColumn.setType(command.getType());
		cmsColumn.setParentId(command.getParentId());
		cmsColumn.setDescription(command.getDescription());
		return cmsColumnDao.save(cmsColumn);
	}

	/**
	 * 更新栏目
	 * @param command 栏目更新命令
	 * @return 更新后的栏目实体
	 */
	@Transactional(rollbackFor = Exception.class)
	@OperateLog(value = "修改内容栏目", id = "#command.id", type = "内容栏目",
			beforeDataAccess = "@cmsColumnQueryService.findById(#command.id)",
			afterDataAccess = "@cmsColumnQueryService.findById(#command.id)")
	public CmsColumnPo update(@Valid CmsColumnUpdateCommand command) {
		CmsColumnPo cmsColumnPo = cmsColumnQueryService.findByIdNotNull(command.getId());
		cmsColumnPo.setStatus(command.getStatus());
		cmsColumnPo.setDescription(command.getDescription());
		return cmsColumnDao.save(cmsColumnPo);
	}

	/**
	 * 更新栏目logo
	 * @param id 栏目ID
	 * @param logoUrl logo地址
	 * @return 更新后的栏目实体
	 */
	@OperateLog(value = "变更内容栏目logo", id = "#id", type = "内容栏目",
			beforeDataAccess = "@cmsColumnQueryService.findById(#id)",
			afterDataAccess = "@cmsColumnQueryService.findById(#id)")
	@Transactional(rollbackFor = Exception.class)
	public CmsColumnPo updateLogo(Long id, String logoUrl) {
		CmsColumnPo cmsColumnPo = cmsColumnQueryService.findByIdNotNull(id);
		cmsColumnPo.setLogo(logoUrl);
		return cmsColumnDao.save(cmsColumnPo);
	}

	/**
	 * 删除栏目
	 * @param id 栏目ID
	 */
	@Transactional(rollbackFor = Exception.class)
	@OperateLog(value = "删除内容栏目", id = "#id", type = "内容栏目", beforeDataAccess = "@cmsColumnQueryService.findById(#id)")
	@RateLimit(mode = RateLimiterMode.WINDOWS, rate = 1)
	public void deleteById(Long id) {
		cmsColumnQueryService.findByIdNotNull(id);
		cmsColumnDao.deleteById(id);
	}

}
