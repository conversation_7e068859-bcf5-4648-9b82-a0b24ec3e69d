package io.naccoll.boilerplate.core.security.enums;

/**
 * 认证存储类型枚举
 * <p>
 * 定义认证信息的存储方式，支持内存和Redis两种实现
 * </p>
 *
 * <b>实现特点：</b>
 * <ul>
 * <li>SIMPLE：基于内存的简单实现，适合开发和测试环境</li>
 * <li>REDIS：基于Redis的分布式实现，适合生产环境</li>
 * </ul>
 *
 * <AUTHOR>
 */
public enum AuthStorageType {

	/**
	 * 基于内存的简单存储实现
	 * <p>
	 * 该实现将认证信息存储在内存中，具有快速访问的特点 但不适合高并发和分布式场景，数据不持久化
	 * </p>
	 */
	SIMPLE,

	/**
	 * 基于Redis的分布式存储实现
	 * <p>
	 * 该实现将认证信息存储在Redis中，支持高并发和分布式场景 数据持久化，支持复杂的过期策略和集群部署
	 * </p>
	 */
	REDIS

}
