package io.naccoll.boilerplate.audit.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 系统操作日志查询条件DTO
 *
 * 该类用于系统操作日志的查询条件封装，包含操作描述、日志类型、客户端信息、用户信息、时间范围等字段。
 *
 * <AUTHOR>
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "系统操作日志查询DTO")
public class AuditOperationLogQueryCondition implements Serializable {

	/**
	 * 操作描述
	 *
	 * 用于记录用户执行的具体操作内容，例如"新增用户"、"修改配置"等。
	 */
	@Schema(description = "操作描述")
	private String description;

	/**
	 * 日志类型
	 *
	 * 表示日志的类型，例如"INFO"、"ERROR"、"WARNING"等。
	 */
	@Schema(description = "日志类型")
	private String type;

	/**
	 * 客户端IP地址
	 *
	 * 记录执行操作的客户端IP地址，用于追踪操作来源。
	 */
	@Schema(description = "客户端IP地址")
	private String clientIp;

	/**
	 * 用户ID
	 *
	 * 执行操作的用户唯一标识，用于关联用户信息。
	 */
	@Schema(description = "用户ID")
	private Long userId;

	/**
	 * 用户名
	 *
	 * 执行操作的用户名，用于显示操作执行者。
	 */
	@Schema(description = "用户名")
	private String username;

	/**
	 * 客户端地址
	 *
	 * 客户端的地理位置信息，例如城市、国家等。
	 */
	@Schema(description = "客户端地址")
	private String clientAddress;

	/**
	 * 用户域
	 *
	 * 用户所属的域信息，用于多租户环境下的区分。
	 */
	@Schema(description = "用户域")
	private String realm;

	/**
	 * 操作开始时间
	 *
	 * 查询的时间范围开始点，用于过滤指定时间段内的操作日志。
	 */
	@Schema(description = "操作开始时间")
	private Date startDate;

	/**
	 * 操作结束时间
	 *
	 * 查询的时间范围结束点，用于过滤指定时间段内的操作日志。
	 */
	@Schema(description = "操作结束时间")
	private Date endDate;

	/**
	 * 目标ID
	 *
	 * 操作的目标对象唯一标识，例如被操作的资源ID。
	 */
	@Schema(description = "目标ID")
	private String targetId;

	/**
	 * 用户真实名称
	 *
	 * 用户的完整名称，用于显示用户的真实身份。
	 */
	@Schema(description = "用户真实名称")
	private String name;

}
