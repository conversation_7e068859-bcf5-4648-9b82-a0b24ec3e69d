package io.naccoll.boilerplate.core.security.enums;

import io.naccoll.boilerplate.core.enums.DisplayEnum;
import io.naccoll.boilerplate.core.enums.EnumHelper;

/**
 * Token类型枚举
 * <p>
 * 定义不同类型的Token格式，用于身份验证和权限控制
 * </p>
 *
 * <AUTHOR>
 */
public enum TokenType implements DisplayEnum {

	/**
	 * 字符串类型Token
	 * <p>
	 * 用于简单的字符串形式Token，适用于基本的身份验证场景
	 * </p>
	 */
	STRING(1, "字符串"),

	/**
	 * JSON类型Token
	 * <p>
	 * 用于结构化的JSON格式Token，适用于需要携带丰富信息的场景
	 * </p>
	 */
	JSON(2, "JSON");

	private final Integer id;

	private final String name;

	/**
	 * 构造方法，初始化Token类型
	 * @param id Token类型ID
	 * @param name Token类型名称
	 */
	TokenType(Integer id, String name) {
		this.id = id;
		this.name = name;
	}

	/**
	 * 根据ID获取Token类型
	 * @param id Token类型ID
	 * @return 对应的Token类型枚举
	 */
	public static TokenType fromId(int id) {
		return EnumHelper.fromId(TokenType.class, id);
	}

	@Override
	public Integer getId() {
		return id;
	}

	@Override
	public String getName() {
		return name;
	}

}
