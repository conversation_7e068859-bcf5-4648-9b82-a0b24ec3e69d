package io.naccoll.boilerplate.core.ratelimit;

import io.naccoll.boilerplate.core.cache.CacheTemplate;

import java.time.Duration;
import java.util.List;

/**
 * 简单令牌桶限流器实现
 *
 * <AUTHOR>
 */
public class SimpleTokenBucketRateLimiter extends BaseTokenBucketRateLimiter {

	private final CacheTemplate cacheTemplate;

	/**
	 * 构造函数
	 * @param defaultConfig 默认限流配置
	 * @param cacheTemplate 缓存操作模板
	 */
	protected SimpleTokenBucketRateLimiter(RateLimiterProperties defaultConfig, CacheTemplate cacheTemplate) {
		super(true, defaultConfig);
		this.cacheTemplate = cacheTemplate;
	}

	@Override
	/**
	 * 执行令牌桶限流检查
	 * @param id 限流唯一标识
	 * @param replenishRate 令牌补充速率
	 * @param burstCapacity 突发容量
	 * @param requestedTokens 请求令牌数
	 * @param routeConfig 路由配置
	 * @return 限流响应结果
	 */
	protected Response check(String id, int replenishRate, int burstCapacity, int requestedTokens,
			RateLimiterProperties routeConfig) {
		String lockKey = "ratelimit:" + id;
		return cacheTemplate.executeLocked(lockKey, () -> {
			List<String> keys = getKeys(id);
			String tokenKey = keys.getFirst();
			String timestampKey = keys.get(1);

			double fillTime = 1.0 * burstCapacity / replenishRate;
			long ttl = (long) Math.floor(fillTime * 2);
			int now = (int) (System.currentTimeMillis() / 1000);

			Object lastTokensObj = cacheTemplate.get(tokenKey);
			int lastTokens = lastTokensObj == null ? burstCapacity : (int) lastTokensObj;

			Object lastRefreshedObj = cacheTemplate.get(timestampKey);
			int lastRefreshed = lastRefreshedObj == null ? 0 : (int) lastRefreshedObj;

			int delta = Math.max(0, now - lastRefreshed);
			int filledTokens = Math.min(burstCapacity, lastTokens + (delta * replenishRate));
			boolean allowed = filledTokens >= requestedTokens;
			int newTokens = filledTokens;
			long allowedNum = 0;
			if (allowed) {
				newTokens = filledTokens - requestedTokens;
				allowedNum = 1;
			}

			if (ttl > 0) {
				cacheTemplate.set(tokenKey, newTokens, Duration.ofSeconds(ttl));
				cacheTemplate.set(timestampKey, now, Duration.ofSeconds(ttl));
			}
			return new Response(allowed, getHeaders(routeConfig, allowedNum));
		});
	}

}
