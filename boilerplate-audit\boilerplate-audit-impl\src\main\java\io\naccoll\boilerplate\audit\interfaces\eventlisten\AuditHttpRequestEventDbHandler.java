package io.naccoll.boilerplate.audit.interfaces.eventlisten;

import io.naccoll.boilerplate.audit.config.AuditLogProperties;
import io.naccoll.boilerplate.audit.dao.AuditHttpRequestLogDao;
import io.naccoll.boilerplate.audit.model.AuditHttpRequestLogPo;
import io.naccoll.boilerplate.core.audit.enums.AuditStorageType;
import io.naccoll.boilerplate.core.audit.feign.HttpRequestAuditCommand;
import io.naccoll.boilerplate.core.audit.feign.HttpRequestAuditEvent;
import io.naccoll.boilerplate.core.constant.AuditRequestConstant;
import io.naccoll.boilerplate.core.utils.JsonUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 审计HTTP请求事件数据库处理器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@EnableConfigurationProperties(AuditLogProperties.class)
public class AuditHttpRequestEventDbHandler implements ApplicationListener<HttpRequestAuditEvent> {

	/**
	 * 审计日志配置属性
	 */
	@Resource
	private AuditLogProperties auditLogProperties;

	/**
	 * 审计HTTP请求日志数据访问对象
	 */
	@Resource
	private AuditHttpRequestLogDao auditHttpRequestLogDao;

	/**
	 * 处理HTTP请求审计事件
	 * @param event HTTP请求审计事件
	 */
	@Override
	@Async
	public void onApplicationEvent(HttpRequestAuditEvent event) {
		AuditLogProperties.Http http = auditLogProperties.getHttp();
		if (http.isEnabled() && http.getType().contains(AuditStorageType.DB)) {
			HttpRequestAuditCommand httpRequest = event.getPayload();
			String source = event.getSource().toString();
			if (log.isDebugEnabled()) {
				if (Objects.equals(source, AuditRequestConstant.REQUEST)) {
					save(httpRequest);
				}

				if (Objects.equals(source, AuditRequestConstant.RESPONSE)) {
					save(httpRequest);
				}

				if (Objects.equals(source, AuditRequestConstant.IOEXCEPTION)) {
					save(httpRequest);
				}
			}
		}
	}

	/**
	 * 保存HTTP请求审计信息到数据库
	 * @param httpRequest HTTP请求审计命令
	 */
	private void save(HttpRequestAuditCommand httpRequest) {
		AuditHttpRequestLogPo audit = auditHttpRequestLogDao.findById(httpRequest.getRequestId())
			.orElse(new AuditHttpRequestLogPo());
		audit.setId(httpRequest.getRequestId());
		audit.setProtocolVersion(httpRequest.getProtocolVersion());
		audit.setMethod(httpRequest.getMethod());
		audit.setUrl(httpRequest.getUrl());
		audit.setLogLevel(httpRequest.getLogLevel());
		audit.setRequestClass(httpRequest.getRequestClass().getName());
		audit.setRequestDate(httpRequest.getRequestDate());
		audit.setRequestHeader(JsonUtil.toString(httpRequest.getRequestHeader()));
		audit.setRequestBody(httpRequest.getRequestBody());
		audit.setRequestBodyLength(httpRequest.getRequestBodyLength());
		audit.setResponseReason(httpRequest.getResponseReason());
		audit.setResponseStatus(httpRequest.getResponseStatus());
		audit.setElapsedTime(httpRequest.getElapsedTime());
		audit.setResponseDate(httpRequest.getResponseDate());
		audit.setResponseHeader(JsonUtil.toString(httpRequest.getResponseHeader()));
		audit.setResponseBody(httpRequest.getResponseBody());
		audit.setResponseBodyLength(httpRequest.getResponseBodyLength());
		audit.setError(httpRequest.getError());
		audit.setErrorDetail(httpRequest.getErrorDetail());
		auditHttpRequestLogDao.saveAndFlush(audit);
	}

}
