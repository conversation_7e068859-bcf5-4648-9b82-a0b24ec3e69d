package io.naccoll.boilerplate.core.exception;

import java.io.Serial;

/**
 * 数据关联异常，表示存在数据关联时尝试了非法操作
 *
 * <AUTHOR>
 */
public class DataRelatedException extends BusinessException {

	@Serial
	private static final long serialVersionUID = 2328912758348709L;

	/**
	 * 构造数据关联异常
	 * @param code 错误码
	 * @param parameters 错误参数
	 */
	public DataRelatedException(String code, Object... parameters) {
		super(code, BusinessError.DATA_RELATED, parameters);
	}

	/**
	 * 构造带原始异常的数据关联异常
	 * @param code 错误码
	 * @param throwable 原始异常
	 * @param parameters 错误参数
	 */
	public DataRelatedException(String code, Throwable throwable, Object... parameters) {
		super(code, BusinessError.DATA_RELATED, throwable, parameters);
	}

}
