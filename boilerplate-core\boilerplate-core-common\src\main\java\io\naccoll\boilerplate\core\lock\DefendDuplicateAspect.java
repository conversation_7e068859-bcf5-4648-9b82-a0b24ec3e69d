package io.naccoll.boilerplate.core.lock;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.crypto.SecureUtil;
import io.naccoll.boilerplate.core.cache.CacheTemplate;
import io.naccoll.boilerplate.core.exception.BusinessError;
import io.naccoll.boilerplate.core.exception.BusinessException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.context.expression.BeanFactoryResolver;
import org.springframework.core.Ordered;
import org.springframework.core.ParameterNameDiscoverer;
import org.springframework.core.StandardReflectionParameterNameDiscoverer;
import org.springframework.core.annotation.Order;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.lang.reflect.Method;
import java.time.Duration;
import java.util.Objects;

/**
 * 防重复提交切面，用于防止同一业务在短时间内被重复提交 支持通过注解配置前缀和自定义锁键表达式
 *
 * <AUTHOR>
 */
@Component
@Aspect
@Slf4j
@Order(Ordered.LOWEST_PRECEDENCE - 101)
public class DefendDuplicateAspect {

	@Resource
	private CacheTemplate cacheTemplate;

	@Resource
	private BeanFactory beanFactory;

	/**
	 * 定义切入点：标记了@DefendDuplicate注解的方法
	 */
	@Pointcut("@annotation(io.naccoll.boilerplate.core.lock.DefendDuplicate)")
	public void lockPointcut() {
	}

	/**
	 * 环绕通知：实现防重复提交逻辑
	 * @param joinPoint 连接点对象
	 * @return 方法执行结果
	 * @throws Throwable 可能抛出的异常
	 */
	@Around("lockPointcut()")
	public Object lockAround(ProceedingJoinPoint joinPoint) throws Throwable {
		DefendDuplicate annotation = getAnnotation(joinPoint);
		String prefix = getPrefix(joinPoint);
		String key = getKey(joinPoint);
		String limitKey = String.format("%s:%s", prefix, key);
		if (!cacheTemplate.setIfAbsent(limitKey, 1, Duration.ofMillis(annotation.silentPeriod()))) {
			throw new BusinessException("请勿重复提交", BusinessError.DUPLICATE_SUBMIT);
		}
		return joinPoint.proceed();
	}

	/**
	 * 获取锁前缀 如果注解未指定前缀，则使用方法签名作为默认前缀
	 * @param joinPoint 连接点对象
	 * @return 锁前缀字符串，不能为空
	 */
	private String getPrefix(ProceedingJoinPoint joinPoint) {
		DefendDuplicate aopLog = getAnnotation(joinPoint);
		String prefix = aopLog.prefix();
		if (!StringUtils.hasLength(prefix)) {
			MethodSignature signature = (MethodSignature) joinPoint.getSignature();
			return signature.toString();
		}
		return prefix;
	}

	/**
	 * 生成防重锁键 支持通过SPEL表达式自定义锁键生成逻辑 如果未配置表达式，则使用参数哈希值作为锁键
	 * @param joinPoint 连接点对象
	 * @return 生成的锁键字符串，如果注解未指定key表达式，则使用参数哈希值
	 */
	private String getKey(ProceedingJoinPoint joinPoint) {
		DefendDuplicate aopLog = getAnnotation(joinPoint);
		String keyExp = aopLog.key();
		if (!StringUtils.hasLength(keyExp)) {
			return SecureUtil.sha256(CharSequenceUtil.join(",", joinPoint.getArgs()));
		}
		Object[] args = joinPoint.getArgs();
		MethodSignature signature = (MethodSignature) joinPoint.getSignature();
		Method method = signature.getMethod();
		ExpressionParser parser = new SpelExpressionParser();
		ParameterNameDiscoverer discoverer = new StandardReflectionParameterNameDiscoverer();
		String[] params = discoverer.getParameterNames(method);
		StandardEvaluationContext context = new StandardEvaluationContext();
		for (int len = 0; len < Objects.requireNonNull(params).length; len++) {
			context.setVariable("p" + len, args[len]);
			context.setVariable(params[len], args[len]);
		}
		context.setBeanResolver(new BeanFactoryResolver(beanFactory));
		Expression expression = parser.parseExpression(keyExp);
		Object key = expression.getValue(context, Object.class);
		if (key == null) {
			return null;
		}
		return key.toString();
	}

	/**
	 * 获取方法上的DefendDuplicate注解
	 * @param joinPoint 连接点对象
	 * @return 方法上的注解实例
	 */
	private DefendDuplicate getAnnotation(ProceedingJoinPoint joinPoint) {
		MethodSignature signature = (MethodSignature) joinPoint.getSignature();
		Method method = signature.getMethod();
		return method.getAnnotation(DefendDuplicate.class);
	}

}
