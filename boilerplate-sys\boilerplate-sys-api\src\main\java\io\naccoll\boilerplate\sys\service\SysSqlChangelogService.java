package io.naccoll.boilerplate.sys.service;

import io.naccoll.boilerplate.sys.model.SysSqlChangelogPo;
import io.naccoll.boilerplate.sys.model.SysSqlPo;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 动态SQL集变更日志服务
 *
 * <AUTHOR>
 */
public interface SysSqlChangelogService {

	/**
	 * 创建动态SQL集变更日志
	 * @param command 创建参数
	 * @return SysSqlChangelogPo
	 */
	SysSqlChangelogPo create(@Valid SysSqlPo command);

	/**
	 * 批量创建动态SQL集变更日志
	 * @param commands 创建参数
	 * @return SysSqlChangelogPo 列表
	 */
	List<SysSqlChangelogPo> batchCreate(@Valid List<SysSqlPo> commands);

	/**
	 * 删除动态SQL集变更日志
	 * @param id 动态SQL集变更日志Id
	 */
	void deleteById(Long id);

}
