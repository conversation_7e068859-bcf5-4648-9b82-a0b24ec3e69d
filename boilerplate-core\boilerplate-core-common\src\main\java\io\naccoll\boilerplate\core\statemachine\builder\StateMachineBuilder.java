package io.naccoll.boilerplate.core.statemachine.builder;

import io.naccoll.boilerplate.core.statemachine.StateMachine;

/**
 * StateMachineBuilder
 *
 * @param <S> the type parameter
 * @param <E> the type parameter
 * @param <C> the type parameter
 * <AUTHOR>
 * @date 2020 -02-07 5:32 PM
 */
public interface StateMachineBuilder<S, E, C> {

	/**
	 * Builder for one transition
	 * @return External transition builder
	 */
	ExternalTransitionBuilder<S, E, C> withExternal();

	/**
	 * Builder for multiple transitions
	 * @return External transition builder
	 */
	ExternalTransitionsBuilder<S, E, C> withExternals();

	/**
	 * Start target build internal transition
	 * @return Internal transition builder
	 */
	InternalTransitionBuilder<S, E, C> withInternal();

	/**
	 * set up fail callback, default do nothing {@code NumbFailCallbackImpl}
	 * @param callback the callback
	 */
	void withFailCallback(FailCallback<S, E, C> callback);

	/**
	 * Build state machine.
	 * @param machineId the machine id
	 * @return the state machine
	 */
	StateMachine<S, E, C> build(String machineId);

}
