package io.naccoll.boilerplate.audit.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 状态机日志创建对象
 *
 * <AUTHOR>
 */
@Data
public class AuditStateMachineLogCreateCommand {

	/**
	 * 使用的状态机
	 */
	@Schema(description = "使用的状态机")
	private String stateMachine;

	/**
	 * 目标id
	 */
	@Schema(description = "目标id")
	private Long targetId;

	/**
	 * 客户端ip
	 */
	@Schema(description = "客户端ip")
	private String clientIp;

	/**
	 * 客户端地址
	 */
	@Schema(description = "客户端地址")
	private String clientAddress;

	/**
	 * 用户id
	 */
	@Schema(description = "用户id")
	private Long userId;

	/**
	 * 用户名
	 */
	@Schema(description = "用户名")
	private String username;

	/**
	 * 真实姓名
	 */
	@Schema(description = "真实姓名")
	private String name;

	/**
	 * 用户域
	 */
	@Schema(description = "用户域")
	private String realm;

	/**
	 * 触发事件
	 */
	@Schema(description = "触发事件")
	private Integer event;

	/**
	 * 之前的状态
	 */
	@Schema(description = "之前的状态")
	private Integer beforeState;

	/**
	 * 之后的状态
	 */
	@Schema(description = "之后的状态")
	private Integer afterState;

	/**
	 * 修改前的上下文
	 */
	@Schema(description = "修改前的上下文")
	private String beforeContext;

	/**
	 * 修改后的上下文
	 */
	@Schema(description = "修改后的上下文")
	private String afterContext;

	/**
	 * 之前的状态名称
	 */
	@Schema(description = "之前的状态名称")
	private String beforeStateName;

	/**
	 * 之后的状态名称
	 */
	@Schema(description = "之后的状态名称")
	private String afterStateName;

	/**
	 * 额外的参数
	 */
	@Schema(description = "额外的参数")
	private String extraParams;

	/**
	 * 事件名称
	 */
	@Schema(description = "事件名称")
	private String eventName;

}
