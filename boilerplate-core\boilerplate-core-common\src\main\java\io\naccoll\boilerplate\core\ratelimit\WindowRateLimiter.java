package io.naccoll.boilerplate.core.ratelimit;

import io.naccoll.boilerplate.core.cache.CacheTemplate;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Optional;

/**
 * 时间窗口限流器实现 通过缓存模板实现基于时间窗口的限流功能
 *
 * <AUTHOR>
 */
@Component
public class WindowRateLimiter extends BaseRateLimiter {

	private final CacheTemplate cacheTemplate;

	/**
	 * 构造函数
	 * @param cacheTemplate 缓存操作模板
	 * @param rateLimiterProperties 限流配置属性
	 */
	public WindowRateLimiter(CacheTemplate cacheTemplate, RateLimiterProperties rateLimiterProperties) {
		super(rateLimiterProperties);
		this.cacheTemplate = cacheTemplate;
	}

	/**
	 * 检查请求是否允许通过
	 * @param key 限流唯一标识
	 * @param args 限流配置参数
	 * @return 限流响应结果
	 */
	@Override
	public Response isAllowed(String key, RateLimiterProperties args) {
		RateLimiterProperties routeConfig = getRouteConfig(args);
		int ttl = routeConfig.getTtl();
		int rate = routeConfig.getRate();
		if (cacheTemplate.setIfAbsent(key, 1, Duration.ofSeconds(ttl))) {
			return new Response(true, getHeaders(routeConfig, (long) (rate - 1)));
		}
		if (rate == 1) {
			return new Response(false, getHeaders(routeConfig, 0L));
		}
		long token = Optional.ofNullable(cacheTemplate.increment(key)).orElse(0L);
		return new Response(token <= rate, getHeaders(routeConfig, rate - token));
	}

	/**
	 * 获取限流模式
	 * @return 限流模式
	 */
	@Override
	public RateLimiterMode getMode() {
		return RateLimiterMode.WINDOWS;
	}

}
