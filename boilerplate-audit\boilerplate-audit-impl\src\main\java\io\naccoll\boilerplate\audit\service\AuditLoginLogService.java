package io.naccoll.boilerplate.audit.service;

import io.naccoll.boilerplate.audit.dao.AuditLoginLogDao;
import io.naccoll.boilerplate.audit.model.AuditLoginLogPo;
import io.naccoll.boilerplate.core.audit.login.LoginSuccessEvent;
import io.naccoll.boilerplate.core.id.IdService;
import io.naccoll.boilerplate.core.ip.Ip2RegionService;
import io.naccoll.boilerplate.core.security.authtication.entity.UserDetailsImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Optional;

/**
 * 登录日志服务
 *
 * <AUTHOR>
 */
@Service
public class AuditLoginLogService {

	@Resource
	private AuditLoginLogDao auditLoginLogDao;

	@Resource
	private Ip2RegionService ip2RegionService;

	@Resource
	private IdService idService;

	/**
	 * 处理登录成功事件
	 * @param event 登录成功事件
	 */
	public void loginSuccess(LoginSuccessEvent event) {
		String ip = event.getSource().toString();
		UserDetailsImpl userDetailsImpl = event.getPayload();
		AuditLoginLogPo po = new AuditLoginLogPo();
		po.setId(idService.getId());
		po.setUserId(userDetailsImpl.getId());
		po.setUsername(Optional.ofNullable(userDetailsImpl.getName()).orElse(userDetailsImpl.getUsername()));
		po.setRealm(userDetailsImpl.getRealm().toString());
		po.setLoginIp(ip);
		po.setLoginDate(new Date());
		po.setLoginAddress(ip2RegionService.getCityInfo(ip));
		auditLoginLogDao.save(po);
	}

}
