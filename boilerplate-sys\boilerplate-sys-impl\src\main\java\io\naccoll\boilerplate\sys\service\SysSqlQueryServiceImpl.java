package io.naccoll.boilerplate.sys.service;

import io.naccoll.boilerplate.core.exception.ResourceNotFoundException;
import io.naccoll.boilerplate.core.persistence.dao.DaoUtils;
import io.naccoll.boilerplate.sys.dao.SysSqlDao;
import io.naccoll.boilerplate.sys.dto.SysSqlQueryCondition;
import io.naccoll.boilerplate.sys.model.SysSqlPo;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 动态SQL集查询服务实现
 *
 * <AUTHOR>
 */
@Service
public class SysSqlQueryServiceImpl implements SysSqlQueryService {

	@Resource
	private SysSqlDao sysSqlDao;

	@Override
	public SysSqlPo findByCode(String code) {
		return sysSqlDao.findFirstByCode(code);
	}

	@Override
	public List<SysSqlPo> findByCodes(Collection<String> codes) {
		return sysSqlDao.findByCodeIn(codes);
	}

	@Override
	public SysSqlPo findByCodeNotNull(String code) {
		SysSqlPo sysSql = findByCode(code);
		if (sysSql == null) {
			throw new ResourceNotFoundException("动态SQL集不存在");
		}
		return sysSql;
	}

	@Override
	public Page<SysSqlPo> page(SysSqlQueryCondition condition, Pageable pageable) {
		return sysSqlDao.page(condition, pageable);
	}

	@Override
	public List<SysSqlPo> findAll(SysSqlQueryCondition condition) {
		return sysSqlDao.findAll(condition);
	}

	@Override
	public SysSqlPo findById(Long id) {
		return sysSqlDao.findById(id).orElse(null);
	}

	@Override
	public SysSqlPo findByIdNotNull(Long id) {
		return Optional.ofNullable(findById(id)).orElseThrow(() -> new ResourceNotFoundException("动态SQL集不存在"));
	}

	@Override
	public List<SysSqlPo> findByIds(Collection<Long> ids) {
		return DaoUtils.findByPrimaryKeyIn(sysSqlDao, ids);
	}

	@Override
	public Map<Long, SysSqlPo> findMapByIds(Collection<Long> ids) {
		return DaoUtils.findMapByPrimaryKeyIn(sysSqlDao, ids);
	}

}
