package io.naccoll.boilerplate.cms.enums;

import io.naccoll.boilerplate.core.enums.DisplayEnum;

/**
 * The enum Cms column status.
 *
 * <AUTHOR>
 */
public enum CmsColumnStatus implements DisplayEnum {

	/**
	 * 禁用
	 */
	DISABLE(0, "禁用"),
	/**
	 * 启用
	 */
	ENABLE(1, "启用");

	/**
	 * 状态ID
	 */
	private final Integer id;

	/**
	 * 状态名称
	 */
	private final String name;

	/**
	 * 构造方法
	 * @param id 状态ID
	 * @param name 状态名称
	 */
	CmsColumnStatus(Integer id, String name) {
		this.id = id;
		this.name = name;
	}

	@Override
	public Integer getId() {
		return id;
	}

	@Override
	public String getName() {
		return name;
	}

}
