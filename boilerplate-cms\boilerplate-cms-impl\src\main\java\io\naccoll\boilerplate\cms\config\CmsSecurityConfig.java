package io.naccoll.boilerplate.cms.config;

import io.naccoll.boilerplate.cms.constant.CmsApiConstant;
import io.naccoll.boilerplate.core.security.customizer.ApiSecurityConfigCustomizer;
import io.naccoll.boilerplate.core.security.properties.RequestPath;
import io.naccoll.boilerplate.core.security.properties.SecurityProperties;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.AuthorizeHttpRequestsConfigurer;

import java.util.Arrays;

/**
 * CMS安全配置类
 *
 * 该类用于配置CMS模块的安全设置，包括API的认证和权限控制
 *
 * <AUTHOR>
 */
@Configuration
public class CmsSecurityConfig implements InitializingBean, ApiSecurityConfigCustomizer {

	@Resource
	private SecurityProperties securityProperties;

	/**
	 * 自定义安全配置
	 * @param registry 安全配置注册器
	 */
	@Override
	public void customize(
			AuthorizeHttpRequestsConfigurer<HttpSecurity>.AuthorizationManagerRequestMatcherRegistry registry) {
		// @formatter:off
        registry
            .requestMatchers(getIgnoreToken()).permitAll()
            .requestMatchers(CmsApiConstant.PlatformApiV1.PREFIX + "/**").fullyAuthenticated();
        // @formatter:on
	}

	/**
	 * 获取忽略Token验证的路径
	 * @return 忽略Token验证的路径数组
	 */
	private String[] getIgnoreToken() {
		return new String[] { CmsApiConstant.PublicApiV1.PREFIX + "/**" };
	}

	/**
	 * 初始化完成后执行的方法
	 */
	@Override
	public void afterPropertiesSet() {
		for (String path : getIgnoreToken()) {
			for (HttpMethod method : Arrays.asList(HttpMethod.GET, HttpMethod.POST, HttpMethod.PUT,
					HttpMethod.DELETE)) {
				securityProperties.getIgnoreTokenUrls().add(new RequestPath(path, method));
			}
		}
	}

}
