/*
 * Copyright 2023 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package io.naccoll.boilerplate.core.interfaces.function;

/**
 * 一个类似于Runnable的接口，允许抛出任何Throwable。检查型异常将被包装在IllegalStateException中。
 *
 * 此接口的主要目的是在需要使用Runnable但又需要处理受检异常的场景下提供支持。
 *
 * @param <E> 可能抛出的受检异常类型
 * <AUTHOR> Bilan
 * <AUTHOR>
 */
@FunctionalInterface
public interface CheckedRunnable<E extends Throwable> {

	/**
	 * 执行可能抛出受检异常的操作。
	 * @throws E 可能抛出的受检异常
	 */
	void run() throws E;

	/**
	 * 将CheckedRunnable转换为标准的Runnable接口。
	 * 此方法会将受检异常包装在IllegalStateException中，以符合Runnable接口的要求。
	 * @return 转换后的Runnable实例
	 */
	default Runnable unchecked() {
		return () -> {
			try {
				run();
			}
			catch (Throwable t) { // NOSONAR
				if (t instanceof RuntimeException runtimeException) { // NOSONAR
					throw runtimeException;
				}
				else if (t instanceof Error error) { // NOSONAR
					throw error;
				}
				else {
					throw new IllegalStateException(t);
				}
			}
		};
	}

}
