package io.naccoll.boilerplate.core.validate;

import io.naccoll.boilerplate.core.enums.EnumHelper;
import io.naccoll.boilerplate.core.enums.IdableEnum;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.extern.slf4j.Slf4j;

import java.util.Collection;

/**
 * 可标识枚举验证器实现类
 * <p>
 * 验证参数是否为指定枚举的有效标识值，支持单个值和集合验证
 * </p>
 * <p>
 * 支持以下验证场景： 1. 单个枚举值验证 2. 集合枚举值验证 3. 空值处理（可配置）
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
public class IsIdableEnumValidator implements ConstraintValidator<IsIdableEnum, Object> {

	/**
	 * 是否启用验证，false表示不验证
	 */
	private boolean required = false;

	/**
	 * 需要验证的可标识枚举类型
	 */
	private Class<? extends IdableEnum> idableEnum;

	@Override
	public void initialize(IsIdableEnum constraintAnnotation) {
		required = constraintAnnotation.required(); // 修复拼写错误
		idableEnum = constraintAnnotation.support();
	}

	@Override
	public boolean isValid(Object value, ConstraintValidatorContext context) {
		if (!required) {
			return true;
		}

		if (value instanceof Collection<?>list) {
			return validateCollection(list);
		}
		return validateSingleValue(value);
	}

	/**
	 * 验证集合中的所有值是否有效
	 * @param list 待验证的值集合
	 * @return 验证结果，所有值都有效时返回true
	 */
	private boolean validateCollection(Collection<?> list) {
		boolean result = true;
		for (Object o : list) {
			result = result && validateSingleValue(o);
		}
		return result;
	}

	/**
	 * 验证单个值是否有效
	 * @param value 待验证的值
	 * @return 验证结果
	 */
	private boolean validateSingleValue(Object value) {
		if (idableEnum == null) {
			throw new IllegalStateException("IdableEnum type not initialized");
		}

		if (!required && value == null) {
			return true;
		}

		if (value instanceof Number number) {
			return checkNumericValue(number);
		}

		log.debug("Invalid enum value type: {} for {}", value.getClass().getSimpleName(), idableEnum.getSimpleName());
		return false;
	}

	/**
	 * 验证数字类型的枚举值
	 * @param number 待验证的数字值
	 * @return 验证结果
	 */
	private boolean checkNumericValue(Number number) {
		try {
			int intValue = number.intValue();
			boolean valid = EnumHelper.isValid(idableEnum, intValue);
			if (!valid) {
				log.debug("Invalid enum value: {} for {}", intValue, idableEnum.getSimpleName());
			}
			return valid;
		}
		catch (ClassCastException e) {
			log.debug("Value conversion failed for {}", idableEnum.getSimpleName(), e);
			return false;
		}
	}

}
