package io.naccoll.boilerplate.core.messenger;

import java.time.Duration;

/**
 * 短信发送接口，用于实现短信验证码的发送功能 提供发送短信验证码的核心方法
 *
 * <AUTHOR>
 */
public interface SmsSender {

	/**
	 * 发送验证码方法 该方法用于向指定手机号发送验证码，并设置验证码的过期时间
	 * @param mobile 接收验证码的手机号
	 * @param code 需要发送的验证码内容
	 * @param duration 验证码的有效时长
	 * @return boolean 发送结果，true表示发送成功，false表示发送失败
	 */
	boolean sendCaptchaCode(String mobile, String code, Duration duration);

}
