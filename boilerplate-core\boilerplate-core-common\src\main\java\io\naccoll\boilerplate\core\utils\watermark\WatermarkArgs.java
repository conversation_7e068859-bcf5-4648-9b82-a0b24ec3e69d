package io.naccoll.boilerplate.core.utils.watermark;

import cn.hutool.core.img.ColorUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.awt.*;

/**
 * 水印参数配置类
 * <p>
 * 该类用于配置水印的文字、样式、位置等参数，支持自定义水印的透明度、字体、颜色、间距和旋转角度。
 * </p>
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WatermarkArgs {

	/**
	 * 水印文字内容
	 */
	private String watermarkText;

	/**
	 * 水印透明度，默认0.5
	 */
	@Builder.Default
	private float alpha = 0.5f;

	/**
	 * 水印文字大小，默认16
	 */
	@Builder.Default
	private int fontSize = 16;

	/**
	 * 水印文字字体，默认微软雅黑加粗16号
	 */
	@Builder.Default
	private Font font = new Font("微软雅黑", Font.BOLD, 16);

	/**
	 * 水印文字颜色，默认浅灰色(#f5f5f5)
	 */
	@Builder.Default
	private Color color = ColorUtil.hexToColor("#f5f5f5");

	/**
	 * 水印之间的水平间隔，默认40像素
	 */
	@Builder.Default
	private int xMove = 40;

	/**
	 * 水印之间的垂直间隔，默认40像素
	 */
	@Builder.Default
	private int yMove = 40;

	/**
	 * 水印旋转角度，默认30度
	 */
	@Builder.Default
	private Double degree = 30.0;

	/**
	 * 快速创建只包含文字的水印参数
	 * @param text 水印文字
	 * @return 水印参数对象
	 */
	public static WatermarkArgs createText(String text) {
		WatermarkArgs watermarkArgs = new WatermarkArgs();
		watermarkArgs.setWatermarkText(text);
		return watermarkArgs;
	}

}
