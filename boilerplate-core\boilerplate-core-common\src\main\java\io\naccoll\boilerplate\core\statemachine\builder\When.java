package io.naccoll.boilerplate.core.statemachine.builder;

import io.naccoll.boilerplate.core.statemachine.Action;

/**
 * When
 *
 * @param <S> the type parameter
 * @param <E> the type parameter
 * @param <C> the type parameter
 * <AUTHOR>
 * @date 2020 -02-07 9:33 PM
 */
public interface When<S, E, C> {

	/**
	 * Define action target be performed during transition
	 * @param action performed action
	 */
	void action(Action<S, E, C>... action);

}
