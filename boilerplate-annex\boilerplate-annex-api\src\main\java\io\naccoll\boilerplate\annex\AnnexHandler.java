package io.naccoll.boilerplate.annex;

import io.naccoll.boilerplate.annex.model.OssAnnexRefPo;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collection;

/**
 * 附件处理接口，用于处理附件的上传、存储和管理
 *
 * <AUTHOR>
 */
public interface AnnexHandler<T> {

	/**
	 * 获取处理类型
	 * @return 处理类型
	 */
	String getHandlerType();

	/**
	 * 根据目标ID查询目标数据
	 * @param targetId 目标ID
	 * @return 目标数据
	 */
	T findTarget(String targetId);

	/**
	 * 检查目标数据的访问权限
	 * @param targetIds 目标ID集合
	 * @return 是否有权限访问
	 */
	default boolean checkPermission(Collection<String> targetIds) {
		return true;
	}

	/**
	 * 上传前处理方法
	 * @param targetType 目标类型
	 * @param targetId 目标ID
	 * @param file 上传文件
	 */
	default void beforeUpdate(String targetType, String targetId, MultipartFile file) {

	}

	/**
	 * 上传后处理方法
	 * @param targetType 目标类型
	 * @param targetId 目标ID
	 * @param file 上传文件
	 */
	default void afterUpload(String targetType, String targetId, MultipartFile file) {

	}

	/**
	 * 保存前处理方法
	 * @param annex 附件引用信息
	 */
	default void beforeSave(OssAnnexRefPo annex) {

	}

	/**
	 * 保存后处理方法
	 * @param annex 附件引用信息
	 */
	default void afterSave(OssAnnexRefPo annex) {

	}

}
