package io.naccoll.boilerplate.core.security.annotations;

import io.naccoll.boilerplate.core.security.enums.SecurityDataEncodeAlgorithm;
import io.naccoll.boilerplate.core.security.enums.SecurityDataType;

import java.lang.annotation.*;

/**
 * 数据安全字段注解
 * <p>
 * 用于标记需要进行数据加密或脱敏处理的字段
 * </p>
 *
 * <AUTHOR>
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface SecurityDataField {

	/**
	 * 获取安全数据类型
	 * @return 安全数据类型
	 */
	SecurityDataType value();

	/**
	 * 获取安全数据编码算法
	 * @return 安全数据编码算法
	 */
	SecurityDataEncodeAlgorithm algorithm() default SecurityDataEncodeAlgorithm.NULL;

}
