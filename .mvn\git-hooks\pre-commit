#!/bin/sh
cached_filesnames=`git diff --diff-filter=d --name-only --cached`
./mvnw spring-javaformat:apply
for line in $cached_filesnames
do
   git add $line
done
./mvnw spring-javaformat:validate
format_validate_result=$?
echo $format_validate_result
if [ $format_validate_result -ne 0 ]
then
  echo "Format Validate Failed"
  exit 1
fi

#mvn clean package
#result=$?
#echo $result
#if [ $result -ne 0 ]
#then
#  echo "Build Failed"
#  exit 1
#fi
#
#echo "Build Success"
exit 0
