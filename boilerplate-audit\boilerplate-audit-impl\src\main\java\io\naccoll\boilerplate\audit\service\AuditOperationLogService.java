package io.naccoll.boilerplate.audit.service;

import io.naccoll.boilerplate.audit.dao.AuditOperationLogDao;
import io.naccoll.boilerplate.audit.model.AuditOperationLogPo;
import io.naccoll.boilerplate.core.audit.operate.AuditOperationLogCommand;
import io.naccoll.boilerplate.core.id.IdService;
import io.naccoll.boilerplate.core.utils.BeanUtils;
import io.naccoll.boilerplate.core.utils.JsonUtil;
import io.naccoll.boilerplate.sys.model.SysUserPo;
import io.naccoll.boilerplate.sys.service.SysUserQueryService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * 操作日志服务
 *
 * <AUTHOR>
 */
@Service
public class AuditOperationLogService {

	@Resource
	private AuditOperationLogDao auditOperationLogDao;

	@Resource
	private IdService idService;

	@Resource
	private SysUserQueryService sysUserQueryService;

	/**
	 * 添加操作日志
	 * @param command 操作日志命令
	 * @return 操作日志对象
	 */
	public AuditOperationLogPo add(AuditOperationLogCommand command) {
		AuditOperationLogPo auditOperationLog = new AuditOperationLogPo();
		BeanUtils.copyProperties(command, auditOperationLog);
		auditOperationLog.setId(idService.getId());
		if (!StringUtils.hasText(command.getName()) && command.getUserId() != null) {
			try {
				SysUserPo user = sysUserQueryService.findUserById(command.getUserId());
				if (user != null) {
					auditOperationLog.setName(user.getName());
				}
			}
			catch (Exception e) {
				// nothing
			}
		}
		if (command.getBeforeData() != null && command.getAfterData() != null) {
			String diffData = JsonUtil.createDiff(command.getBeforeData(), command.getAfterData());
			auditOperationLog.setDiffData(diffData);
		}
		return auditOperationLogDao.save(auditOperationLog);
	}

}
