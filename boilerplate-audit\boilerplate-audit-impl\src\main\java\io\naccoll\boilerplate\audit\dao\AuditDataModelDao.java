package io.naccoll.boilerplate.audit.dao;

import io.naccoll.boilerplate.audit.dto.AuditDataModelQueryCondition;
import io.naccoll.boilerplate.audit.model.AuditDataModelPo;
import io.naccoll.boilerplate.core.persistence.dao.BaseDao;
import io.naccoll.boilerplate.core.persistence.jpa.specification.Specifications;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import java.util.List;

/**
 * 数据审计模型数据库访问层
 *
 * <AUTHOR>
 */
@SuppressWarnings("AlibabaAbstractMethodOrInterfaceMethodMustUseJavadoc")
public interface AuditDataModelDao extends BaseDao<AuditDataModelPo, Long> {

	/**
	 * 根据模型签名查找第一个审计数据模型
	 * @param modelSign 模型签名
	 * @return 审计数据模型
	 */
	AuditDataModelPo findFirstByModelSign(String modelSign);

	/**
	 * 查询AuditDataModel列表
	 * @param condition AuditDataModel查询条件
	 * @return AuditDataModel列表
	 */
	default List<AuditDataModelPo> findAll(AuditDataModelQueryCondition condition) {
		Specification<AuditDataModelPo> spec = buildSpecification(condition);
		return findAll(spec);
	}

	/**
	 * 查询AuditDataModel分页
	 * @param condition AuditDataModel查询条件
	 * @param pageable 分页参数
	 * @return AuditDataModel分页
	 */
	default Page<AuditDataModelPo> page(AuditDataModelQueryCondition condition, Pageable pageable) {
		Specification<AuditDataModelPo> spec = buildSpecification(condition);
		return findAll(spec, pageable);
	}

	/**
	 * 构建查询条件
	 * @param condition 查询条件
	 * @return Specification查询条件
	 */
	default Specification<AuditDataModelPo> buildSpecification(AuditDataModelQueryCondition condition) {
		return Specifications.builder(AuditDataModelPo.class);
	}

}
