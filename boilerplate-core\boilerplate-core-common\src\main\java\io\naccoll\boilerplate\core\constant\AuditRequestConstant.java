package io.naccoll.boilerplate.core.constant;

/**
 * <AUTHOR>
 */
public class AuditRequestConstant {

	/**
	 * 请求相关的常量定义
	 */
	public static final String REQUEST = "Request";

	/**
	 * 响应相关的常量定义
	 */
	public static final String RESPONSE = "Response";

	/**
	 * 输入输出异常相关的常量定义
	 */
	public static final String IOEXCEPTION = "IOException";

	/**
	 * 私有构造方法，防止实例化
	 */
	private AuditRequestConstant() {
	}

}
