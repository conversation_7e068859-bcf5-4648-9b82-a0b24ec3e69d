package io.naccoll.boilerplate.cms.convert;

import io.naccoll.boilerplate.cms.dto.CmsArticleDto;
import io.naccoll.boilerplate.cms.dto.CmsColumnDto;
import io.naccoll.boilerplate.cms.model.CmsArticlePo;
import io.naccoll.boilerplate.cms.model.CmsColumnPo;
import io.naccoll.boilerplate.cms.service.CmsColumnQueryService;
import io.naccoll.boilerplate.core.utils.BeanUtils;
import io.naccoll.boilerplate.oss.OssServiceHelper;
import io.naccoll.boilerplate.sys.model.SysVariablePo;
import io.naccoll.boilerplate.sys.service.SysVariableQueryService;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * CMS数据转换类
 *
 * 该类负责将CMS相关的POJO对象转换为DTO对象，以及处理相关的数据转换逻辑
 *
 * <AUTHOR>
 */
@Component
public class CmsConvert {

	@Resource
	private CmsColumnQueryService cmsColumnQueryService;

	@Resource
	private OssServiceHelper ossServiceHelper;

	@Resource
	private SysVariableQueryService sysVariableQueryService;

	/**
	 * 将单个栏目POJO对象转换为DTO对象
	 * @param po 栏目POJO对象
	 * @return 转换后的栏目DTO对象
	 */
	public CmsColumnDto convertCmsColumnDto(CmsColumnPo po) {
		return convertCmsColumnDtoList(Collections.singletonList(po)).getFirst();
	}

	/**
	 * 将栏目POJO对象列表转换为DTO对象列表
	 * @param cmsColumnPos 栏目POJO对象列表
	 * @return 转换后的栏目DTO对象列表
	 */
	public List<CmsColumnDto> convertCmsColumnDtoList(List<CmsColumnPo> cmsColumnPos) {
		Map<String, String> varMap = sysVariableQueryService.listByTypeCode("cms-column-type", false)
			.stream()
			.collect(Collectors.toMap(SysVariablePo::getCode, SysVariablePo::getName, (a, b) -> b));
		return cmsColumnPos.stream().map(i -> {
			CmsColumnDto dto = new CmsColumnDto();
			BeanUtils.copyProperties(i, dto);
			if (StringUtils.hasText(i.getLogo())) {
				dto.setLogo(ossServiceHelper.parseUrl(dto.getLogo()));
			}
			if (i.getType() != null && varMap.containsKey(i.getType().toString())) {
				dto.setTypeName(varMap.get(i.getType().toString()));
			}
			return dto;
		}).toList();
	}

	/**
	 * 将栏目POJO对象列表转换为树形DTO对象列表
	 * @param cmsColumnPos 栏目POJO对象列表
	 * @return 转换后的树形DTO对象列表
	 */
	public List<CmsColumnDto> convertCmsColumnDtoTree(List<CmsColumnPo> cmsColumnPos) {
		List<CmsColumnDto> dtos = convertCmsColumnDtoList(cmsColumnPos);
		Map<Long, CmsColumnDto> map = dtos.stream().collect(Collectors.toMap(CmsColumnPo::getId, i -> i));
		for (CmsColumnDto dto : dtos) {
			if (dto.isLeaf() && map.containsKey(dto.getParentId())) {
				if (map.get(dto.getParentId()).getChildren() == null) {
					map.get(dto.getParentId()).setChildren(new LinkedList<>());
				}
				map.get(dto.getParentId()).getChildren().add(dto);
			}
		}
		return dtos.stream().filter(CmsColumnPo::isRoot).toList();
	}

	/**
	 * 将单个文章POJO对象转换为DTO对象
	 * @param po 文章POJO对象
	 * @return 转换后的文章DTO对象
	 */
	public CmsArticleDto convertCmsArticleDto(CmsArticlePo po) {
		return convertCmsArticleDtoList(Collections.singletonList(po)).getFirst();
	}

	/**
	 * 将文章POJO对象列表转换为DTO对象列表
	 * @param cmsColumnPos 文章POJO对象列表
	 * @return 转换后的文章DTO对象列表
	 */
	public List<CmsArticleDto> convertCmsArticleDtoList(List<CmsArticlePo> cmsColumnPos) {
		List<Long> columnIds = cmsColumnPos.stream().map(CmsArticlePo::getColumnId).toList();
		Map<Long, CmsColumnPo> map = cmsColumnQueryService.findMapByIds(columnIds);
		return cmsColumnPos.stream().map(i -> {
			CmsArticleDto dto = new CmsArticleDto();
			BeanUtils.copyProperties(i, dto);
			dto.setHeadImage(ossServiceHelper.parseUrl(dto.getHeadImage()));
			dto.setContent(ossServiceHelper.parseUrl(dto.getContent()));
			Optional.ofNullable(map.get(i.getColumnId())).ifPresent(column -> dto.setColumnName(column.getName()));
			return dto;
		}).toList();
	}

	/**
	 * 将文章POJO分页对象转换为DTO分页对象
	 * @param cmsColumnPages 文章POJO分页对象
	 * @return 转换后的文章DTO分页对象
	 */
	public Page<CmsArticleDto> convertCmsArticleDtoPage(Page<CmsArticlePo> cmsColumnPages) {
		return new PageImpl<>(convertCmsArticleDtoList(cmsColumnPages.getContent()), cmsColumnPages.getPageable(),
				cmsColumnPages.getTotalElements());
	}

}
