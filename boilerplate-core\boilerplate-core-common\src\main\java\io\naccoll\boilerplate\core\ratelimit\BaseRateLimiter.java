package io.naccoll.boilerplate.core.ratelimit;

import lombok.Getter;
import lombok.Setter;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 限流器基础抽象类 <br>
 * 提供限流器公共配置和工具方法，支持多种限流算法实现
 *
 * <AUTHOR>
 */
public abstract class BaseRateLimiter implements RateLimiter {

	/**
	 * 剩余请求数限制头名称
	 */
	public static final String REMAINING_HEADER = "X-RateLimit-Remaining";

	/**
	 * 令牌补充速率头名称
	 */
	public static final String REPLENISH_RATE_HEADER = "X-RateLimit-Replenish-Rate";

	/**
	 * 突发容量头名称
	 */
	public static final String BURST_CAPACITY_HEADER = "X-RateLimit-Burst-Capacity";

	/**
	 * 请求令牌数头名称
	 */
	public static final String REQUESTED_TOKENS_HEADER = "X-RateLimit-Requested-Tokens";

	/**
	 * 默认限流配置
	 */
	protected final RateLimiterProperties defaultConfig;

	/**
	 * 剩余请求数响应头名称
	 */
	@Setter
	@Getter
	private String remainingHeader = REMAINING_HEADER;

	/**
	 * 令牌补充速率响应头名称
	 */
	@Setter
	@Getter
	private String replenishRateHeader = REPLENISH_RATE_HEADER;

	/**
	 * 突发容量响应头名称
	 */
	@Getter
	@Setter
	private String burstCapacityHeader = BURST_CAPACITY_HEADER;

	/**
	 * 请求令牌数响应头名称
	 */
	@Getter
	@Setter
	private String requestedTokensHeader = REQUESTED_TOKENS_HEADER;

	/**
	 * 是否包含限流信息响应头
	 */
	@Setter
	@Getter
	private boolean includeHeaders = true;

	/**
	 * 构造方法
	 * @param defaultConfig 默认限流配置
	 */
	protected BaseRateLimiter(RateLimiterProperties defaultConfig) {
		this.defaultConfig = defaultConfig;
	}

	/**
	 * 生成Redis存储键 <br>
	 * 使用Redis hash tag保证集群环境下相同ID的键分布在相同节点
	 * @param id 限流器ID
	 * @return 包含令牌数和时间戳的两个键
	 */
	static List<String> getKeys(String id) {
		String prefix = "rate_limiter.{" + id;
		String tokenKey = prefix + "}.tokens";
		String timestampKey = prefix + "}.timestamp";
		return Arrays.asList(tokenKey, timestampKey);
	}

	/**
	 * 构建限流响应头
	 * @param config 限流配置
	 * @param tokensLeft 剩余令牌数
	 * @return 包含限流信息的响应头
	 */
	public Map<String, String> getHeaders(RateLimiterProperties config, Long tokensLeft) {
		Map<String, String> headers = HashMap.newHashMap(5);
		if (isIncludeHeaders()) {
			headers.put(this.remainingHeader, tokensLeft.toString());
			headers.put(this.replenishRateHeader, String.valueOf(config.getRate()));
			headers.put(this.burstCapacityHeader, String.valueOf(config.getCapacity()));
			headers.put(this.requestedTokensHeader, String.valueOf(1));
		}
		return headers;
	}

	/**
	 * 获取路由配置 <br>
	 * 优先使用自定义配置，未配置时使用默认配置
	 * @param properties 自定义限流配置
	 * @return 实际生效的限流配置
	 */
	protected RateLimiterProperties getRouteConfig(RateLimiterProperties properties) {
		return properties != null ? properties : defaultConfig;
	}

}
