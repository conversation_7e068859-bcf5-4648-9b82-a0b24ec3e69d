package io.naccoll.boilerplate.cms.constant;

/**
 * CMS缓存名称常量类
 *
 * 该类定义了CMS模块中所有缓存相关的常量名称，用于统一管理缓存键
 *
 * <AUTHOR>
 */
public class CmsCacheName {

	/**
	 * 文章缓存的唯一标识
	 */
	public static final String ARTICLE_ID = "cms:article:id";

	/**
	 * 栏目缓存的唯一标识
	 */
	public static final String COLUMN_ID = "cms:column:id";

	/**
	 * 所有栏目缓存的唯一标识
	 */
	public static final String COLUMN_ALL = "cms:column:all";

	private CmsCacheName() {
	}

}
